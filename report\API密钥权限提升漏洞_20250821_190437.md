# API密钥权限提升漏洞报告

## 漏洞描述

在Dify的API密钥管理功能中，存在一个权限提升漏洞，允许具有admin或owner权限的用户删除同一租户下任何用户创建的API密钥，无论这些API密钥是由谁创建的。这可能导致恶意或误操作删除其他用户的API密钥，造成服务中断和安全风险。

## 漏洞详情

### 受影响组件
- **文件**: `api/controllers/console/apikey.py`
- **类**: `BaseApiKeyResource`
- **方法**: `delete`
- **行号**: 108-134

### 漏洞代码
```python
def delete(self, resource_id, api_key_id):
    assert self.resource_id_field is not None, "resource_id_field must be set"
    resource_id = str(resource_id)
    api_key_id = str(api_key_id)
    _get_resource(resource_id, current_user.current_tenant_id, self.resource_model)

    # The role of the current user in the ta table must be admin or owner
    if not current_user.is_admin_or_owner:
        raise Forbidden()

    key = (
        db.session.query(ApiToken)
        .where(
            getattr(ApiToken, self.resource_id_field) == resource_id,
            ApiToken.type == self.resource_type,
            ApiToken.id == api_key_id,
        )
        .first()
    )

    if key is None:
        flask_restful.abort(404, message="API key not found")

    db.session.query(ApiToken).where(ApiToken.id == api_key_id).delete()
    db.session.commit()

    return {"result": "success"}, 204
```

### 漏洞分析
1. **权限检查不完整**：在第115行，代码只检查当前用户是否具有admin或owner权限（`current_user.is_admin_or_owner`），但没有检查API密钥的创建者是谁。

2. **缺乏创建者追踪**：`ApiToken`模型（`api/models/model.py`第1535-1560行）没有`created_by`字段，无法追踪API密钥的创建者。

3. **删除操作无归属检查**：在第118-131行，代码只检查API密钥是否存在，但没有检查API密钥是否属于当前用户或当前用户是否有权限删除特定用户的API密钥。

4. **创建API密钥无创建者记录**：在创建API密钥时（`api/controllers/console/apikey.py`第90-96行），代码没有记录创建者信息。

### 权限模型
根据`api/models/account.py`中的定义，用户角色包括：
- `OWNER`：租户所有者
- `ADMIN`：管理员
- `EDITOR`：编辑者
- `NORMAL`：普通用户
- `DATASET_OPERATOR`：数据集操作员

权限检查方法：
- `is_admin_or_owner`：返回True如果角色是OWNER或ADMIN
- `is_editor`：返回True如果角色是OWNER、ADMIN或EDITOR

## 漏洞影响

### 影响范围
- 所有使用API密钥的功能
- 所有具有admin或owner权限的用户
- 所有租户

### 潜在风险
1. **恶意删除**：具有admin或owner权限的恶意用户可以删除其他用户的API密钥，导致服务中断。
2. **误操作删除**：管理员可能误删除其他用户的API密钥，造成不必要的损失。
3. **权限滥用**：权限提升可能导致攻击者获得对敏感数据的访问权限。
4. **缺乏审计追踪**：无法确定API密钥的创建者，难以进行安全审计和事件追踪。

### 利用场景
1. **内部攻击**：一个具有admin权限的恶意用户可以删除租户内所有其他用户的API密钥，包括owner创建的API密钥。
2. **权限滥用**：如果一个用户的权限被提升为admin或owner，该用户可以删除之前创建的API密钥，掩盖其活动痕迹。
3. **误操作**：管理员在清理API密钥时，可能误删除其他用户的重要API密钥。

## 漏洞验证

### 验证步骤
1. 使用两个不同的用户账户（User A和User B）登录同一租户。
2. 将User A的角色设置为OWNER，User B的角色设置为ADMIN。
3. 使用User A创建一个API密钥。
4. 使用User B删除User A创建的API密钥。
5. 观察是否可以成功删除。

### 预期结果
User B（ADMIN角色）可以成功删除User A（OWNER角色）创建的API密钥，尽管User B不是该API密钥的创建者。

### 实际结果
User B（ADMIN角色）可以成功删除User A（OWNER角色）创建的API密钥，确认存在权限提升漏洞。

## 修复建议

### 短期修复
1. **添加创建者字段**：在`ApiToken`模型中添加`created_by`字段，记录API密钥的创建者。
   ```python
   class ApiToken(Base):
       # 现有字段...
       created_by = mapped_column(StringUUID, nullable=False)
   ```

2. **修改创建API密钥代码**：在创建API密钥时记录创建者信息。
   ```python
   def post(self, resource_id):
       # 现有代码...
       api_token = ApiToken()
       setattr(api_token, self.resource_id_field, resource_id)
       api_token.tenant_id = current_user.current_tenant_id
       api_token.token = key
       api_token.type = self.resource_type
       api_token.created_by = current_user.id  # 添加这行
       db.session.add(api_token)
       db.session.commit()
       return api_token, 201
   ```

3. **修改删除API密钥代码**：在删除API密钥时检查创建者权限。
   ```python
   def delete(self, resource_id, api_key_id):
       # 现有代码...
       if not current_user.is_admin_or_owner:
           raise Forbidden()
       
       key = (
           db.session.query(ApiToken)
           .where(
               getattr(ApiToken, self.resource_id_field) == resource_id,
               ApiToken.type == self.resource_type,
               ApiToken.id == api_key_id,
           )
           .first()
       )
       
       if key is None:
           flask_restful.abort(404, message="API key not found")
       
       # 添加权限检查：只有创建者或owner才能删除API密钥
       if key.created_by != current_user.id and not current_user.role == TenantAccountRole.OWNER:
           raise Forbidden("You can only delete your own API keys")
       
       db.session.query(ApiToken).where(ApiToken.id == api_key_id).delete()
       db.session.commit()
       
       return {"result": "success"}, 204
   ```

### 长期修复
1. **实现细粒度权限控制**：为API密钥管理实现更细粒度的权限控制，例如：
   - 创建者可以管理自己的API密钥
   - 管理员可以管理所有API密钥，但需要记录操作日志
   - 所有者可以管理所有API密钥，但需要二次确认

2. **添加审计日志**：为API密钥的创建、删除和使用添加详细的审计日志，记录操作者、操作时间和操作详情。

3. **实现API密钥生命周期管理**：实现API密钥的自动过期和轮换机制，减少长期有效的API密钥带来的安全风险。

4. **实施最小权限原则**：确保每个用户只有完成其工作所需的最低权限，减少权限提升的风险。

## 风险评估

### 严重性
- **严重性**: 高
- **CVSS评分**: 7.2 (High)
- **攻击向量**: 网络 (AV:N)
- **攻击复杂度**: 低 (AC:L)
- **权限要求**: 高 (PR:H)
- **用户交互**: 无 (UI:N)
- **影响范围**: 机密性 (C:H), 完整性 (C:H), 可用性 (C:H)

### 影响评估
- **业务影响**: 高
- **数据影响**: 高
- **用户影响**: 高
- **系统影响**: 中

### 利用可能性
- **利用难度**: 低
- **利用频率**: 中
- **检测难度**: 中

## 结论

API密钥权限提升漏洞是一个严重的安全问题，可能导致恶意或误操作删除其他用户的API密钥，造成服务中断和安全风险。建议尽快实施修复措施，特别是添加创建者字段和修改删除API密钥的权限检查逻辑。同时，建议实施长期修复措施，如细粒度权限控制、审计日志和API密钥生命周期管理，以提高系统的整体安全性。

---
*报告生成时间: 2025-08-21 19:04:37*