# tools/history_tools.py

from langchain_core.tools import tool
from pydantic import BaseModel, Field
import logging

class HistorySearchInput(BaseModel):
    query: str = Field(description="需要检索的历史记录相关内容描述")
    max_results: int = Field(default=2, description="返回的最大结果数量")
    use_reranker: bool = Field(default=True, description="是否使用重排序模型进行精确排序")
    similarity_threshold: float = Field(default=1, description="去重时的相似度阈值，0-1之间")

# 全局历史管理器实例（将在初始化时设置）
_history_manager = None

def set_history_manager(history_manager):
    """设置全局历史管理器实例"""
    global _history_manager
    _history_manager = history_manager

@tool(args_schema=HistorySearchInput)
async def search_history(query: str, max_results: int = 2, use_reranker: bool = True,
                        similarity_threshold: float = 1) -> str:
    """
    搜索和检索相关的历史对话记录。

    这个工具可以快速找到与当前任务相关的历史对话记录，
    类似于代码检索工具可以找到相关代码片段一样。
    自动进行智能去重，避免返回重复或高度相似的历史记录。

    使用方法：
    - 提供需要检索的内容描述
    - 可选指定返回结果数量
    - 可选是否使用重排序模型优化结果
    - 可选设置相似度阈值

    Args:
        query: 需要检索的历史记录相关内容描述
        max_results: 返回的最大结果数量（默认2条）
        use_reranker: 是否使用重排序模型进行精确排序（默认启用）
        similarity_threshold: 去重时的相似度阈值，0-1之间（默认1）

    Returns:
        相关历史记录的字符串格式内容
    """
    if _history_manager is None:
        return "❌ 历史管理器未初始化，无法进行检索"

    try:
        logging.info(f"🔍 智能体正在搜索历史记录: '{query[:50]}...'")

        # 使用增强的检索方法，包含重排序和强制去重功能
        relevant_history = await _history_manager.search_relevant_history(
            query=query,
            k=max_results,
            fetch_k=max(20, max_results * 2),  # 获取更多候选文档供重排序选择
            use_reranker=use_reranker,
            similarity_threshold=similarity_threshold
        )

        if relevant_history:
            return f"📚 找到相关历史记录：\n\n{relevant_history}"
        else:
            return "📭 未找到相关的历史记录"

    except Exception as e:
        logging.error(f"❌ 搜索历史记录时发生错误: {e}")
        return f"❌ 搜索历史记录时发生错误: {str(e)}"