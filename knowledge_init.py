#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库初始化脚本

这个脚本用于初始化知识库管理器，并将其设置到知识库查询工具中。
在主程序启动时调用此脚本即可启用知识库功能。
"""

import logging
import json
import os
from knowledge_manager import KnowledgeManager
from tools.knowledge_tools import set_knowledge_manager

def init_knowledge_base(config_file: str = "knowledge_config.json") -> bool:
    """
    初始化知识库

    Args:
        config_file: 配置文件路径

    Returns:
        bool: 初始化是否成功
    """
    try:
        # 加载配置文件
        if not os.path.exists(config_file):
            logging.error(f"❌ 配置文件不存在: {config_file}")
            return False

        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # 验证必需的配置项
        required_fields = [
            "embedding.api_key",
            "embedding.base_url",
            "embedding.model_name",
            "reranker.model",
            "reranker.base_url",
            "reranker.api_key"
        ]

        for field in required_fields:
            parts = field.split('.')
            current = config
            for part in parts:
                if part not in current:
                    logging.error(f"❌ 配置文件缺少必需字段: {field}")
                    return False
                current = current[part]

            if not current or current == "your_api_key_here" or current == "your_reranker_api_key":
                logging.error(f"❌ 配置字段 {field} 未正确设置")
                return False

        # 创建知识库管理器
        knowledge_manager = KnowledgeManager(
            api_key=config["embedding"]["api_key"],
            base_url=config["embedding"]["base_url"],
            model_name=config["embedding"]["model_name"],
            reranker_model=config["reranker"]["model"],
            reranker_base_url=config["reranker"]["base_url"],
            reranker_api_key=config["reranker"]["api_key"],
            persist_directory=config["database"]["persist_directory"],
            chunk_size=config["text_splitter"]["chunk_size"],
            chunk_overlap=config["text_splitter"]["chunk_overlap"]
        )

        # 设置到知识库工具中
        set_knowledge_manager(knowledge_manager)

        logging.info("✅ 知识库初始化成功")
        logging.info(f"📁 数据库路径: {config['database']['persist_directory']}")
        logging.info(f"🧠 嵌入模型: {config['embedding']['model_name']}")
        logging.info(f"🔄 重排序模型: {config['reranker']['model']}")

        return True

    except Exception as e:
        logging.error(f"❌ 知识库初始化失败: {e}")
        return False

def get_knowledge_stats() -> dict:
    """
    获取知识库统计信息

    Returns:
        dict: 统计信息
    """
    try:
        # 这里需要访问知识库管理器实例
        # 由于知识库管理器是全局变量，我们需要一个方法来获取它
        # 为了简单起见，这里返回一个基本的统计信息
        config_file = "knowledge_config.json"
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            db_path = config["database"]["persist_directory"]
            if os.path.exists(db_path):
                # 计算数据库文件大小
                total_size = 0
                for dirpath, dirnames, filenames in os.walk(db_path):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        total_size += os.path.getsize(filepath)

                return {
                    "database_size_mb": round(total_size / (1024 * 1024), 2),
                    "database_path": db_path
                }

        return {"status": "not_initialized"}

    except Exception as e:
        logging.error(f"❌ 获取知识库统计信息失败: {e}")
        return {"error": str(e)}

if __name__ == "__main__":
    # 测试初始化
    print("🧪 测试知识库初始化...")
    success = init_knowledge_base()

    if success:
        print("✅ 知识库初始化测试成功")
        stats = get_knowledge_stats()
        print(f"📊 统计信息: {stats}")
    else:
        print("❌ 知识库初始化测试失败")