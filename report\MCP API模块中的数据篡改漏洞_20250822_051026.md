# MCP API模块中的数据篡改漏洞

## 漏洞概述

MCP API模块中存在严重的数据篡改漏洞，主要集中在`_convert_input_form_to_parameters`方法和`invoke_tool`方法中的参数转换和数据处理过程。攻击者可以通过构造恶意的输入参数，在经过不充分的输入清理后，被传递到模板渲染方法时，导致模板注入攻击，进而可能导致远程代码执行、信息泄露等严重安全问题。

## 漏洞详情

### 1. 不充分的输入清理

**位置**: `api/core/app/apps/base_app_generator.py:150-153`

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

**问题描述**: `_sanitize_value`方法仅移除字符串中的空字符(`\x00`)，没有对其他可能引起模板注入的特殊字符进行清理或转义。这意味着攻击者可以通过构造包含模板表达式的输入，绕过这个简单的清理过程。

### 2. 不安全的模板渲染

**位置**: `api/core/prompt/utils/prompt_template_parser.py:32-42`

```python
def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
    def replacer(match):
        key = match.group(1)
        value = inputs.get(key, match.group(0))  # return original matched string if key not found

        if remove_template_variables and isinstance(value, str):
            return PromptTemplateParser.remove_template_variables(value, self.with_variable_tmpl)
        return value

    prompt = re.sub(self.regex, replacer, self.template)
    return re.sub(r"<\|.*?\|>", "", prompt)
```

**问题描述**: `PromptTemplateParser.format`方法使用正则表达式替换模板中的变量，但没有对输入值进行任何安全处理。如果输入值包含模板表达式，它们将被直接插入到最终的提示中。

### 3. 不安全的Jinja2模板渲染

**位置**: `api/core/helper/code_executor/jinja2/jinja2_transformer.py:18-23`

```python
def get_runner_script(cls) -> str:
    runner_script = dedent(f"""
        # declare main function
        def main(**inputs):
            import jinja2
            template = jinja2.Template('''{cls._code_placeholder}''')
            return template.render(**inputs)
        ...
    """)
    return runner_script
```

**问题描述**: `Jinja2TemplateTransformer`类直接使用`jinja2.Template`创建模板，并使用`template.render(**inputs)`来渲染模板，没有对输入进行任何安全处理。Jinja2模板引擎允许执行任意Python代码，这可能导致严重的代码注入漏洞。

### 4. 不安全的提示模板格式化

**位置**: `api/core/prompt/simple_prompt_transform.py:116`

```python
prompt = prompt_template.format(variables)
```

**问题描述**: `SimplePromptTransform`类在第116行使用`prompt_template.format(variables)`来格式化提示模板，这可能导致模板注入漏洞。Python的字符串格式化方法不会对特殊字符进行转义，如果输入值包含格式化字符串，它们将被直接插入到最终的提示中。

### 5. 不安全的高级提示模板格式化

**位置**: `api/core/prompt/advanced_prompt_transform.py:119,124`

```python
# 第119行
prompt = parser.format(prompt_inputs)

# 第124行
prompt = Jinja2Formatter.format(prompt, prompt_inputs)
```

**问题描述**: `AdvancedPromptTransform`类在第119行和第124行分别调用了`PromptTemplateParser.format`方法和`Jinja2Formatter.format`方法来格式化模板，这可能导致模板注入漏洞。

## 数据流路径

以下是MCP API模块中数据篡改的完整数据流路径：

1. **请求入口点**: `MCPAppApi.post`方法接收MCP请求
2. **参数解析**: 使用`reqparse.RequestParser`解析JSON-RPC请求参数
3. **请求处理**: 创建`MCPServerStreamableHTTPRequestHandler`实例并调用其`handle`方法
4. **工具调用**: `handle`方法根据请求类型调用`invoke_tool`方法
5. **参数处理**: `invoke_tool`方法处理工具调用参数，并将其转换为应用生成所需的格式
6. **参数验证**: `BaseAppGenerator._prepare_user_inputs`方法验证和清理用户输入
7. **参数清理**: `BaseAppGenerator._sanitize_value`方法清理用户输入（仅移除空字符）
8. **应用生成**: `AppGenerateService.generate`方法根据应用模式调用相应的生成器
9. **模板渲染**: 在`MessageBasedAppGenerator._get_conversation_introduction`方法或`advanced_prompt_transform.py`中，用户输入被传递到模板渲染方法

## 攻击向量

### 1. 针对PromptTemplateParser.format方法的攻击

攻击者可以通过构造包含模板表达式的输入，绕过`_sanitize_value`方法的简单清理，然后在`PromptTemplateParser.format`方法中被执行。

**PoC示例**:
```python
# 构造恶意输入
malicious_input = "{{7*7}}"

# 经过_sanitize_value方法处理后
sanitized_input = malicious_input.replace("\x00", "")  # 仍然是"{{7*7}}"

# 在PromptTemplateParser.format方法中被处理
template = "Hello, {{name}}!"
inputs = {"name": sanitized_input}
result = PromptTemplateParser(template).format(inputs)  # 结果为"Hello, 49!"
```

### 2. 针对Jinja2Formatter.format方法的攻击

攻击者可以通过构造包含Jinja2模板表达式的输入，绕过`_sanitize_value`方法的简单清理，然后在`Jinja2Formatter.format`方法中被执行，可能导致远程代码执行。

**PoC示例**:
```python
# 构造恶意输入
malicious_input = "{{ ''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate()[0].strip() }}"

# 经过_sanitize_value方法处理后
sanitized_input = malicious_input.replace("\x00", "")  # 仍然是恶意输入

# 在Jinja2Formatter.format方法中被处理
template = "Result: {{input}}"
inputs = {"input": sanitized_input}
result = Jinja2Formatter.format(template, inputs)  # 可能执行系统命令"ls"
```

### 3. 针对SimplePromptTransform类的攻击

攻击者可以通过构造包含格式化字符串的输入，绕过`_sanitize_value`方法的简单清理，然后在`SimplePromptTransform`类中被执行。

**PoC示例**:
```python
# 构造恶意输入
malicious_input = "{name.__class__}"

# 经过_sanitize_value方法处理后
sanitized_input = malicious_input.replace("\x00", "")  # 仍然是"{name.__class__}"

# 在SimplePromptTransform类中被处理
prompt_template = "Hello, {name}!"
variables = {"name": sanitized_input}
result = prompt_template.format(**variables)  # 可能泄露敏感信息
```

## 影响范围

- **远程代码执行**: 攻击者可以执行任意Python代码，完全控制服务器。
- **信息泄露**: 攻击者可以获取敏感信息，如数据库凭证、API密钥等。
- **拒绝服务**: 攻击者可以通过构造恶意输入导致服务不可用。
- **权限提升**: 攻击者可以利用此漏洞获取更高的权限。

## 严重性评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 修复建议

### 短期修复措施

1. **增强`_sanitize_value`方法**:
   ```python
   def _sanitize_value(self, value: Any) -> Any:
       if isinstance(value, str):
           # 移除空字符
           value = value.replace("\x00", "")
           # 转义模板表达式
           value = value.replace("{{", "&lbrace;&lbrace;").replace("}}", "&rbrace;&rbrace;")
           value = value.replace("{", "&lbrace;").replace("}", "&rbrace;")
           value = value.replace("%", "&percnt;")
           return value
       return value
   ```

2. **使用安全的模板引擎**:
   - 对于`PromptTemplateParser.format`方法，使用安全的模板渲染方式，如`string.Template`或自定义的安全模板引擎。
   - 对于`Jinja2Formatter.format`方法，使用Jinja2的沙箱环境，限制可用的函数和属性。

3. **实现输入验证**:
   - 对所有用户输入进行严格的类型和格式验证。
   - 使用白名单验证，而不是黑名单过滤。

### 长期修复措施

1. **实现模板沙箱**:
   - 为Jinja2模板引擎实现沙箱环境，限制可用的函数和属性。
   - 禁用危险的Jinja2特性，如`{% %}`语句块。

2. **使用安全的模板语法**:
   - 考虑使用更安全的模板语法，如Mustache或Handlebars。
   - 实现自定义的模板渲染器，确保所有用户输入都被正确转义。

3. **实现内容安全策略（CSP）**:
   - 在Web界面中实现CSP，限制可以执行的脚本和样式。
   - 使用CSP限制外部资源的加载，防止XSS攻击。

4. **增强日志记录和监控**:
   - 记录所有模板渲染操作，包括输入和输出。
   - 实现实时监控，检测异常的模板渲染行为。

5. **定期安全审计**:
   - 定期对代码进行安全审计，特别是与用户输入处理相关的部分。
   - 进行渗透测试，验证修复措施的有效性。

## 结论

MCP API模块中存在严重的数据篡改漏洞，主要集中在`_convert_input_form_to_parameters`方法和`invoke_tool`方法中的参数转换和数据处理过程。攻击者可以通过构造恶意的输入参数，在经过不充分的输入清理后，被传递到模板渲染方法时，导致模板注入攻击，进而可能导致远程代码执行、信息泄露等严重安全问题。

建议立即采取修复措施，特别是增强`_sanitize_value`方法，使用安全的模板引擎，并实现输入验证。长期来看，应考虑实现模板沙箱，使用安全的模板语法，并增强日志记录和监控。

---
*报告生成时间: 2025-08-22 05:10:26*