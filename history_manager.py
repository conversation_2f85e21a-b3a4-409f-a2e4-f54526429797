# history_manager.py

import logging
from typing import List, Dict, Any
import uuid
import httpx

# --- LangChain & VectorStore 核心库 ---
from langchain_core.messages import AIMessage, ToolMessage
from langchain_text_splitters import RecursiveCharacterTextSplitter
from chromadb.config import Settings
from langchain_chroma import Chroma
from langchain_openai import OpenAIEmbeddings

class VectorizedHistoryManager:
    """
    一个用于管理和检索 Agent 对话历史的向量化记忆模块。

    该模块将每一次成功的 Agent 交互（包括思考、工具调用和结果）
    作为一个独立的文档进行向量化，并存储在 ChromaDB 中。
    在开始新任务时，可以根据任务描述检索最相关的历史记录，
    为 Agent 提供过往的成功经验作为参考。
    """
    def __init__(self, api_key: str, base_url: str, model_name: str, reranker_model: str, reranker_base_url: str, reranker_api_key: str,
                 persist_directory: str = None, embedding_model=None, chunk_size=7000, chunk_overlap=200):
        """
        初始化向量化历史管理器。

        Args:
            api_key (str): 用于嵌入模型的 API key。
            base_url (str): 用于嵌入模型的 base URL。
            model_name (str): 要使用的嵌入模型的具体名称。
            persist_directory (str, optional): 用于持久化存储向量数据库的目录。如果为 None，则使用内存模式。
            embedding_model: 可选，用于文本嵌入的预构建模型实例。如果为 None，则使用提供的凭证创建。
            chunk_size (int): 文本分割的块大小。
            chunk_overlap (int): 文本分割的重叠大小。
            reranker_model (str): 重排序模型名称。
            reranker_base_url (str): 重排序模型的API地址。
            reranker_api_key (str): 重排序模型的API密钥。
        """
        if embedding_model:
            self.embedding_model = embedding_model
        else:
            self.embedding_model = OpenAIEmbeddings(
                openai_api_key=api_key,
                openai_api_base=base_url,
                model=model_name
            )
        
        self.text_splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)

        # 重排序模型配置
        self.reranker_model = reranker_model
        self.reranker_base_url = reranker_base_url
        self.reranker_api_key = reranker_api_key
        
        collection_name = "agent_history"
        
        # --- 禁用 ChromaDB 的匿名遥测 ---
        client_settings = Settings(anonymized_telemetry=False)

        if persist_directory:
            # 使用持久化目录和自定义设置初始化 Chroma
            self.vectorstore = Chroma(
                collection_name=collection_name,
                embedding_function=self.embedding_model,
                persist_directory=persist_directory,
                client_settings=client_settings
            )
            logging.info(f"✅ 向量化历史管理器初始化成功，数据将持久化到 '{persist_directory}'")
        else:
            # 在内存中初始化 Chroma，并禁用遥测
            self.vectorstore = Chroma(
                collection_name=collection_name,
                embedding_function=self.embedding_model,
                client_settings=client_settings
            )
            logging.info("✅ 向量化历史管理器初始化成功，使用内存 ChromaDB (遥测已禁用)。")

    def _format_interaction_to_text(self, interaction_chunks: List[Dict[str, Any]]) -> str:
        """
        将 LangGraph 返回的交互数据块列表格式化为一段连贯的文本。
        
        Args:
            interaction_chunks: 从 astream 返回的原始数据块列表。

        Returns:
            一段描述了整个交互流程的字符串。
        """
        formatted_steps = []
        for chunk in interaction_chunks:
            if "agent" in chunk and "messages" in chunk["agent"]:
                for msg in chunk["agent"]["messages"]:
                    if isinstance(msg, AIMessage):
                        if msg.content:
                            formatted_steps.append(f"【AI思考/回应】: {msg.content}")
                        if msg.tool_calls:
                            for tc in msg.tool_calls:
                                formatted_steps.append(f"【工具调用】: 调用 `{tc['name']}`，参数: {tc['args']}")
            
            elif "tools" in chunk and "messages" in chunk["tools"]:
                for output in chunk["tools"]["messages"]:
                    # output 是一个 ToolMessage 对象, 它有 .content 和 .name 属性
                    # 我们需要正确地提取这些属性
                    tool_name = getattr(output, 'name', 'unknown_tool')
                    content = getattr(output, 'content', 'N/A')
                    formatted_steps.append(f"【工具结果 - {tool_name}】:\n{content}\n")

        return "\n".join(formatted_steps)

    async def add_history(self, interaction_chunks: List[Dict[str, Any]]):
        """
        将一次完整的、成功的交互添加到向量数据库中。

        Args:
            interaction_chunks: 从 astream 返回的原始数据块列表。
        """
        if not interaction_chunks:
            return

        # 1. 将交互格式化为文本
        full_interaction_text = interaction_chunks
        if not full_interaction_text.strip():
            logging.warning("⚠️ 尝试添加空的交互历史，已跳过。")
            return

        # 2. 分割文本
        documents = self.text_splitter.create_documents([full_interaction_text])
        
        # 3. 将分割后的文档分批添加到向量存储，以避免超出API的batch size限制
        if documents:
            batch_size = 32 # 设置一个安全的批处理大小，远小于API限制的64
            for i in range(0, len(documents), batch_size):
                batch = documents[i:i + batch_size]
                await self.vectorstore.aadd_documents(batch)
                logging.info(f"📚 正在分批添加历史记录... 已处理 {i + len(batch)} / {len(documents)} 个片段。")
            logging.info(f"✅ 交互历史已全部分批添加完成（共 {len(documents)} 个片段）。")

    async def search_relevant_history(self, query: str, k: int = 3, fetch_k: int = 20, use_reranker: bool = True,
                                       similarity_threshold: float = 0.85, relevance_threshold: float = 0.3) -> str:
        """
        根据查询语句，从历史记录中检索最相关的 k 条记录。
        先使用MMR检索获取候选文档，然后应用相关性阈值过滤丢弃无关记录，
        可选使用重排序模型进行精确排序，最后强制进行去重处理以移除相似内容。

        Args:
            query (str): 用于检索的查询字符串（通常是新任务的描述）。
            k (int): 最终要返回的多样化文档的数量。
            fetch_k (int): 初始获取的文档数量，以供MMR算法选择。
            use_reranker (bool): 是否使用重排序模型进行二次排序。
            similarity_threshold (float): 去重时的相似度阈值，0-1之间。
            relevance_threshold (float): 相关性阈值，低于此值的记录将被丢弃，0-1之间。

        Returns:
            一个包含最相关且多样化的历史记录的字符串，如果没有找到则返回空字符串。
        """
        if not query:
            return ""

        try:
            # 第一步：使用 MMR 进行初步检索，以增强结果的多样性
            retriever = self.vectorstore.as_retriever(
                search_type="mmr",
                search_kwargs={'k': fetch_k, 'fetch_k': fetch_k}
            )
            initial_results = await retriever.ainvoke(query)

            if not initial_results:
                return ""

            # 第二步：应用相关性阈值过滤，丢弃与当前任务无关的记录
            filtered_results = await self.filter_by_relevance(query, initial_results, relevance_threshold)

            if not filtered_results:
                logging.info(f"⚠️ 所有候选文档的相关性得分均低于阈值 {relevance_threshold}，无相关历史记录")
                return ""

            # 第三步：使用重排序模型进行精确排序
            final_results = filtered_results
            if use_reranker and len(filtered_results) > k:
                logging.info(f"🔄 正在对 {len(filtered_results)} 个相关文档进行重排序...")
                final_results = await self.rerank_documents(query, filtered_results, k)
            else:
                # 如果不使用重排序或候选文档数量不大于k，直接取前k个
                final_results = filtered_results[:k]

            # 第三步：强制去重处理
            if len(final_results) > 1:
                logging.info(f"🔍 正在对 {len(final_results)} 个文档进行去重处理...")
                final_results = self.deduplicate_documents(final_results, similarity_threshold)

            # 将检索到的文档内容格式化为单一字符串
            formatted_history = "\n\n---\n\n".join([doc.page_content for doc in final_results])

            # --- 新增：为每条找到的历史记录打印详细日志 ---
            log_message = f"🔍 根据查询 '{query[:50]}...' 找到了 {len(final_results)} 条相关历史记录。"
            log_message += f" (相关性阈值过滤: {len(initial_results)} → {len(filtered_results)})"
            if use_reranker and len(filtered_results) > k:
                log_message += f" (通过重排序从 {len(filtered_results)} 个候选中选出)"
            if len(final_results) < len(filtered_results[:k]):
                log_message += f" (去重后保留 {len(final_results)} 条)"
            log_message += " 内容摘要如下:"
            for i, doc in enumerate(final_results):
                # 提取每条记录的前100个字符作为摘要
                summary = doc.page_content.strip().replace('\n', ' ')[:100]
                log_message += f"\n  {i+1}. {summary}..."
            logging.info(log_message)

            return formatted_history

        except Exception as e:
            logging.error(f"❌ 在检索历史记录时发生错误: {e}")
            return ""

    async def rerank_documents(self, query: str, documents: List[Any], top_k: int = 3) -> List[Any]:
        """
        使用重排序模型对文档进行重排序。

        Args:
            query (str): 查询字符串。
            documents: 要重排序的文档列表。
            top_k (int): 返回的文档数量。

        Returns:
            重排序后的文档列表。
        """
        if not documents or len(documents) <= top_k:
            return documents[:top_k] if documents else []

        try:
            # 准备请求数据
            request_data = {
                "model": self.reranker_model,
                "query": query,
                "documents": [doc.page_content for doc in documents]
            }

            headers = {
                "Authorization": f"Bearer {self.reranker_api_key}",
                "Content-Type": "application/json"
            }

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    self.reranker_base_url,
                    headers=headers,
                    json=request_data
                )
                response.raise_for_status()
                result = response.json()

                # 根据重排序结果重新排列文档
                if "results" in result:
                    # 获取排序后的索引
                    sorted_indices = [item.get("index", i) for i, item in enumerate(result["results"])]
                    # 根据得分排序（得分高的在前）
                    sorted_indices.sort(key=lambda i: result["results"][i].get("score", 0), reverse=True)

                    # 取前top_k个
                    top_indices = sorted_indices[:top_k]
                    reranked_docs = [documents[i] for i in top_indices]

                    logging.info(f"✅ 重排序完成，原始文档数: {len(documents)}, 重排序后取前{top_k}个")
                    return reranked_docs
                else:
                    logging.warning("⚠️ 重排序API响应格式异常，使用原始排序")
                    return documents[:top_k]

        except Exception as e:
            logging.error(f"❌ 重排序过程中发生错误: {e}，使用原始排序")
            return documents[:top_k]

    def deduplicate_documents(self, documents: List[Any], similarity_threshold: float = 0.85) -> List[Any]:
        """
        对文档列表进行去重，移除相似度过高的重复内容。

        Args:
            documents: 要去重的文档列表
            similarity_threshold: 相似度阈值，超过此值的内容将被视为重复

        Returns:
            去重后的文档列表
        """
        if not documents or len(documents) <= 1:
            return documents

        try:
            from sklearn.feature_extraction.text import TfidfVectorizer
            from sklearn.metrics.pairwise import cosine_similarity
            import numpy as np

            # 提取文档内容
            contents = [doc.page_content for doc in documents]

            # 使用TF-IDF向量化
            vectorizer = TfidfVectorizer(
                max_features=1000,
                stop_words='english',
                ngram_range=(1, 2)
            )
            tfidf_matrix = vectorizer.fit_transform(contents)

            # 计算相似度矩阵
            similarity_matrix = cosine_similarity(tfidf_matrix)

            # 去重逻辑：保留相似度低的文档
            to_keep = []
            seen_indices = set()

            for i in range(len(documents)):
                if i in seen_indices:
                    continue

                # 找到当前文档的所有相似文档
                similar_indices = []
                for j in range(i + 1, len(documents)):
                    if similarity_matrix[i, j] >= similarity_threshold:
                        similar_indices.append(j)

                # 在当前文档和相似文档中选择得分最高的保留
                candidates = [i] + similar_indices
                # 使用relevance_score进行排序，如果没有则使用索引作为得分
                def get_relevance_score(doc):
                    return doc.metadata.get('relevance_score', 0) if hasattr(doc, 'metadata') else 0

                best_idx = max(candidates, key=lambda x: get_relevance_score(documents[x]))

                to_keep.append(documents[best_idx])
                seen_indices.update(candidates)

            logging.info(f"✅ 去重完成：原始文档 {len(documents)} 个，去重后 {len(to_keep)} 个")
            return to_keep

        except ImportError:
            logging.warning("⚠️ sklearn未安装，使用简单去重方法")
            # 简单的基于文本长度和关键词的去重
            return self._simple_deduplicate(documents, similarity_threshold)
        except Exception as e:
            logging.error(f"❌ 去重过程中发生错误: {e}，返回原始文档")
            return documents

    def _simple_deduplicate(self, documents: List[Any], similarity_threshold: float = 0.85) -> List[Any]:
        """
        简单的去重方法，当sklearn不可用时使用。
        基于文本长度和共同词比例进行去重。
        """
        if not documents or len(documents) <= 1:
            return documents

        to_keep = []

        for doc in documents:
            is_duplicate = False
            content = doc.page_content.lower()

            for kept_doc in to_keep:
                kept_content = kept_doc.page_content.lower()

                # 计算共同词比例
                words1 = set(content.split())
                words2 = set(kept_content.split())

                if words1 and words2:
                    common_ratio = len(words1.intersection(words2)) / max(len(words1), len(words2))
                    length_ratio = min(len(content), len(kept_content)) / max(len(content), len(kept_content))

                    # 如果共同词比例高且长度相近，认为是重复
                    if common_ratio >= similarity_threshold * 0.8 and length_ratio >= 0.7:
                        is_duplicate = True
                        break

            if not is_duplicate:
                to_keep.append(doc)

        logging.info(f"✅ 简单去重完成：原始文档 {len(documents)} 个，去重后 {len(to_keep)} 个")
        return to_keep

    async def filter_by_relevance(self, query: str, documents: List[Any], relevance_threshold: float = 0.3) -> List[Any]:
        """
        根据相关性阈值过滤文档，丢弃与当前任务无关的记录。

        Args:
            query (str): 查询字符串。
            documents: 要过滤的文档列表。
            relevance_threshold (float): 相关性阈值，低于此值的文档将被丢弃。

        Returns:
            过滤后的文档列表，只包含相关性足够高的文档。
        """
        if not documents:
            return []

        try:
            # 方法1：使用嵌入模型计算语义相似度
            filtered_docs = []
            query_embedding = await self.embedding_model.aembed_query(query)

            for doc in documents:
                doc_embedding = await self.embedding_model.aembed_query(doc.page_content)
                # 计算余弦相似度
                similarity = self._cosine_similarity(query_embedding, doc_embedding)

                if similarity >= relevance_threshold:
                    # 将相似度存储在文档的metadata中
                    if not hasattr(doc, 'metadata'):
                        doc.metadata = {}
                    doc.metadata['relevance_score'] = similarity
                    filtered_docs.append(doc)
                    logging.debug(f"📄 保留文档 (相似度: {similarity:.3f})")
                else:
                    logging.debug(f"🗑️ 丢弃文档 (相似度: {similarity:.3f}) - 低于阈值 {relevance_threshold}")

            logging.info(f"✅ 相关性过滤完成：原始文档 {len(documents)} 个，过滤后 {len(filtered_docs)} 个")
            return filtered_docs

        except Exception as e:
            logging.error(f"❌ 相关性过滤过程中发生错误: {e}，使用简单关键词匹配方法")

            # 方法2：简单关键词匹配作为备选方案
            return self._simple_relevance_filter(query, documents, relevance_threshold)

    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """
        计算两个向量的余弦相似度。

        Args:
            vec1: 第一个向量。
            vec2: 第二个向量。

        Returns:
            余弦相似度，范围为[-1, 1]。
        """
        import numpy as np
        vec1 = np.array(vec1)
        vec2 = np.array(vec2)

        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)

    def _simple_relevance_filter(self, query: str, documents: List[Any], relevance_threshold: float = 0.3) -> List[Any]:
        """
        简单的关键词匹配相关性过滤方法。

        Args:
            query (str): 查询字符串。
            documents: 要过滤的文档列表。
            relevance_threshold (float): 相关性阈值。

        Returns:
            过滤后的文档列表。
        """
        filtered_docs = []

        # 将查询分解为关键词
        query_words = set(query.lower().split())
        query_len = len(query_words)

        if query_len == 0:
            return documents  # 如果查询为空，返回所有文档

        for doc in documents:
            doc_content = doc.page_content.lower()
            doc_words = set(doc_content.split())

            # 计算关键词匹配度
            common_words = query_words.intersection(doc_words)
            relevance_score = len(common_words) / query_len if query_len > 0 else 0

            if relevance_score >= relevance_threshold:
                # 将相关性得分存储在文档的metadata中
                if not hasattr(doc, 'metadata'):
                    doc.metadata = {}
                doc.metadata['relevance_score'] = relevance_score
                filtered_docs.append(doc)
                logging.debug(f"📄 保留文档 (关键词匹配度: {relevance_score:.3f})")
            else:
                logging.debug(f"🗑️ 丢弃文档 (关键词匹配度: {relevance_score:.3f}) - 低于阈值 {relevance_threshold}")

        logging.info(f"✅ 简单相关性过滤完成：原始文档 {len(documents)} 个，过滤后 {len(filtered_docs)} 个")
        return filtered_docs
