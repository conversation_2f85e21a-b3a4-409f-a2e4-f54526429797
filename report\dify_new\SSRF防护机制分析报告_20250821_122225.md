# SSRF防护机制分析报告

## 概述

本报告分析了 `ssrf_proxy.py` 中的 `make_request` 方法实现，检查其 SSRF 防护机制的有效性，特别关注代理配置、URL 验证和端口限制等方面。

## 分析对象

- **文件路径**: `api/core/helper/ssrf_proxy.py`
- **主要方法**: `make_request`
- **相关配置**: 
  - `SSRF_PROXY_ALL_URL`: 通用代理 URL
  - `SSRF_PROXY_HTTP_URL`: HTTP 请求代理 URL
  - `SSRF_PROXY_HTTPS_URL`: HTTPS 请求代理 URL
  - `SSRF_DEFAULT_TIME_OUT`: 默认超时时间 (5秒)
  - `SSRF_DEFAULT_CONNECT_TIME_OUT`: 默认连接超时时间 (5秒)
  - `SSRF_DEFAULT_READ_TIME_OUT`: 默认读取超时时间 (5秒)
  - `SSRF_DEFAULT_WRITE_TIME_OUT`: 默认写入超时时间 (5秒)
  - `SSRF_DEFAULT_MAX_RETRIES`: 默认最大重试次数 (3次)

## make_request 方法分析

### 方法实现

```python
def make_request(method, url, max_retries=SSRF_DEFAULT_MAX_RETRIES, **kwargs):
    # 处理 allow_redirects 参数
    if "allow_redirects" in kwargs:
        allow_redirects = kwargs.pop("allow_redirects")
        if "follow_redirects" not in kwargs:
            kwargs["follow_redirects"] = allow_redirects

    # 设置默认超时
    if "timeout" not in kwargs:
        kwargs["timeout"] = httpx.Timeout(
            timeout=dify_config.SSRF_DEFAULT_TIME_OUT,
            connect=dify_config.SSRF_DEFAULT_CONNECT_TIME_OUT,
            read=dify_config.SSRF_DEFAULT_READ_TIME_OUT,
            write=dify_config.SSRF_DEFAULT_WRITE_TIME_OUT,
        )

    # 设置默认 SSL 验证
    if "ssl_verify" not in kwargs:
        kwargs["ssl_verify"] = HTTP_REQUEST_NODE_SSL_VERIFY

    ssl_verify = kwargs.pop("ssl_verify")

    # 重试逻辑
    retries = 0
    while retries <= max_retries:
        try:
            # 代理配置逻辑
            if dify_config.SSRF_PROXY_ALL_URL:
                with httpx.Client(proxy=dify_config.SSRF_PROXY_ALL_URL, verify=ssl_verify) as client:
                    response = client.request(method=method, url=url, **kwargs)
            elif dify_config.SSRF_PROXY_HTTP_URL and dify_config.SSRF_PROXY_HTTPS_URL:
                proxy_mounts = {
                    "http://": httpx.HTTPTransport(proxy=dify_config.SSRF_PROXY_HTTP_URL, verify=ssl_verify),
                    "https://": httpx.HTTPTransport(proxy=dify_config.SSRF_PROXY_HTTPS_URL, verify=ssl_verify),
                }
                with httpx.Client(mounts=proxy_mounts, verify=ssl_verify) as client:
                    response = client.request(method=method, url=url, **kwargs)
            else:
                with httpx.Client(verify=ssl_verify) as client:
                    response = client.request(method=method, url=url, **kwargs)

            # 检查状态码
            if response.status_code not in STATUS_FORCELIST:
                return response
            else:
                logging.warning(
                    "Received status code %s for URL %s which is in the force list", response.status_code, url
                )

        except httpx.RequestError as e:
            logging.warning("Request to URL %s failed on attempt %s: %s", url, retries + 1, e)
            if max_retries == 0:
                raise

        retries += 1
        if retries <= max_retries:
            time.sleep(BACKOFF_FACTOR * (2 ** (retries - 1)))
    raise MaxRetriesExceededError(f"Reached maximum retries ({max_retries}) for URL {url}")
```

### SSRF 防护机制分析

#### 1. 代理配置

**实现方式**:
- 优先使用 `SSRF_PROXY_ALL_URL` 作为所有请求的代理
- 如果没有设置 `SSRF_PROXY_ALL_URL`，则分别使用 `SSRF_PROXY_HTTP_URL` 和 `SSRF_PROXY_HTTPS_URL` 作为 HTTP 和 HTTPS 请求的代理
- 如果都没有设置，则直接发起请求，不使用代理

**安全评估**:
- ✅ **优点**: 代理配置是 SSRF 防护的核心机制，可以限制请求只能通过指定的代理服务器发出
- ⚠️ **风险点**: 当所有代理配置都未设置时，请求将直接发起，绕过 SSRF 防护

#### 2. URL 验证

**实现方式**:
- 方法中没有直接的 URL 验证逻辑
- 没有检查 URL 是否为内网地址或特殊协议
- 没有限制可访问的域名或 IP 范围

**安全评估**:
- ❌ **风险点**: 缺乏直接的 URL 验证是一个严重的安全问题
- ❌ **风险点**: 没有防止访问内网地址（如 127.0.0.1、192.168.x.x、10.x.x.x 等）
- ❌ **风险点**: 没有限制可访问的协议，可能导致 file://、gopher:// 等危险协议的使用

#### 3. 端口限制

**实现方式**:
- 方法中没有直接的端口限制逻辑
- 端口限制依赖于代理服务器的配置

**安全评估**:
- ❌ **风险点**: 方法本身没有端口限制，完全依赖外部代理配置
- ⚠️ **依赖点**: 端口限制依赖于 squid 代理服务器的配置

### 代理服务器配置分析

根据 `docker/ssrf_proxy/squid.conf.template` 文件，代理服务器有以下限制：

#### 1. 网络访问控制

```conf
acl localnet src *******-*************	# RFC 1122 "this" network (LAN)
acl localnet src 10.0.0.0/8		# RFC 1918 local private network (LAN)
acl localnet src **********/10		# RFC 6598 shared address space (CGN)
acl localnet src ***********/16 	# RFC 3927 link-local (directly plugged) machines
acl localnet src **********/12		# RFC 1918 local private network (LAN)
acl localnet src ***********/16		# RFC 1918 local private network (LAN)
acl localnet src fc00::/7       	# RFC 4193 local private network range
acl localnet src fe80::/10      	# RFC 4291 link-local (directly plugged) machines
```

**安全评估**:
- ✅ **优点**: 定义了内网地址范围，但没有明确拒绝这些地址的访问
- ⚠️ **风险点**: 配置中没有明确拒绝访问这些内网地址

#### 2. 端口限制

```conf
acl SSL_ports port 443
# acl SSL_ports port 1025-65535   # Enable the configuration to resolve this issue: https://github.com/langgenius/dify/issues/12792
acl Safe_ports port 80		# http
acl Safe_ports port 21		# ftp
acl Safe_ports port 443		# https
acl Safe_ports port 70		# gopher
acl Safe_ports port 210		# wais
acl Safe_ports port 1025-65535	# unregistered ports
acl Safe_ports port 280		# http-mgmt
acl Safe_ports port 488		# gss-http
acl Safe_ports port 591		# filemaker
acl Safe_ports port 777		# multiling http
acl CONNECT method CONNECT
```

**安全评估**:
- ⚠️ **风险点**: 允许访问 1025-65535 范围的所有端口，包括许多可能存在敏感服务的端口
- ⚠️ **风险点**: 注释掉的行 `# acl SSL_ports port 1025-65535` 可能是为了解决某个问题，但会增加安全风险
- ✅ **优点**: 限制了 CONNECT 方法只能用于 SSL 端口

#### 3. 访问控制

```conf
http_access deny !Safe_ports
http_access deny CONNECT !SSL_ports
http_access allow localhost manager
http_access deny manager
http_access allow localhost
http_access deny all
```

**安全评估**:
- ✅ **优点**: 拒绝访问非安全端口
- ✅ **优点**: 限制 CONNECT 方法只能用于 SSL 端口
- ✅ **优点**: 允许本地访问，但拒绝所有其他访问

## 漏洞分析

### 1. 缺乏直接的 URL 验证

**漏洞描述**:
`make_request` 方法中没有直接的 URL 验证逻辑，没有检查 URL 是否为内网地址或特殊协议。

**影响**:
- 可能导致访问内网敏感服务
- 可能导致访问危险协议（如 file://、gopher:// 等）

**证据**:
```python
# 没有对 url 参数进行任何验证
response = client.request(method=method, url=url, **kwargs)
```

### 2. 代理配置绕过风险

**漏洞描述**:
当所有代理配置（`SSRF_PROXY_ALL_URL`、`SSRF_PROXY_HTTP_URL` 和 `SSRF_PROXY_HTTPS_URL`）都未设置时，请求将直接发起，完全绕过 SSRF 防护。

**影响**:
- 完全绕过 SSRF 防护机制
- 可以直接访问内网和外部网络

**证据**:
```python
else:
    with httpx.Client(verify=ssl_verify) as client:
        response = client.request(method=method, url=url, **kwargs)
```

### 3. 过于宽松的端口限制

**漏洞描述**:
代理服务器配置中允许访问 1025-65535 范围的所有端口，包括许多可能存在敏感服务的端口。

**影响**:
- 可能访问内网敏感服务
- 增加攻击面

**证据**:
```conf
acl Safe_ports port 1025-65535	# unregistered ports
```

### 4. 缺乏明确内网访问拒绝

**漏洞描述**:
代理服务器配置中定义了内网地址范围，但没有明确拒绝这些地址的访问。

**影响**:
- 可能访问内网敏感服务
- 绕过 SSRF 防护的目的

**证据**:
```conf
acl localnet src ***********/16		# RFC 1918 local private network (LAN)
# 没有明确的拒绝规则
```

## 攻击场景

### 场景 1: 代理未配置时的直接访问

如果系统未正确配置 SSRF 代理，攻击者可以通过以下方式发起 SSRF 攻击：

```python
# 假设这是一个用户可控制的 URL
user_controlled_url = "http://127.0.0.1:22"  # 内网 SSH 服务

# 如果代理未配置，请求将直接发送到内网 SSH 服务
response = ssrf_proxy.get(user_controlled_url)
```

### 场景 2: 利用宽泛端口限制访问敏感服务

即使配置了代理，攻击者仍可能利用宽泛的端口限制访问内网敏感服务：

```python
# 假设这是一个用户可控制的 URL
user_controlled_url = "http://internal-service:6379"  # 内网 Redis 服务

# 即使配置了代理，由于端口限制过于宽泛，请求仍可能被允许
response = ssrf_proxy.get(user_controlled_url)
```

### 场景 3: 利用特殊协议

攻击者可能尝试使用特殊协议绕过防护：

```python
# 假设这是一个用户可控制的 URL
user_controlled_url = "file:///etc/passwd"  # 读取本地文件

# 方法中没有协议限制，可能导致安全风险
response = ssrf_proxy.get(user_controlled_url)
```

## 安全建议

### 1. 实现直接的 URL 验证

在 `make_request` 方法中添加 URL 验证逻辑，防止访问内网地址和危险协议：

```python
def make_request(method, url, max_retries=SSRF_DEFAULT_MAX_RETRIES, **kwargs):
    # 添加 URL 验证
    parsed_url = urlparse(url)
    
    # 检查协议
    if parsed_url.scheme not in ['http', 'https']:
        raise ValueError(f"Unsupported URL scheme: {parsed_url.scheme}")
    
    # 检查主机是否为内网地址
    hostname = parsed_url.hostname
    if hostname:
        try:
            ip = socket.gethostbyname(hostname)
            if ipaddress.ip_address(ip).is_private:
                raise ValueError(f"Access to private IP address {ip} is not allowed")
        except socket.gaierror:
            raise ValueError(f"Invalid hostname: {hostname}")
    
    # 原有代码...
```

### 2. 强制代理配置

确保代理配置始终有效，避免直接发起请求：

```python
def make_request(method, url, max_retries=SSRF_DEFAULT_MAX_RETRIES, **kwargs):
    # 添加 URL 验证...
    
    # 确保代理配置有效
    if not dify_config.SSRF_PROXY_ALL_URL and not (dify_config.SSRF_PROXY_HTTP_URL and dify_config.SSRF_PROXY_HTTPS_URL):
        raise ValueError("SSRF proxy configuration is required but not set")
    
    # 原有代码...
```

### 3. 限制端口访问

修改 squid 配置，限制可访问的端口：

```conf
# 只允许常用端口
acl Safe_ports port 80		# http
acl Safe_ports port 443		# https
acl Safe_ports port 8000-8100	# 常用开发端口
acl Safe_ports port 3000		# 常用开发端口
# 拒绝所有其他端口
http_access deny !Safe_ports
```

### 4. 明确拒绝内网访问

在 squid 配置中添加明确的内网访问拒绝规则：

```conf
# 拒绝内网访问
http_access deny localnet
```

### 5. 添加白名单机制

实现 URL 白名单机制，只允许访问特定域名：

```python
def make_request(method, url, max_retries=SSRF_DEFAULT_MAX_RETRIES, **kwargs):
    # 添加 URL 验证...
    
    # 检查是否在白名单中
    allowed_domains = dify_config.SSRF_ALLOWED_DOMAINS or []
    if allowed_domains:
        parsed_url = urlparse(url)
        hostname = parsed_url.hostname
        if hostname and hostname not in allowed_domains:
            raise ValueError(f"Domain {hostname} is not in the allowed domains list")
    
    # 原有代码...
```

## 结论

`ssrf_proxy.py` 中的 `make_request` 方法实现了一个基本的 SSRF 防护机制，主要依赖于代理服务器配置。然而，该方法存在多个安全风险：

1. 缺乏直接的 URL 验证，没有检查 URL 是否为内网地址或特殊协议
2. 当代理配置未设置时，请求将直接发起，完全绕过 SSRF 防护
3. 代理服务器配置中端口限制过于宽泛
4. 代理服务器配置中没有明确拒绝内网访问

这些风险可能导致攻击者绕过 SSRF 防护，访问内网敏感服务或利用特殊协议进行攻击。建议按照上述安全建议进行修复，增强 SSRF 防护机制的有效性。

---
*报告生成时间: 2025-08-21 12:22:25*