# config_loader.py

import json
import os
from typing import Dict, Any

class ConfigLoader:
    def __init__(self, config_path: str = "config.json"):
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Config file not found at: {config_path}")
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = json.load(f)

    def get_active_llm_config(self) -> Dict[str, Any]:
        """获取当前活动的LLM配置"""
        active_llm_key = self.config.get("active_llm")
        if not active_llm_key:
            raise ValueError("'active_llm' not specified in config")
        
        llm_config = self.config.get("llm_providers", {}).get(active_llm_key)
        if not llm_config:
            raise ValueError(f"Configuration for active LLM '{active_llm_key}' not found")
            
        return llm_config

    def get_active_embedding_config(self) -> Dict[str, Any]:
        """获取当前活动的Embedding模型配置"""
        active_embedding_key = self.config.get("embedding_options", {}).get("active_embedding")
        if not active_embedding_key:
            raise ValueError("'active_embedding' not specified in embedding_options")

        embedding_config = self.config.get("embedding_options", {}).get("embedding_providers", {}).get(active_embedding_key)
        if not embedding_config:
            raise ValueError(f"Configuration for active embedding model '{active_embedding_key}' not found")
            
        return embedding_config

    def get_reranker_config(self) -> Dict[str, Any]:
        """获取Reranker配置"""
        return self.config.get("reranker", {})

    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self.config.get("database", {})

    def get_text_splitter_config(self) -> Dict[str, Any]:
        """获取文本分割器配置"""
        return self.config.get("text_splitter", {})
        
    def get_project_path(self) -> str:
        """获取项目路径"""
        return self.config.get("project_path", "")

# 全局配置实例
try:
    config_loader = ConfigLoader()
except (FileNotFoundError, ValueError) as e:
    print(f"Error loading configuration: {e}")
    config_loader = None