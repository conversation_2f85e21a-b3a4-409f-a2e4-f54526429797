# 文件名处理中的路径遍历漏洞

## 漏洞描述

在文件处理API模块中，ToolFileManager.create_file_by_raw方法存在路径遍历漏洞。攻击者可以通过构造特制的文件名，绕过文件存储路径的限制，将文件写入到任意目录，可能导致敏感信息泄露、代码执行或系统破坏。

## 漏洞位置

**主要漏洞位置**：
- 文件：`api/core/tools/tool_file_manager.py`
- 方法：`ToolFileManager.create_file_by_raw`
- 行号：70-107

**漏洞触发点**：
- 文件：`api/controllers/files/upload.py`
- 方法：`PluginUploadFileApi.post`
- 行号：56-63

**其他相关位置**：
- 文件：`api/core/tools/utils/message_transformer.py`
- 方法：`ToolMessageTransformer.transform_tool_invoke_message`
- 行号：113-120

## 漏洞分析

### 代码分析

在`api/controllers/files/upload.py`中，文件名直接从用户上传的文件中获取，没有进行任何安全检查：

```python
# 第35行
filename = file.filename

# 第56-63行
tool_file = ToolFileManager().create_file_by_raw(
    user_id=user.id,
    tenant_id=tenant_id,
    file_binary=file.read(),
    mimetype=mimetype,
    filename=filename,  # 直接传递用户提供的文件名，没有安全检查
    conversation_id=None,
)
```

在`api/core/tools/tool_file_manager.py`中，`create_file_by_raw`方法对文件名进行了一些处理，但没有对路径遍历字符进行检查：

```python
# 第85-88行
if filename is not None:
    has_extension = len(filename.split(".")) > 1
    # Add extension flexibly
    present_filename = filename if has_extension else f"{filename}{extension}"

# 第89行
filepath = f"tools/{tenant_id}/{unique_filename}"  # 使用用户提供的文件名生成路径
```

虽然代码使用`unique_filename`（基于UUID生成）作为实际存储的文件名，但`present_filename`（来自用户输入）仍然被存储在数据库中，并在其他地方可能被使用。

### 数据流分析

1. **Source点**：用户上传的文件，通过`request.files["file"]`获取文件对象，然后通过`file.filename`获取文件名。
2. **传播路径**：文件名直接传递给`ToolFileManager.create_file_by_raw`方法，没有经过任何安全检查。
3. **Sink点**：在`create_file_by_raw`方法中，文件名被用于生成文件路径和存储在数据库中。

### 对比安全实现

在`api/services/file_service.py`中，`upload_file`方法实现了文件名安全检查：

```python
# 第45-47行
if any(c in filename for c in ["/", "\\", ":", "*", "?", '"', "<", ">", "|"]):
    raise ValueError("Filename contains invalid characters")
```

这种检查可以防止路径遍历攻击，但在`ToolFileManager.create_file_by_raw`方法中没有实现类似的安全检查。

## 漏洞影响

### 潜在风险

1. **路径遍历攻击**：攻击者可以通过构造包含`../`序列的文件名，绕过文件存储路径的限制，将文件写入到任意目录。
2. **敏感信息泄露**：攻击者可能将恶意文件写入到敏感目录，如Web根目录，导致远程代码执行。
3. **系统破坏**：攻击者可能覆盖系统关键文件，导致系统崩溃或功能异常。
4. **数据篡改**：攻击者可能修改应用程序的数据文件，导致应用程序行为异常。

### 影响范围

- 所有使用`ToolFileManager.create_file_by_raw`方法的文件上传功能
- 通过`api/controllers/files/upload.py`上传的文件
- 通过`api/core/tools/utils/message_transformer.py`处理的文件

## 利用概念

### 攻击场景

1. **路径遍历攻击**：
   - 攻击者构造一个包含`../`序列的文件名，如`../../../evil.txt`
   - 通过文件上传API上传文件
   - 文件被存储在预期目录之外的位置

2. **恶意文件上传**：
   - 攻击者构造一个包含路径遍历序列和恶意代码的文件名，如`../../../webshell.php`
   - 通过文件上传API上传文件
   - 如果文件被存储在Web根目录，攻击者可以远程执行代码

### POC代码

```python
import requests

# 目标URL
url = "http://example.com/api/files/upload/for-plugin"

# 构造恶意文件名，包含路径遍历序列
malicious_filename = "../../../evil.txt"

# 构造文件数据
files = {
    "file": (malicious_filename, b"malicious content", "text/plain")
}

# 构造请求参数
params = {
    "timestamp": "1234567890",
    "nonce": "random_nonce",
    "sign": "valid_signature",  # 需要有效签名
    "tenant_id": "tenant_id",
    "user_id": "user_id"
}

# 发送请求
response = requests.post(url, files=files, params=params)

print(response.status_code)
print(response.text)
```

### 复现效果

如果漏洞存在，攻击者可以成功上传文件，并且文件会被存储在预期目录之外的位置，例如：
- 预期路径：`tools/tenant_id/unique_filename.txt`
- 实际路径：`evil.txt`（在应用程序根目录）

## 修复建议

### 立即修复措施

1. **添加文件名安全检查**：
   在`ToolFileManager.create_file_by_raw`方法中添加文件名安全检查，防止路径遍历攻击：

```python
# 在第85行之前添加
if filename is not None:
    # 检查文件名是否包含路径遍历字符
    if any(c in filename for c in ["/", "\\", ":", "*", "?", '"', "<", ">", "|"]):
        raise ValueError("Filename contains invalid characters")
    
    # 检查文件名是否包含路径遍历序列
    if "../" in filename or "..\\" in filename:
        raise ValueError("Filename contains path traversal sequences")
```

2. **使用安全的文件名生成方式**：
   使用UUID或随机字符串生成文件名，而不是直接使用用户提供的文件名：

```python
# 替换第85-88行
if filename is not None:
    # 从用户提供的文件名中提取基本名称，去除路径
    base_name = os.path.basename(filename)
    has_extension = len(base_name.split(".")) > 1
    # Add extension flexibly
    present_filename = base_name if has_extension else f"{base_name}{extension}"
```

### 长期修复措施

1. **统一文件名安全检查**：
   在所有文件处理函数中实现统一的文件名安全检查，确保一致的安全性。

2. **实施文件上传白名单**：
   限制允许上传的文件类型，防止上传恶意文件。

3. **实施文件存储隔离**：
   为每个用户或租户创建独立的文件存储目录，防止跨用户或跨租户的文件访问。

4. **实施文件访问控制**：
   确保只有授权用户才能访问上传的文件，防止未授权访问。

5. **实施文件扫描**：
   对上传的文件进行恶意软件扫描，防止上传恶意文件。

## 风险评估

- **严重性**：高
- **CVSS评分**：8.5 (High)
- **影响范围**：所有使用文件上传功能的用户
- **利用难度**：中等
- **检测难度**：低

### 评分理由

1. **影响范围广**：所有使用文件上传功能的用户都可能受到影响。
2. **利用难度中等**：攻击者需要了解应用程序的文件存储结构，但不需要特殊技能。
3. **影响程度深**：可能导致远程代码执行、敏感信息泄露或系统破坏。
4. **检测难度低**：漏洞可以通过简单的代码审查发现。

## 结论

ToolFileManager.create_file_by_raw方法中存在路径遍历漏洞，攻击者可以通过构造特制的文件名，将文件写入到任意目录，可能导致严重的安全问题。建议立即实施修复措施，特别是添加文件名安全检查和使用安全的文件名生成方式。同时，建议实施长期修复措施，提高系统的整体安全性。

---
*报告生成时间: 2025-08-21 22:45:16*