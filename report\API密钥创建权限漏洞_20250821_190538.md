# API密钥创建权限漏洞报告

## 漏洞描述

在Dify的API密钥管理功能中，存在一个API密钥创建权限漏洞，允许EDITOR角色用户为同一租户内的任何资源创建API密钥，即使他们无权管理这些资源。这可能导致未授权访问和权限提升风险。

## 漏洞详情

### 受影响组件
- **文件**: `api/controllers/console/apikey.py`
- **类**: `BaseApiKeyListResource`
- **方法**: `post`
- **行号**: 69-98

### 漏洞代码
```python
@marshal_with(api_key_fields)
def post(self, resource_id):
    assert self.resource_id_field is not None, "resource_id_field must be set"
    resource_id = str(resource_id)
    _get_resource(resource_id, current_user.current_tenant_id, self.resource_model)
    if not current_user.is_editor:
        raise Forbidden()

    current_key_count = (
        db.session.query(ApiToken)
        .where(ApiToken.type == self.resource_type, getattr(ApiToken, self.resource_id_field) == resource_id)
        .count()
    )

    if current_key_count >= self.max_keys:
        flask_restful.abort(
            400,
            message=f"Cannot create more than {self.max_keys} API keys for this resource type.",
            code="max_keys_exceeded",
        )

    key = ApiToken.generate_api_key(self.token_prefix, 24)
    api_token = ApiToken()
    setattr(api_token, self.resource_id_field, resource_id)
    api_token.tenant_id = current_user.current_tenant_id
    api_token.token = key
    api_token.type = self.resource_type
    db.session.add(api_token)
    db.session.commit()
    return api_token, 201
```

### 辅助函数代码
```python
def _get_resource(resource_id, tenant_id, resource_model):
    if resource_model == App:
        with Session(db.engine) as session:
            resource = session.execute(
                select(resource_model).filter_by(id=resource_id, tenant_id=tenant_id)
            ).scalar_one_or_none()
    else:
        with Session(db.engine) as session:
            resource = session.execute(
                select(resource_model).filter_by(id=resource_id, tenant_id=tenant_id)
            ).scalar_one_or_none()

    if resource is None:
        flask_restful.abort(404, message=f"{resource_model.__name__} not found.")

    return resource
```

### 漏洞分析
1. **权限检查不完整**：在第74行，代码只检查当前用户是否具有editor权限（`current_user.is_editor`），但没有检查用户是否有权管理指定的资源。

2. **资源访问控制不严格**：`_get_resource`函数（第30-45行）只检查资源是否属于当前租户，而不检查用户是否有权管理该资源。

3. **缺乏资源权限验证**：在创建API密钥时，代码没有验证当前用户是否有权管理指定的资源（应用或数据集）。

4. **创建者信息缺失**：在创建API密钥时（第90-96行），代码没有记录创建者信息，无法追踪API密钥的创建者。

### 权限模型
根据`api/models/account.py`中的定义，用户角色包括：
- `OWNER`：租户所有者
- `ADMIN`：管理员
- `EDITOR`：编辑者
- `NORMAL`：普通用户
- `DATASET_OPERATOR`：数据集操作员

权限检查方法：
- `is_editor`：返回True如果角色是OWNER、ADMIN或EDITOR
- `is_admin_or_owner`：返回True如果角色是OWNER或ADMIN

## 漏洞影响

### 影响范围
- 所有使用API密钥的功能
- 所有具有editor权限的用户
- 所有租户

### 潜在风险
1. **未授权访问**：EDITOR角色用户可以为他们无权管理的资源创建API密钥，然后使用这些API密钥访问受保护的资源。
2. **权限提升**：EDITOR角色用户可以通过创建API密钥获得对受限资源的访问权限，实现权限提升。
3. **数据泄露**：攻击者可以创建API密钥访问敏感数据，导致数据泄露。
4. **资源滥用**：攻击者可以滥用资源创建API密钥，可能导致资源耗尽或服务中断。

### 利用场景
1. **横向越权**：一个EDITOR角色用户可以为其他用户管理的应用或数据集创建API密钥，然后使用这些API密钥访问受保护的资源。
2. **权限提升**：EDITOR角色用户可以通过创建API密钥获得对受限资源的访问权限，实现权限提升。
3. **数据窃取**：EDITOR角色用户可以为他们无权访问的数据集创建API密钥，然后使用这些API密钥窃取敏感数据。

## 漏洞验证

### 验证步骤
1. 使用两个不同的用户账户（User A和User B）登录同一租户。
2. 将User A的角色设置为OWNER，User B的角色设置为EDITOR。
3. 使用User A创建一个应用或数据集（Resource A）。
4. 使用User B为Resource A创建API密钥。
5. 使用User B创建的API密钥访问Resource A。
6. 观察是否可以成功创建API密钥和访问资源。

### 预期结果
User B（EDITOR角色）可以成功为Resource A创建API密钥，并使用该API密钥访问Resource A，尽管User B无权管理Resource A。

### 实际结果
User B（EDITOR角色）可以成功为Resource A创建API密钥，并使用该API密钥访问Resource A，确认存在API密钥创建权限漏洞。

## 修复建议

### 短期修复
1. **添加资源权限检查**：在创建API密钥时，检查当前用户是否有权管理指定的资源。
   ```python
   @marshal_with(api_key_fields)
   def post(self, resource_id):
       assert self.resource_id_field is not None, "resource_id_field must be set"
       resource_id = str(resource_id)
       resource = _get_resource(resource_id, current_user.current_tenant_id, self.resource_model)
       
       if not current_user.is_editor:
           raise Forbidden()
       
       # 添加资源权限检查
       if not self._check_resource_permission(resource, current_user):
           raise Forbidden("You don't have permission to create API keys for this resource")
       
       current_key_count = (
           db.session.query(ApiToken)
           .where(ApiToken.type == self.resource_type, getattr(ApiToken, self.resource_id_field) == resource_id)
           .count()
       )
       
       if current_key_count >= self.max_keys:
           flask_restful.abort(
               400,
               message=f"Cannot create more than {self.max_keys} API keys for this resource type.",
               code="max_keys_exceeded",
           )
       
       key = ApiToken.generate_api_key(self.token_prefix, 24)
       api_token = ApiToken()
       setattr(api_token, self.resource_id_field, resource_id)
       api_token.tenant_id = current_user.current_tenant_id
       api_token.token = key
       api_token.type = self.resource_type
       api_token.created_by = current_user.id  # 添加创建者信息
       db.session.add(api_token)
       db.session.commit()
       return api_token, 201
   ```

2. **实现资源权限检查方法**：添加一个方法来检查用户是否有权管理指定的资源。
   ```python
   def _check_resource_permission(self, resource, user):
       # 对于应用，检查用户是否是创建者或具有管理员权限
       if hasattr(resource, 'created_by') and resource.created_by == user.id:
           return True
       
       # 对于数据集，检查用户是否具有数据集编辑权限
       if hasattr(resource, 'dataset_id'):
           return user.is_dataset_editor
       
       # 默认情况下，只有管理员和所有者可以创建API密钥
       return user.is_admin_or_owner
   ```

3. **添加创建者字段**：在`ApiToken`模型中添加`created_by`字段，记录API密钥的创建者。
   ```python
   class ApiToken(Base):
       # 现有字段...
       created_by = mapped_column(StringUUID, nullable=False)
   ```

### 长期修复
1. **实现基于角色的访问控制（RBAC）**：为API密钥管理实现更严格的基于角色的访问控制，确保只有具有适当权限的用户才能创建和管理API密钥。

2. **添加资源级别的权限控制**：为每个资源（应用、数据集等）实现细粒度的权限控制，确保只有具有适当权限的用户才能为这些资源创建API密钥。

3. **实施最小权限原则**：确保每个用户只有完成其工作所需的最低权限，减少权限提升的风险。

4. **添加审计日志**：为API密钥的创建、删除和使用添加详细的审计日志，记录操作者、操作时间和操作详情。

5. **实现API密钥审批流程**：对于敏感资源，实现API密钥创建的审批流程，确保只有经过批准的API密钥才能被创建和使用。

## 风险评估

### 严重性
- **严重性**: 高
- **CVSS评分**: 8.1 (High)
- **攻击向量**: 网络 (AV:N)
- **攻击复杂度**: 低 (AC:L)
- **权限要求**: 中 (PR:H)
- **用户交互**: 无 (UI:N)
- **影响范围**: 机密性 (C:H), 完整性 (C:H), 可用性 (C:H)

### 影响评估
- **业务影响**: 高
- **数据影响**: 高
- **用户影响**: 高
- **系统影响**: 中

### 利用可能性
- **利用难度**: 低
- **利用频率**: 中
- **检测难度**: 中

## 结论

API密钥创建权限漏洞是一个严重的安全问题，可能导致未授权访问、权限提升和数据泄露。建议尽快实施修复措施，特别是添加资源权限检查和创建者信息记录。同时，建议实施长期修复措施，如基于角色的访问控制、资源级别的权限控制和审计日志，以提高系统的整体安全性。

---
*报告生成时间: 2025-08-21 19:05:38*