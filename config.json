{"active_llm": "deepseek_llm", "llm_providers": {"deepseek_llm": {"provider": "ChatDeepSeek", "config": {"model": "deepseek-ai/DeepSeek-R1-0528", "api_key": "sk-0eOt5j4XhuBkSn9mBIZigspdqkSHhyvGENO7fEFkYqvcEAfC", "api_base": "http://124.220.37.159:8301/v1", "max_retries": 20, "temperature": 0.6, "streaming": true, "timeout": 360}}, "openai_llm": {"provider": "ChatOpenAI", "config": {"model": "gpt-4.1-mini", "api_key": "your_openai_api_key", "api_base": "https://api.openai.com/v1", "temperature": 0.8}}, "google_llm": {"provider": "ChatGoogleGenerativeAI", "config": {"model": "gemini-2.5-pro", "api_key": "your_google_api_key", "api_base": "https://generativelanguage.googleapis.com/v1beta"}}}, "embedding_options": {"active_embedding": "bge_embedding", "embedding_providers": {"bge_embedding": {"provider": "OpenAIEmbeddings", "config": {"model": "BAAI/bge-m3", "api_key": "sk-zrpeouyilapveahhwtxoyuampujfplikgqjkauvjhztwasos", "api_base": "https://api.siliconflow.cn/v1"}}}}, "reranker": {"model": "BAAI/bge-reranker-v2-m3", "base_url": "https://api.siliconflow.cn/v1/rerank", "api_key": "sk-zrpeouyilapveahhwtxoyuampujfplikgqjkauvjhztwasos"}, "database": {"persist_directory": "./chroma_db"}, "text_splitter": {"chunk_size": 1000, "chunk_overlap": 200}, "project_path": "C:\\Users\\<USER>\\Desktop\\test\\dify-main"}