# tools/knowledge_tools.py

from langchain_core.tools import tool
from pydantic import BaseModel, Field
import logging

class KnowledgeSearchInput(BaseModel):
    query: str = Field(description="需要检索的知识内容相关描述")
    max_results: int = Field(default=2, description="返回的最大结果数量")
    use_reranker: bool = Field(default=True, description="是否使用重排序模型进行精确排序")

# 全局知识库管理器实例（将在初始化时设置）
_knowledge_manager = None

def set_knowledge_manager(knowledge_manager):
    """设置全局知识库管理器实例"""
    global _knowledge_manager
    _knowledge_manager = knowledge_manager

@tool(args_schema=KnowledgeSearchInput)
async def search_knowledge(query: str, max_results: int = 2, use_reranker: bool = True) -> str:
    """
    搜索和检索相关的知识内容。

    这个工具可以快速找到与当前任务相关的知识内容，
    提供智能化的知识查询和检索功能。

    使用方法：
    - 提供需要检索的内容描述
    - 可选指定返回结果数量
    - 可选是否使用重排序模型优化结果

    Args:
        query: 需要检索的知识内容相关描述
        max_results: 返回的最大结果数量（默认2条）
        use_reranker: 是否使用重排序模型进行精确排序（默认启用）

    Returns:
        相关知识内容的格式化字符串
    """
    if _knowledge_manager is None:
        return "❌ 知识库管理器未初始化，无法进行检索"

    try:
        logging.info(f"🔍 AI正在搜索知识库: '{query[:50]}...'")

        # 使用知识库管理器的搜索方法
        relevant_knowledge = await _knowledge_manager.search_knowledge(
            query=query,
            k=max_results,
            fetch_k=max(20, max_results * 3),  # 获取更多候选文档供重排序选择
            use_reranker=use_reranker
        )

        if relevant_knowledge:
            # 格式化搜索结果
            formatted_results = []
            for i, knowledge in enumerate(relevant_knowledge, 1):
                result = f"""
📄 知识 {i}:
标题: {knowledge['title']}
分类: {knowledge['category']}
相关度: {knowledge.get('relevance_score', 0):.3f}
内容: {knowledge['content'][:500]}{'...' if len(knowledge['content']) > 500 else ''}
"""
                if knowledge.get('tags'):
                    result += f"标签: {', '.join(knowledge['tags'])}\n"
                if knowledge.get('created_at'):
                    result += f"创建时间: {knowledge['created_at']}\n"

                formatted_results.append(result.strip())

            return f"📚 找到相关知识 {len(relevant_knowledge)} 条：\n\n" + "\n\n---\n\n".join(formatted_results)
        else:
            return "📭 未找到相关的知识内容"

    except Exception as e:
        logging.error(f"❌ 搜索知识内容时发生错误: {e}")
        return f"❌ 搜索知识内容时发生错误: {str(e)}"
