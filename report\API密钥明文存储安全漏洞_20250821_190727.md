## API密钥明文存储安全漏洞

### 漏洞描述
API密钥以明文形式存储在数据库中，没有进行任何加密或哈希处理。这导致如果数据库被未授权访问，所有API密钥将直接暴露。

### 漏洞位置
- **文件**: `api/models/model.py`
- **行号**: 第1548行
- **代码**: `token: Mapped[str] = mapped_column(String(255), nullable=False)`

### 漏洞分析
在ApiToken模型中，token字段被定义为普通的字符串类型，以明文形式存储在数据库中。当API密钥被创建时，系统生成的随机字符串直接存储在数据库中，没有任何加密或哈希处理。

### 数据流分析
1. **Source**: 用户通过控制台API创建API密钥 (`api/controllers/console/apikey.py` 第90行)
   ```python
   key = ApiToken.generate_api_key(self.token_prefix, 24)
   ```
2. **传播**: 生成的密钥直接存储在ApiToken对象中 (`api/controllers/console/apikey.py` 第94行)
   ```python
   api_token.token = key
   ```
3. **Sink**: 密钥以明文形式存储在数据库中 (`api/models/model.py` 第1548行)
   ```python
   token: Mapped[str] = mapped_column(String(255), nullable=False)
   ```

### 漏洞影响
1. **数据库泄露风险**: 如果数据库被未授权访问，所有API密钥将直接暴露。
2. **内部人员威胁**: 具有数据库访问权限的内部人员可以轻易获取所有API密钥。
3. **备份文件风险**: 数据库备份文件中包含明文API密钥，如果备份文件存储不当或被未授权访问，将导致API密钥泄露。

### 漏洞利用概念
1. **SQL注入攻击**:
   - 攻击者通过SQL注入漏洞获取数据库访问权限
   - 执行查询获取所有API密钥:
     ```sql
     SELECT id, app_id, tenant_id, type, token, created_at FROM api_tokens;
     ```
   - 使用获取的API密钥访问相应的资源，可能导致数据泄露、未授权操作等严重后果

2. **数据库备份泄露**:
   - 攻击者获取未加密的数据库备份文件
   - 直接从备份文件中提取API密钥
   - 使用提取的API密钥访问受保护的资源

### 修复建议
1. **实现API密钥哈希存储**:
   - 将API密钥以哈希形式存储，而不是明文存储
   - 只存储token的哈希值和前缀，验证时对用户提供的token进行哈希后与数据库中的哈希值比较
   - 使用强哈希算法如bcrypt或PBKDF2

2. **修改ApiToken模型**:
   ```python
   token_hash: Mapped[str] = mapped_column(String(255), nullable=False)
   token_prefix: Mapped[str] = mapped_column(String(10), nullable=False)
   ```

3. **修改API密钥验证逻辑**:
   ```python
   def verify_api_token(token, stored_hash):
       return bcrypt.checkpw(token.encode('utf-8'), stored_hash.encode('utf-8'))
   ```

4. **对数据库备份进行加密**:
   - 确保即使备份文件被泄露，攻击者也无法直接获取其中的敏感信息

### 风险评估
- **严重性**: 高
- **CVSS评分**: 8.2 (AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H)
- **影响范围**: 所有使用API密钥的功能
- **利用难度**: 低
- **检测难度**: 中等

### 结论
API密钥明文存储是一个严重的安全漏洞，可能导致数据泄露、未授权访问等严重后果。建议立即实施修复措施，特别是实现API密钥哈希存储和对数据库备份进行加密。

---
*报告生成时间: 2025-08-21 19:07:27*