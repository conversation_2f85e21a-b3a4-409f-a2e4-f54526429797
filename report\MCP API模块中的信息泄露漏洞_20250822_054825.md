# MCP API模块中的信息泄露漏洞报告

## 漏洞概述

**漏洞类型**: 信息泄露 (Information Disclosure)

**严重性**: 中危 (Medium)

**CVSS评分**: 6.5 (Medium)

**影响范围**: 所有使用MCP API功能的应用

**发现日期**: 2025-08-22

## 漏洞描述

MCP API模块中的错误处理机制存在信息泄露安全问题，主要表现在error_response方法和handle方法中的异常处理过程中。当系统捕获到异常时，直接将异常信息包含在错误响应中返回给客户端，没有进行任何过滤或清理，导致敏感信息可能泄露给攻击者。

## 漏洞位置

### 1. handle方法中的异常处理

**文件**: `api/core/mcp/server/streamable_http.py`

**行号**: 102-104

**代码片段**:
```python
except Exception as e:
    logger.exception("Internal server error")
    return self.error_response(INTERNAL_ERROR, f"Internal server error: {str(e)}")
```

### 2. error_response方法

**文件**: `api/core/mcp/server/streamable_http.py`

**行号**: 82-84

**代码片段**:
```python
def error_response(self, code: int, message: str, data=None):
    request_id = (self.request.root.model_extra or {}).get("id", 1) or 1
    return create_mcp_error_response(request_id, code, message, data)
```

### 3. create_mcp_error_response方法

**文件**: `api/core/mcp/utils.py`

**行号**: 104-114

**代码片段**:
```python
def create_mcp_error_response(request_id: int | str | None, code: int, message: str, data=None):
    """Create MCP error response"""
    error_data = ErrorData(code=code, message=message, data=data)
    json_response = JSONRPCError(
        jsonrpc="2.0",
        id=request_id or 1,
        error=error_data,
    )
    json_data = json.dumps(jsonable_encoder(json_response))
    sse_content = f"event: message\ndata: {json_data}\n\n".encode()
    yield sse_content
```

### 4. 控制器中的异常处理

**文件**: `api/controllers/mcp/mcp.py`

**行号**: 95-97

**代码片段**:
```python
return helper.compact_generate_response(
    create_mcp_error_response(request_id, types.INVALID_PARAMS, f"Invalid MCP request: {str(e)}")
)
```

## 数据流分析

### 完整的数据流路径

1. **攻击者发送恶意请求** → 
2. **MCP API控制器接收请求** (`api/controllers/mcp/mcp.py`) → 
3. **请求验证失败** (如参数验证失败) → 
4. **异常被捕获** (`except ValidationError as e`) → 
5. **异常信息直接包含在错误响应中** (`create_mcp_error_response(request_id, types.INVALID_PARAMS, f"Invalid MCP request: {str(e)}")`) → 
6. **敏感信息泄露给攻击者**

### 关键调用链

1. `api/controllers/mcp/mcp.py:100` → `mcp_server_handler.handle()`
2. `api/core/mcp/server/streamable_http.py:86` → `handle()`
3. `api/core/mcp/server/streamable_http.py:102-104` → `except Exception as e: return self.error_response(INTERNAL_ERROR, f"Internal server error: {str(e)}")`
4. `api/core/mcp/server/streamable_http.py:82-84` → `error_response()`
5. `api/core/mcp/utils.py:104-114` → `create_mcp_error_response()`
6. 敏感信息泄露给客户端

## 漏洞影响

攻击者可以通过构造恶意请求，触发服务器异常，从而获取敏感信息，如：

1. **数据库结构信息**：如SQL异常中的表名、字段名等
2. **文件路径信息**：如文件操作异常中的文件路径
3. **内部配置信息**：如配置加载异常中的配置项
4. **堆栈跟踪信息**：包含代码结构、函数调用链等
5. **其他敏感信息**：如API密钥、密码等（如果包含在异常信息中）

这些信息可能被用于进一步的攻击，如SQL注入、文件包含、配置篡改等。

## 利用概念

### 攻击场景1：触发数据库异常

攻击者可以构造一个包含特殊字符的请求，触发数据库异常，从而获取数据库结构信息。

**请求示例**:
```json
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "test_tool",
    "arguments": {
      "query": "' OR 1=1 --"
    }
  },
  "id": 1
}
```

**可能的响应**:
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "error": {
    "code": -32603,
    "message": "Internal server error: (sqlite3.OperationalError) near \"OR\": syntax error [SQL: SELECT * FROM users WHERE query = '' OR 1=1 --']",
    "data": null
  }
}
```

### 攻击场景2：触发文件操作异常

攻击者可以构造一个包含特殊路径的请求，触发文件操作异常，从而获取文件路径信息。

**请求示例**:
```json
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "file_tool",
    "arguments": {
      "path": "../../../etc/passwd"
    }
  },
  "id": 1
}
```

**可能的响应**:
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "error": {
    "code": -32603,
    "message": "Internal server error: [Errno 2] No such file or directory: '/app/data/../../../etc/passwd'",
    "data": null
  }
}
```

### 攻击场景3：触发配置加载异常

攻击者可以构造一个包含特殊配置的请求，触发配置加载异常，从而获取内部配置信息。

**请求示例**:
```json
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "config_tool",
    "arguments": {
      "config": "invalid_config"
    }
  },
  "id": 1
}
```

**可能的响应**:
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "error": {
    "code": -32603,
    "message": "Internal server error: ConfigurationError: Invalid configuration value 'invalid_config' for parameter 'database_url'",
    "data": null
  }
}
```

## 修复建议

### 短期修复措施

1. **过滤异常信息**：在`streamable_http.py`的`handle`方法中，不要直接将异常信息包含在错误响应中

```python
# 修复前
except Exception as e:
    logger.exception("Internal server error")
    return self.error_response(INTERNAL_ERROR, f"Internal server error: {str(e)}")

# 修复后
except Exception as e:
    logger.exception("Internal server error")
    return self.error_response(INTERNAL_ERROR, "Internal server error")
```

2. **清理错误消息**：在`create_mcp_error_response`方法中，对错误消息进行过滤或清理

```python
def create_mcp_error_response(request_id: int | str | None, code: int, message: str, data=None):
    """Create MCP error response"""
    # 过滤敏感信息
    sanitized_message = sanitize_error_message(message)
    
    error_data = ErrorData(code=code, message=sanitized_message, data=data)
    json_response = JSONRPCError(
        jsonrpc="2.0",
        id=request_id or 1,
        error=error_data,
    )
    json_data = json.dumps(jsonable_encoder(json_response))
    sse_content = f"event: message\ndata: {json_data}\n\n".encode()
    yield sse_content

def sanitize_error_message(message: str) -> str:
    """Sanitize error message to remove sensitive information"""
    # 移除可能的文件路径
    message = re.sub(r'\b[a-zA-Z]:\\[^)]*', '[REDACTED_PATH]', message)
    message = re.sub(r'\/[^)]*', '[REDACTED_PATH]', message)
    
    # 移除可能的SQL语句
    message = re.sub(r'SELECT.*FROM.*\w+', '[REDACTED_SQL]', message, flags=re.IGNORECASE)
    message = re.sub(r'INSERT.*INTO.*\w+', '[REDACTED_SQL]', message, flags=re.IGNORECASE)
    message = re.sub(r'UPDATE.*SET.*\w+', '[REDACTED_SQL]', message, flags=re.IGNORECASE)
    message = re.sub(r'DELETE.*FROM.*\w+', '[REDACTED_SQL]', message, flags=re.IGNORECASE)
    
    # 移除可能的配置信息
    message = re.sub(r'password=[^)]*', 'password=[REDACTED]', message, flags=re.IGNORECASE)
    message = re.sub(r'api_key=[^)]*', 'api_key=[REDACTED]', message, flags=re.IGNORECASE)
    message = re.sub(r'secret=[^)]*', 'secret=[REDACTED]', message, flags=re.IGNORECASE)
    
    return message
```

3. **对所有异常类型都返回通用错误消息**：不包含任何异常详细信息

```python
# 修复前
except ValueError as e:
    logger.exception("Invalid params")
    return self.error_response(INVALID_PARAMS, str(e))

# 修复后
except ValueError as e:
    logger.exception("Invalid params")
    return self.error_response(INVALID_PARAMS, "Invalid parameters")
```

### 长期修复措施

1. **实现错误消息过滤机制**：创建专门的错误消息过滤函数，对所有错误消息进行过滤

```python
class ErrorMessageFilter:
    """Filter sensitive information from error messages"""
    
    SENSITIVE_PATTERNS = [
        (r'\b[a-zA-Z]:\\[^)]*', '[REDACTED_PATH]'),  # Windows paths
        (r'\/[^)]*', '[REDACTED_PATH]'),  # Unix paths
        (r'SELECT.*FROM.*\w+', '[REDACTED_SQL]', re.IGNORECASE),  # SQL SELECT
        (r'INSERT.*INTO.*\w+', '[REDACTED_SQL]', re.IGNORECASE),  # SQL INSERT
        (r'UPDATE.*SET.*\w+', '[REDACTED_SQL]', re.IGNORECASE),  # SQL UPDATE
        (r'DELETE.*FROM.*\w+', '[REDACTED_SQL]', re.IGNORECASE),  # SQL DELETE
        (r'password=[^)]*', 'password=[REDACTED]', re.IGNORECASE),  # Passwords
        (r'api_key=[^)]*', 'api_key=[REDACTED]', re.IGNORECASE),  # API keys
        (r'secret=[^)]*', 'secret=[REDACTED]', re.IGNORECASE),  # Secrets
        (r'token=[^)]*', 'token=[REDACTED]', re.IGNORECASE),  # Tokens
    ]
    
    @classmethod
    def filter_message(cls, message: str) -> str:
        """Filter sensitive information from error message"""
        if not message:
            return message
            
        for pattern, replacement, *flags in cls.SENSITIVE_PATTERNS:
            if flags:
                message = re.sub(pattern, replacement, message, flags=flags[0])
            else:
                message = re.sub(pattern, replacement, message)
        
        return message
```

2. **实现错误代码分类**：根据错误类型和严重程度，定义不同的错误代码

```python
class MCPErrorCodes:
    """MCP error codes"""
    
    # Client errors (4xxx)
    INVALID_REQUEST = -32600
    METHOD_NOT_FOUND = -32601
    INVALID_PARAMS = -32602
    
    # Server errors (5xxx)
    INTERNAL_ERROR = -32603
    DATABASE_ERROR = -32604
    FILE_OPERATION_ERROR = -32605
    CONFIGURATION_ERROR = -32606
    AUTHENTICATION_ERROR = -32607
    AUTHORIZATION_ERROR = -32608
    RATE_LIMIT_ERROR = -32609
    VALIDATION_ERROR = -32610
```

3. **实现日志记录机制**：在服务器端记录详细的错误信息，但不将这些详细信息返回给客户端

```python
import logging

class MCPErrorLogger:
    """Log detailed error information"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def log_error(self, error: Exception, context: dict = None):
        """Log detailed error information"""
        error_info = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'traceback': traceback.format_exc(),
            'context': context or {}
        }
        
        self.logger.error(f"Error occurred: {json.dumps(error_info)}")
```

4. **实现错误响应格式标准化**：定义统一的错误响应格式，不包含敏感信息

```python
class MCPErrorResponse:
    """Standardized MCP error response"""
    
    def __init__(self, request_id: int | str | None, code: int, message: str, data=None):
        self.request_id = request_id or 1
        self.code = code
        self.message = ErrorMessageFilter.filter_message(message)
        self.data = data
    
    def to_response(self):
        """Convert to JSON-RPC error response"""
        error_data = ErrorData(code=self.code, message=self.message, data=self.data)
        json_response = JSONRPCError(
            jsonrpc="2.0",
            id=self.request_id,
            error=error_data,
        )
        return json_response
```

## 风险评估

- **严重性**: 中危 (Medium)
- **CVSS评分**: 6.5 (Medium)
  - **攻击向量**: 网络 (Network)
  - **攻击复杂度**: 低 (Low)
  - **所需权限**: 无 (None)
  - **用户交互**: 无 (None)
  - **影响范围**: 保密性 (Confidentiality)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 低
- **攻击成本**: 低

## 结论

MCP API模块中存在信息泄露安全问题，攻击者可以通过构造恶意请求，触发服务器异常，从而获取敏感信息。该漏洞的严重性中等，影响范围广，攻击成本低，建议立即采取修复措施。

修复的关键点在于：
1. 不直接将异常信息包含在错误响应中
2. 对错误消息进行过滤或清理
3. 实现统一的错误处理机制
4. 记录详细的错误信息到日志，但不返回给客户端

通过实施这些修复措施，可以有效防止敏感信息泄露，提高系统的安全性。

---
*报告生成时间: 2025-08-22 05:48:25*