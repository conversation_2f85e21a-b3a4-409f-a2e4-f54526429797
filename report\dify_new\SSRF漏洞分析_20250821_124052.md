# SSRF漏洞分析报告

## 漏洞概述

在Dify工作流和模型运行时模块中，发现了一个潜在的SSRF（服务器端请求伪造）漏洞，该漏洞位于工作流的HTTP请求节点（HTTP Request Node）中。攻击者可能通过构造恶意的URL，使服务器向内部网络发送请求，导致内网信息泄露或攻击内部系统。

## 漏洞位置

漏洞位于以下文件中：
- `core/workflow/nodes/http_request/executor.py`
- `core/helper/ssrf_proxy.py`

## 漏洞详情

### 1. HTTP请求节点分析

在`core/workflow/nodes/http_request/executor.py`文件中，`Executor`类负责处理HTTP请求的执行。虽然代码中有一些安全措施，但仍存在潜在的风险。

```python
def _init_url(self):
    self.url = self.variable_pool.convert_template(self.node_data.url).text

    # check if url is a valid URL
    if not self.url:
        raise InvalidURLError("url is required")
    if not self.url.startswith(("http://", "https://")):
        raise InvalidURLError("url should start with http:// or https://")
```

### 2. SSRF代理分析

在`core/helper/ssrf_proxy.py`文件中，实现了SSRF代理来处理HTTP请求，以防止SSRF攻击。

```python
def make_request(method, url, max_retries=SSRF_DEFAULT_MAX_RETRIES, **kwargs):
    # ... 其他代码 ...
    
    retries = 0
    while retries <= max_retries:
        try:
            if dify_config.SSRF_PROXY_ALL_URL:
                with httpx.Client(proxy=dify_config.SSRF_PROXY_ALL_URL, verify=ssl_verify) as client:
                    response = client.request(method=method, url=url, **kwargs)
            elif dify_config.SSRF_PROXY_HTTP_URL and dify_config.SSRF_PROXY_HTTPS_URL:
                proxy_mounts = {
                    "http://": httpx.HTTPTransport(proxy=dify_config.SSRF_PROXY_HTTP_URL, verify=ssl_verify),
                    "https://": httpx.HTTPTransport(proxy=dify_config.SSRF_PROXY_HTTPS_URL, verify=ssl_verify),
                }
                with httpx.Client(mounts=proxy_mounts, verify=ssl_verify) as client:
                    response = client.request(method=method, url=url, **kwargs)
            else:
                with httpx.Client(verify=ssl_verify) as client:
                    response = client.request(method=method, url=url, **kwargs)

            if response.status_code not in STATUS_FORCELIST:
                return response
            # ... 其他代码 ...
```

### 3. 漏洞点分析

虽然代码中使用了SSRF代理来防止SSRF攻击，但仍存在以下潜在风险：

1. **配置依赖风险**：SSRF防护完全依赖于配置文件中的代理设置。如果管理员未正确配置`SSRF_PROXY_ALL_URL`、`SSRF_PROXY_HTTP_URL`和`SSRF_PROXY_HTTPS_URL`，请求将直接发送到目标URL，没有任何保护。

2. **内网IP限制不足**：代码中没有对请求的目标IP地址进行明确限制，攻击者可能通过URL重定向或DNS重绑定等方式绕过代理，访问内网资源。

3. **协议限制不完整**：虽然代码限制了URL必须以`http://`或`https://`开头，但攻击者可能利用其他协议（如`file://`、`ftp://`等）通过重定向等方式进行攻击。

4. **端口限制不足**：代码中没有对请求的目标端口进行限制，攻击者可能扫描内网开放的服务端口。

### 4. 潜在绕过方式

1. **DNS重绑定攻击**：攻击者可以通过控制DNS服务器，使同一域名在短时间内解析到不同的IP地址，从而绕过SSRF代理的限制。

2. **URL重定向**：攻击者可以使用短链接服务或设置重定向，使服务器访问内网资源。

3. **IPv6地址绕过**：如果SSRF代理不支持IPv6，攻击者可能使用IPv6地址绕过限制。

4. **本地地址编码**：攻击者可能使用各种编码方式表示本地地址，如`http://127.0.0.1`、`http://localhost`、`http://[::1]`等。

## 漏洞利用条件

1. 攻击者需要能够创建或修改工作流，并添加HTTP请求节点。
2. 攻击者需要在HTTP请求节点中输入恶意的URL。
3. 服务器未正确配置SSRF代理，或者SSRF代理存在可以被绕过的限制。

## 漏洞影响

如果攻击者成功利用此漏洞，可能会：

1. 扫描内网开放的服务端口，发现内网系统。
2. 访问内网敏感服务，如数据库、缓存服务器等。
3. 读取内网服务的敏感信息，如配置文件、环境变量等。
4. 攻击内网存在的漏洞，进一步扩大攻击面。
5. 利用内网服务的未授权访问，获取更多敏感信息。

## 修复建议

1. **强制使用代理**：修改代码，强制所有HTTP请求都通过配置的代理发送，如果没有配置代理，则拒绝请求。

```python
if not (dify_config.SSRF_PROXY_ALL_URL or (dify_config.SSRF_PROXY_HTTP_URL and dify_config.SSRF_PROXY_HTTPS_URL)):
    raise HttpRequestNodeError("SSRF proxy is not configured, HTTP request is not allowed")
```

2. **IP地址白名单**：明确禁止访问内网IP地址，包括私有IP地址、回环地址等。

```python
import ipaddress

def is_internal_ip(url):
    hostname = urlparse(url).hostname
    if not hostname:
        return False
    
    try:
        ip = ipaddress.ip_address(hostname)
        return ip.is_private or ip.is_loopback
    except ValueError:
        # Hostname is not an IP address, resolve it
        try:
            addr_info = socket.getaddrinfo(hostname, None)
            for info in addr_info:
                ip = ipaddress.ip_address(info[4][0])
                if ip.is_private or ip.is_loopback:
                    return True
        except socket.gaierror:
            pass
    
    return False

if is_internal_ip(self.url):
    raise InvalidURLError("Access to internal IP addresses is not allowed")
```

3. **端口限制**：限制可访问的端口范围，禁止访问敏感端口。

```python
ALLOWED_PORTS = {80, 443, 8000, 8080, 8443}

def is_allowed_port(url):
    parsed_url = urlparse(url)
    if not parsed_url.port:
        # Use default ports
        if parsed_url.scheme == "http":
            port = 80
        elif parsed_url.scheme == "https":
            port = 443
        else:
            return False
    else:
        port = parsed_url.port
    
    return port in ALLOWED_PORTS

if not is_allowed_port(self.url):
    raise InvalidURLError(f"Port {parsed_url.port} is not allowed")
```

4. **协议限制**：只允许HTTP和HTTPS协议，禁止其他协议。

```python
parsed_url = urlparse(self.url)
if parsed_url.scheme not in {"http", "https"}:
    raise InvalidURLError("Only HTTP and HTTPS protocols are allowed")
```

5. **重定向限制**：禁止跟随重定向，或者限制重定向的次数和目标。

```python
request_args = {
    "url": self.url,
    "data": self.data,
    "files": self.files,
    "json": self.json,
    "content": self.content,
    "headers": headers,
    "params": self.params,
    "timeout": (self.timeout.connect, self.timeout.read, self.timeout.write),
    "ssl_verify": self.ssl_verify,
    "follow_redirects": False,  # 禁止跟随重定向
    "max_retries": self.max_retries,
}
```

6. **超时限制**：设置合理的请求超时时间，防止长时间挂起的请求。

```python
request_args = {
    # ... 其他参数 ...
    "timeout": (self.timeout.connect, self.timeout.read, self.timeout.write),
}
```

7. **响应大小限制**：限制响应的大小，防止大型响应导致资源耗尽。

```python
def _validate_and_parse_response(self, response: httpx.Response) -> Response:
    executor_response = Response(response)
    
    # 限制响应大小
    MAX_RESPONSE_SIZE = 10 * 1024 * 1024  # 10MB
    if len(response.content) > MAX_RESPONSE_SIZE:
        raise ResponseSizeError(f"Response size is too large, max size is {MAX_RESPONSE_SIZE / 1024 / 1024:.2f} MB")
    
    return executor_response
```

8. **日志记录**：记录所有HTTP请求的详细信息，便于安全审计和事件响应。

```python
logger.info(f"HTTP request: {method} {url}, headers: {headers}, status: {response.status_code}")
```

## 结论

SSRF漏洞是Dify工作流和模型运行时模块中的一个潜在安全风险，可能导致内网信息泄露或攻击内部系统。虽然代码中已经实现了一些防护措施，但仍存在被绕过的可能性。建议按照上述建议加强SSRF防护措施，并对所有使用HTTP请求节点的工作流进行安全审查。

---
*报告生成时间: 2025-08-21 12:40:52*