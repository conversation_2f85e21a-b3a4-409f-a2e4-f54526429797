# XSS漏洞：tool_file_manager.py中的文件名处理逻辑存在安全风险

## 漏洞描述

在 `api/core/tools/tool_file_manager.py` 文件的 `create_file_by_raw` 方法中，文件名处理逻辑存在安全风险，可能导致跨站脚本攻击（XSS）。

## 漏洞位置

文件：`api/core/tools/tool_file_manager.py`
方法：`create_file_by_raw`（第70-107行）

## 漏洞代码

```python
def create_file_by_raw(
    self,
    *,
    user_id: str,
    tenant_id: str,
    conversation_id: Optional[str],
    file_binary: bytes,
    mimetype: str,
    filename: Optional[str] = None,
) -> ToolFile:
    extension = guess_extension(mimetype) or ".bin"
    unique_name = uuid4().hex
    unique_filename = f"{unique_name}{extension}"
    # default just as before
    present_filename = unique_filename
    if filename is not None:
        has_extension = len(filename.split(".")) > 1
        # Add extension flexibly
        present_filename = filename if has_extension else f"{filename}{extension}"
    filepath = f"tools/{tenant_id}/{unique_filename}"
    storage.save(filepath, file_binary)

    with Session(self._engine, expire_on_commit=False) as session:
        tool_file = ToolFile(
            user_id=user_id,
            tenant_id=tenant_id,
            conversation_id=conversation_id,
            file_key=filepath,
            mimetype=mimetype,
            name=present_filename,
            size=len(file_binary),
        )

        session.add(tool_file)
        session.commit()
        session.refresh(tool_file)

    return tool_file
```

## 漏洞分析

1. 在 `create_file_by_raw` 方法中，如果 `filename` 参数不为 `None`，则 `present_filename` 会被设置为 `filename` 或 `f"{filename}{extension}"`。
2. 然后，`present_filename` 被用于设置 `ToolFile` 模型的 `name` 字段。
3. `ToolFile.name` 字段在多个地方被使用，包括：
   - 在 `api/controllers/files/tool_files.py` 中，用于设置下载文件的名称（`Content-Disposition` 头）。
   - 在 `api/controllers/files/upload.py` 中，被包含在返回给客户端的 JSON 响应中。
   - 在 `api/core/workflow/nodes/llm/file_saver.py` 和 `api/factories/file_factory.py` 中，被用于创建 `File` 对象的 `filename` 属性。

4. `filename` 参数的来源包括：
   - 在 `api/controllers/files/upload.py` 中，来自 `file.filename`，即上传文件的原始文件名。
   - 在 `api/core/tools/utils/message_transformer.py` 中，来自 `meta.get("filename", None)`，即从消息的元数据中获取。

5. 虽然在 `api/controllers/files/tool_files.py` 中使用了 `quote` 函数对 `tool_file.name` 进行编码，可以防止文件名注入攻击，但在其他地方（如 `api/controllers/files/upload.py` 中的 JSON 响应）没有对 `tool_file.name` 进行适当的过滤或转义，可能导致 XSS 攻击。

## 攻击场景

攻击者可以上传一个文件名包含恶意 JavaScript 代码的文件，例如：
```
<script>alert('XSS')</script>.txt
```

当这个文件的信息通过 API 返回给客户端，并且在客户端没有对文件名进行适当的过滤或转义时，恶意 JavaScript 代码可能会被执行，导致 XSS 攻击。

## 漏洞验证

虽然实际的文件路径是由系统生成的随机字符串构建的，不包含用户输入的内容，因此不存在路径遍历漏洞。但是，`ToolFile.name` 字段直接使用了用户提供的文件名，没有进行适当的过滤或转义，可能导致 XSS 攻击。

## 修复建议

1. 在 `create_file_by_raw` 方法中，对 `filename` 参数进行过滤或转义，以防止 XSS 攻击。
2. 在返回给客户端的 JSON 响应中，对 `ToolFile.name` 字段进行适当的过滤或转义。
3. 使用专门的文件名清理函数，去除文件名中的潜在恶意字符。

具体的修复代码可能如下：

```python
import html
import re

def sanitize_filename(filename):
    """
    Sanitize filename to prevent XSS attacks and other security issues.
    """
    if not filename:
        return filename
    
    # Remove path traversal sequences
    filename = re.sub(r'\.\.|\/|\\', '', filename)
    
    # HTML escape to prevent XSS attacks
    filename = html.escape(filename)
    
    # Remove control characters
    filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', filename)
    
    return filename

def create_file_by_raw(
    self,
    *,
    user_id: str,
    tenant_id: str,
    conversation_id: Optional[str],
    file_binary: bytes,
    mimetype: str,
    filename: Optional[str] = None,
) -> ToolFile:
    extension = guess_extension(mimetype) or ".bin"
    unique_name = uuid4().hex
    unique_filename = f"{unique_name}{extension}"
    # default just as before
    present_filename = unique_filename
    if filename is not None:
        # Sanitize filename to prevent XSS attacks and other security issues
        sanitized_filename = sanitize_filename(filename)
        has_extension = len(sanitized_filename.split(".")) > 1
        # Add extension flexibly
        present_filename = sanitized_filename if has_extension else f"{sanitized_filename}{extension}"
    filepath = f"tools/{tenant_id}/{unique_filename}"
    storage.save(filepath, file_binary)

    with Session(self._engine, expire_on_commit=False) as session:
        tool_file = ToolFile(
            user_id=user_id,
            tenant_id=tenant_id,
            conversation_id=conversation_id,
            file_key=filepath,
            mimetype=mimetype,
            name=present_filename,
            size=len(file_binary),
        )

        session.add(tool_file)
        session.commit()
        session.refresh(tool_file)

    return tool_file
```

## 风险等级

中等风险。虽然该漏洞不会导致文件系统被破坏，但可能导致用户数据泄露或会话劫持。

## 结论

`api/core/tools/tool_file_manager.py` 文件中的 `create_file_by_raw` 方法存在 XSS 漏洞，建议尽快修复。

---
*报告生成时间: 2025-08-21 12:05:25*