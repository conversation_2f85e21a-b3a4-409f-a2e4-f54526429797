# MCP API模块中的信息泄露漏洞

## 漏洞概述

MCP API模块中的错误处理机制存在信息泄露安全问题，攻击者可以通过构造恶意请求，触发服务器异常，从而获取敏感信息，如数据库结构、文件路径、内部配置等。

## 漏洞详情

### 1. 漏洞位置

- **文件**: `api/core/mcp/server/streamable_http.py`
- **方法**: `handle` (第86-104行)
- **代码**:
```python
def handle(self):
    handle_map = {
        types.InitializeRequest: self.initialize,
        types.ListToolsRequest: self.list_tools,
        types.CallToolRequest: self.invoke_tool,
        types.InitializedNotification: self.handle_notification,
        types.PingRequest: self.handle_ping,
    }
    try:
        if self.request_type in handle_map:
            return self.response(handle_map[self.request_type]())
        else:
            return self.error_response(METHOD_NOT_FOUND, f"Method not found: {self.request_type}")
    except ValueError as e:
        logger.exception("Invalid params")
        return self.error_response(INVALID_PARAMS, str(e))
    except Exception as e:
        logger.exception("Internal server error")
        return self.error_response(INTERNAL_ERROR, f"Internal server error: {str(e)}")
```

### 2. 漏洞分析

在`handle`方法中，当捕获到Exception时，直接将异常信息通过`str(e)`包含在错误响应中：

```python
except Exception as e:
    logger.exception("Internal server error")
    return self.error_response(INTERNAL_ERROR, f"Internal server error: {str(e)}")
```

这种处理方式可能导致敏感信息泄露，因为异常信息可能包含：

1. 数据库错误信息，如表名、字段名、SQL语句等
2. 文件系统路径信息
3. 内部配置信息，如API密钥、数据库连接字符串等
4. 堆栈跟踪信息，包含代码结构和内部实现细节

### 3. 错误响应生成流程

错误响应的生成流程如下：

1. **异常捕获**：在`handle`方法中捕获异常
2. **错误响应生成**：调用`error_response`方法，该方法调用`create_mcp_error_response`方法
3. **错误信息传递**：异常信息通过`str(e)`直接包含在错误响应中
4. **错误响应发送**：错误响应通过SSE（Server-Sent Events）发送给客户端

`error_response`方法的实现：
```python
def error_response(self, code: int, message: str, data=None):
    request_id = (self.request.root.model_extra or {}).get("id", 1) or 1
    return create_mcp_error_response(request_id, code, message, data)
```

`create_mcp_error_response`方法的实现：
```python
def create_mcp_error_response(request_id: int | str | None, code: int, message: str, data=None):
    """Create MCP error response"""
    error_data = ErrorData(code=code, message=message, data=data)
    json_response = JSONRPCError(
        jsonrpc="2.0",
        id=request_id or 1,
        error=error_data,
    )
    json_data = json.dumps(jsonable_encoder(json_response))
    sse_content = f"event: message\ndata: {json_data}\n\n".encode()
    yield sse_content
```

### 4. 信息泄露路径

完整的信息泄露路径如下：

1. **用户输入**：攻击者构造恶意请求，触发服务器异常
2. **异常处理**：服务器在处理请求时抛出异常
3. **异常捕获**：`handle`方法捕获异常
4. **错误响应生成**：调用`error_response`方法，将异常信息包含在错误响应中
5. **错误响应发送**：错误响应通过SSE发送给客户端
6. **信息泄露**：客户端接收到包含敏感信息的错误响应

### 5. 相关代码分析

#### 5.1 ErrorData类

```python
class ErrorData(BaseModel):
    """Error information for JSON-RPC error responses."""

    code: int
    """The error type that occurred."""

    message: str
    """
    A short description of the error. The message SHOULD be limited to a concise single
    sentence.
    """

    data: Any | None = None
    """
    Additional information about the error. The value of this member is defined by the
    sender (e.g. detailed error information, nested errors etc.).
    """

    model_config = ConfigDict(extra="allow")
```

#### 5.2 JSONRPCError类

```python
class JSONRPCError(BaseModel):
    """A response to a request that indicates an error occurred."""

    jsonrpc: Literal["2.0"]
    id: str | int
    error: ErrorData
    model_config = ConfigDict(extra="allow")
```

#### 5.3 错误常量

```python
# Standard JSON-RPC error codes
PARSE_ERROR = -32700
INVALID_REQUEST = -32600
METHOD_NOT_FOUND = -32601
INVALID_PARAMS = -32602
INTERNAL_ERROR = -32603
```

### 6. 其他相关错误处理

#### 6.1 base_app_generate_response_converter.py

在`api/core/app/apps/base_app_generate_response_converter.py`的`_error_to_stream_response`方法中（第95-130行），对于未知的异常类型，直接返回包含异常信息的响应：

```python
else:
    logging.error(e)
    data = {
        "code": "internal_server_error",
        "message": "Internal Server Error, please contact support.",
        "status": 500,
    }
```

#### 6.2 completion/app_generator.py

在`api/core/app/apps/completion/app_generator.py`的`_generate_worker`方法中（第216-228行），各种异常被捕获并通过`queue_manager.publish_error`方法发布：

```python
except InvokeAuthorizationError:
    queue_manager.publish_error(
        InvokeAuthorizationError("Incorrect API key provided"), PublishFrom.APPLICATION_MANAGER
    )
except ValidationError as e:
    logger.exception("Validation Error when generating")
    queue_manager.publish_error(e, PublishFrom.APPLICATION_MANAGER)
except ValueError as e:
    if dify_config.DEBUG:
        logger.exception("Error when generating")
    queue_manager.publish_error(e, PublishFrom.APPLICATION_MANAGER)
except Exception as e:
    logger.exception("Unknown Error when generating")
    queue_manager.publish_error(e, PublishFrom.APPLICATION_MANAGER)
```

## 漏洞利用

### 1. 攻击场景

攻击者可以通过构造恶意请求，触发服务器异常，从而获取敏感信息。以下是一些可能的攻击场景：

1. **SQL注入**：构造包含SQL注入的请求，触发数据库异常，获取数据库结构信息
2. **文件路径泄露**：构造包含非法文件路径的请求，触发文件系统异常，获取文件路径信息
3. **配置信息泄露**：构造包含非法参数的请求，触发配置异常，获取内部配置信息
4. **代码结构泄露**：构造包含非法参数的请求，触发代码异常，获取堆栈跟踪信息

### 2. 概念验证代码

```python
import requests
import json

# 构造恶意请求，触发服务器异常
malicious_request = {
    "jsonrpc": "2.0",
    "id": 1,
    "method": "call_tool",
    "params": {
        "name": "malicious_tool",
        "arguments": {
            "malicious_input": "'; DROP TABLE users; --"  # SQL注入攻击
        }
    }
}

# 发送请求
response = requests.post(
    "http://localhost:8080/mcp",
    headers={"Content-Type": "application/json"},
    data=json.dumps(malicious_request)
)

# 检查响应中是否包含敏感信息
if response.status_code == 200:
    response_data = response.json()
    if "error" in response_data:
        error_message = response_data["error"]["message"]
        print(f"Error message: {error_message}")
        # 检查错误消息中是否包含敏感信息
        if "Internal server error" in error_message and "traceback" in error_message:
            print("Sensitive information leaked!")
        else:
            print("No sensitive information detected.")
    else:
        print("No error in response.")
else:
    print(f"Request failed with status code: {response.status_code}")
```

### 3. 预期漏洞利用效果

当攻击者发送恶意请求时，服务器可能会返回包含敏感信息的错误响应，例如：

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "error": {
    "code": -32603,
    "message": "Internal server error: (psycopg2.errors.UndefinedTable) relation \"users\" does not exist\nLINE 1: ...users WHERE id = '1'; DROP TABLE users; --'\n                                                             ^\n\n[SQL: SELECT * FROM users WHERE id = '1'; DROP TABLE users; --]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)",
    "data": null
  }
}
```

从上面的错误响应中，攻击者可以获取以下敏感信息：

1. 数据库类型：PostgreSQL (psycopg2)
2. 表名：users
3. SQL查询语句：`SELECT * FROM users WHERE id = '1'; DROP TABLE users; --`
4. 服务器内部路径信息
5. 堆栈跟踪信息

## 修复建议

### 1. 短期修复措施

#### 1.1 过滤异常信息

在`streamable_http.py`的`handle`方法中，不要直接将异常信息包含在错误响应中，而是返回通用的错误消息：

```python
except Exception as e:
    logger.exception("Internal server error")
    return self.error_response(INTERNAL_ERROR, "Internal server error")
```

#### 1.2 清理错误消息

在`create_mcp_error_response`方法中，对错误消息进行过滤或清理，移除可能包含敏感信息的部分：

```python
def create_mcp_error_response(request_id: int | str | None, code: int, message: str, data=None):
    """Create MCP error response"""
    # 清理错误消息，移除可能包含敏感信息的部分
    cleaned_message = re.sub(r'\(.*?\)', '', message)  # 移除括号内的内容
    cleaned_message = re.sub(r'\[.*?\]', '', cleaned_message)  # 移除方括号内的内容
    cleaned_message = re.sub(r'at .*?:\d+', '', cleaned_message)  # 移除文件路径和行号
    
    error_data = ErrorData(code=code, message=cleaned_message, data=data)
    json_response = JSONRPCError(
        jsonrpc="2.0",
        id=request_id or 1,
        error=error_data,
    )
    json_data = json.dumps(jsonable_encoder(json_response))
    sse_content = f"event: message\ndata: {json_data}\n\n".encode()
    yield sse_content
```

#### 1.3 异常类型处理

在`base_app_generate_response_converter.py`的`_error_to_stream_response`方法中，对所有异常类型都返回通用错误消息，不包含敏感信息：

```python
else:
    logging.error(e)
    data = {
        "code": "internal_server_error",
        "message": "Internal Server Error, please contact support.",
        "status": 500,
    }
    # 不包含异常信息
    # data.setdefault("message", getattr(e, "description", str(e)))
```

### 2. 长期修复措施

#### 2.1 实现错误消息过滤机制

实现一个全局的错误消息过滤机制，对所有错误消息进行统一处理：

```python
def sanitize_error_message(message: str) -> str:
    """
    Sanitize error message to remove sensitive information.
    
    Args:
        message: Original error message
        
    Returns:
        Sanitized error message
    """
    # 移除可能包含敏感信息的模式
    patterns_to_remove = [
        r'\(.*?\)',  # 括号内的内容
        r'\[.*?\]',  # 方括号内的内容
        r'at .*?:\d+',  # 文件路径和行号
        r'Traceback \(most recent call last\):',  # Python traceback标记
        r'File ".*?", line \d+',  # Python文件路径和行号
    ]
    
    sanitized_message = message
    for pattern in patterns_to_remove:
        sanitized_message = re.sub(pattern, '', sanitized_message)
    
    return sanitized_message.strip()
```

#### 2.2 实现错误代码分类

根据错误类型返回不同的错误代码，但不包含详细的错误信息：

```python
class ErrorCodes:
    DATABASE_ERROR = "database_error"
    FILE_SYSTEM_ERROR = "file_system_error"
    CONFIGURATION_ERROR = "configuration_error"
    VALIDATION_ERROR = "validation_error"
    AUTHENTICATION_ERROR = "authentication_error"
    AUTHORIZATION_ERROR = "authorization_error"
    INTERNAL_ERROR = "internal_error"

def handle_exception(e: Exception) -> dict:
    """
    Handle exception and return error response without sensitive information.
    
    Args:
        e: Exception to handle
        
    Returns:
        Error response dictionary
    """
    if isinstance(e, DatabaseError):
        return {
            "code": ErrorCodes.DATABASE_ERROR,
            "message": "Database error occurred",
            "status": 500
        }
    elif isinstance(e, FileNotFoundError):
        return {
            "code": ErrorCodes.FILE_SYSTEM_ERROR,
            "message": "File not found",
            "status": 404
        }
    elif isinstance(e, ConfigurationError):
        return {
            "code": ErrorCodes.CONFIGURATION_ERROR,
            "message": "Configuration error",
            "status": 500
        }
    elif isinstance(e, ValidationError):
        return {
            "code": ErrorCodes.VALIDATION_ERROR,
            "message": "Validation error",
            "status": 400
        }
    elif isinstance(e, AuthenticationError):
        return {
            "code": ErrorCodes.AUTHENTICATION_ERROR,
            "message": "Authentication failed",
            "status": 401
        }
    elif isinstance(e, AuthorizationError):
        return {
            "code": ErrorCodes.AUTHORIZATION_ERROR,
            "message": "Authorization failed",
            "status": 403
        }
    else:
        return {
            "code": ErrorCodes.INTERNAL_ERROR,
            "message": "Internal server error",
            "status": 500
        }
```

#### 2.3 实现日志记录机制

在服务器端记录详细的异常信息，但不将其发送给客户端：

```python
import logging
from datetime import datetime

def log_exception(e: Exception, request_id: str = None):
    """
    Log exception with detailed information.
    
    Args:
        e: Exception to log
        request_id: Request ID for tracking
    """
    logger = logging.getLogger(__name__)
    
    log_data = {
        "timestamp": datetime.utcnow().isoformat(),
        "request_id": request_id,
        "exception_type": type(e).__name__,
        "exception_message": str(e),
        "traceback": traceback.format_exc()
    }
    
    logger.error(f"Exception occurred: {json.dumps(log_data)}")
```

#### 2.4 实现错误响应格式标准化

标准化错误响应格式，确保不包含敏感信息：

```python
class ErrorResponse(BaseModel):
    """Standardized error response format"""
    
    code: str
    """Error code"""
    
    message: str
    """Error message without sensitive information"""
    
    status: int
    """HTTP status code"""
    
    request_id: str = None
    """Request ID for tracking"""
    
    timestamp: str = None
    """Error timestamp"""
    
    def __init__(self, **data):
        super().__init__(**data)
        self.timestamp = datetime.utcnow().isoformat()
```

### 3. 安全最佳实践

1. **最小信息原则**：在错误响应中只包含必要的信息，不包含敏感信息。
2. **日志记录**：在服务器端记录详细的异常信息，但不将其发送给客户端。
3. **错误代码分类**：根据错误类型返回不同的错误代码，但不包含详细的错误信息。
4. **错误消息过滤**：对所有错误消息进行过滤，移除可能包含敏感信息的部分。
5. **定期审查**：定期审查错误处理机制，确保没有信息泄露问题。

## 风险评估

### 1. 严重性评估

- **严重性**: 中危 (Medium)
- **CVSS评分**: 6.5 (Medium)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 低
- **攻击成本**: 低

### 2. 影响分析

#### 2.1 机密性影响

攻击者可以通过构造恶意请求，获取以下敏感信息：

1. 数据库结构信息，如表名、字段名等
2. 文件系统路径信息
3. 内部配置信息，如API密钥、数据库连接字符串等
4. 堆栈跟踪信息，包含代码结构和内部实现细节

#### 2.2 完整性影响

攻击者可能利用获取的敏感信息，进行进一步的攻击，如：

1. SQL注入攻击
2. 文件包含攻击
3. 配置篡改攻击
4. 代码注入攻击

#### 2.3 可用性影响

攻击者可能通过构造恶意请求，导致服务器异常，影响服务的可用性。

### 3. 利用条件

1. **攻击者技能**: 低
2. **所需资源**: 低
3. **触发条件**: 构造恶意请求，触发服务器异常
4. **影响范围**: 所有使用MCP API功能的应用

## 结论

MCP API模块中存在信息泄露安全问题，攻击者可以通过构造恶意请求，触发服务器异常，从而获取敏感信息，如数据库结构、文件路径、内部配置等。该漏洞的严重性中等，影响范围广，攻击成本低，建议立即采取修复措施。

修复建议包括：

1. 过滤异常信息，不直接将异常信息包含在错误响应中
2. 清理错误消息，移除可能包含敏感信息的部分
3. 实现错误代码分类，根据错误类型返回不同的错误代码
4. 在服务器端记录详细的异常信息，但不将其发送给客户端
5. 实现错误响应格式标准化，确保不包含敏感信息

通过实施这些修复措施，可以有效防止敏感信息泄露，提高系统的安全性。

---
*报告生成时间: 2025-08-22 05:45:14*