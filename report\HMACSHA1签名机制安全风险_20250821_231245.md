# HMAC-SHA1签名机制安全风险报告

## 漏洞概述

本报告分析了Dify项目中HMAC-SHA1签名机制的安全风险。虽然`enterprise_inner_api_user_auth`装饰器目前未被使用，但其实现存在严重的安全隐患，如果将来被使用，可能导致系统安全受到威胁。

## 漏洞详情

### 1. HMAC-SHA1算法安全风险

**位置**: `api/controllers/inner_api/wraps.py` 第52行

**问题代码**:
```python
signature = hmac_new(inner_api_key.encode("utf-8"), data_to_sign.encode("utf-8"), sha1)
```

**问题描述**:
SHA1哈希算法已被证明存在安全漏洞，包括：
- 碰撞攻击：攻击者可以构造两个不同的输入，产生相同的SHA1哈希值
- 长度扩展攻击：攻击者可以在已知消息和其SHA1哈希值的情况下，构造出新消息的哈希值
- 计算能力提升：随着计算能力的提升，SHA1的安全性已经大大降低

**风险分析**:
使用SHA1进行HMAC签名验证存在以下风险：
1. **签名伪造风险**：攻击者可能通过碰撞攻击伪造有效签名
2. **身份验证绕过风险**：如果签名机制被绕过，攻击者可能冒充合法用户
3. **数据完整性风险**：攻击者可能篡改传输数据而不被检测到

### 2. 签名验证逻辑缺陷

**位置**: `api/controllers/inner_api/wraps.py` 第55-56行

**问题代码**:
```python
if signature_base64 != token:
    return view(*args, **kwargs)
```

**问题描述**:
当签名验证失败时，装饰器不会拒绝请求，而是继续执行视图函数。这意味着：
1. 签名验证失败不会阻止请求继续处理
2. 攻击者可以发送无效签名的请求，系统仍会处理
3. 缺乏明确的错误响应，使安全审计变得困难

**风险分析**:
这种设计存在以下风险：
1. **认证绕过风险**：攻击者可以发送无效签名，系统仍会处理请求
2. **安全审计困难**：无法通过日志区分合法请求和非法请求
3. **权限控制失效**：签名验证机制形同虚设，无法有效控制访问权限

### 3. 用户ID解析不安全

**位置**: `api/controllers/inner_api/wraps.py` 第44-46行

**问题代码**:
```python
user_id, token = parts
if " " in user_id:
    user_id = user_id.split(" ")[1]
```

**问题描述**:
用户ID解析逻辑过于简单，存在以下问题：
1. 没有验证user_id的格式和有效性
2. 简单的字符串分割可能导致解析错误
3. 没有对user_id进行安全检查

**风险分析**:
这种解析方式存在以下风险：
1. **注入攻击风险**：恶意构造的user_id可能导致注入攻击
2. **权限提升风险**：通过构造特殊user_id可能访问其他用户数据
3. **系统稳定性风险**：无效user_id可能导致系统异常

### 4. 密钥管理不当

**位置**: `api/controllers/inner_api/wraps.py` 第48行

**问题代码**:
```python
inner_api_key = request.headers.get("X-Inner-Api-Key", "")
```

**问题描述**:
密钥获取方式存在以下问题：
1. 密钥直接从HTTP头获取，没有额外的验证
2. 使用空字符串作为默认值，可能导致空密钥被接受
3. 没有验证密钥的有效性和强度

**风险分析**:
密钥管理不当可能导致以下风险：
1. **密钥泄露风险**：密钥在HTTP头中传输，可能被窃取
2. **弱密钥风险**：没有验证密钥强度，可能使用弱密钥
3. **密钥重用风险**：同一密钥可能被用于多个场景，增加泄露风险

### 5. 其他HMAC-SHA1使用风险

除了`enterprise_inner_api_user_auth`装饰器外，项目中还有其他地方使用了HMAC-SHA1：

**位置1**: `api/configs/remote_settings_sources/apollo/utils.py` 第18行
```python
hmac_code = hmac.new(secret.encode(), string_to_sign.encode(), hashlib.sha1).digest()
```

**位置2**: `api/configs/remote_settings_sources/nacos/http_request.py` 第52行
```python
base64.encodebytes(hmac.new(sk.encode(), sign_str.encode(), digestmod=hashlib.sha1).digest())
```

这些地方同样存在SHA1算法的安全风险，可能导致配置管理系统的安全问题。

## 数据流分析

### 签名验证流程

1. **请求接收**: 系统接收带有Authorization头的HTTP请求
2. **Authorization解析**: 从Authorization头中提取user_id和token
3. **密钥获取**: 从X-Inner-Api-Key头获取inner_api_key
4. **数据构造**: 构造待签名字符串"DIFY {user_id}"
5. **签名计算**: 使用HMAC-SHA1计算签名
6. **签名验证**: 比较计算出的签名与请求中的token
7. **用户查询**: 根据user_id查询用户信息
8. **请求处理**: 将用户信息传递给视图函数处理

### 攻击向量

1. **签名伪造攻击**:
   - 攻击者构造不同的user_id和token组合
   - 利用SHA1碰撞攻击生成有效签名
   - 发送带有伪造签名的请求

2. **签名绕过攻击**:
   - 攻击者发送无效签名
   - 系统继续处理请求而不是拒绝
   - 攻击者成功访问受保护资源

3. **用户ID注入攻击**:
   - 攻击者构造特殊的user_id
   - 利用解析逻辑缺陷注入恶意代码
   - 系统执行注入的恶意代码

## 漏洞影响

### 安全影响

1. **身份验证失效**: HMAC-SHA1的安全漏洞可能导致签名机制失效，攻击者可以伪造身份
2. **权限控制失效**: 签名验证逻辑缺陷可能导致权限控制失效，攻击者可以访问未授权资源
3. **数据泄露**: 攻击者可能通过签名伪造或绕过访问敏感数据
4. **系统完整性受损**: 攻击者可能通过用户ID注入攻击破坏系统完整性

### 业务影响

1. **数据泄露**: 敏感业务数据可能被未授权访问
2. **服务中断**: 恶意攻击可能导致服务不可用
3. **合规风险**: 不符合数据保护和安全合规要求
4. **声誉损失**: 安全事件可能导致用户信任度下降

## 利用概念

### 概念验证代码

```python
import base64
import hashlib
import hmac
import requests

# 伪造签名攻击示例
def forge_signature_attack(target_url, user_id, inner_api_key):
    """
    伪造HMAC-SHA1签名攻击示例
    """
    # 构造待签名字符串
    data_to_sign = f"DIFY {user_id}"
    
    # 使用HMAC-SHA1计算签名
    signature = hmac.new(inner_api_key.encode("utf-8"), data_to_sign.encode("utf-8"), hashlib.sha1)
    signature_base64 = base64.b64encode(signature.digest()).decode("utf-8")
    
    # 构造Authorization头
    authorization = f"{user_id}:{signature_base64}"
    
    # 构造请求头
    headers = {
        "Authorization": authorization,
        "X-Inner-Api-Key": inner_api_key
    }
    
    # 发送请求
    response = requests.post(target_url, headers=headers)
    
    return response.status_code, response.text

# 签名绕过攻击示例
def bypass_signature_attack(target_url, user_id):
    """
    签名绕过攻击示例
    """
    # 构造无效签名
    invalid_signature = "invalid_signature"
    
    # 构造Authorization头
    authorization = f"{user_id}:{invalid_signature}"
    
    # 构造请求头（不提供X-Inner-Api-Key）
    headers = {
        "Authorization": authorization
    }
    
    # 发送请求
    response = requests.post(target_url, headers=headers)
    
    return response.status_code, response.text

# 用户ID注入攻击示例
def user_id_injection_attack(target_url, inner_api_key):
    """
    用户ID注入攻击示例
    """
    # 构造恶意user_id
    malicious_user_id = "admin; DROP TABLE users; --"
    
    # 构造待签名字符串
    data_to_sign = f"DIFY {malicious_user_id}"
    
    # 使用HMAC-SHA1计算签名
    signature = hmac.new(inner_api_key.encode("utf-8"), data_to_sign.encode("utf-8"), hashlib.sha1)
    signature_base64 = base64.b64encode(signature.digest()).decode("utf-8")
    
    # 构造Authorization头
    authorization = f"{malicious_user_id}:{signature_base64}"
    
    # 构造请求头
    headers = {
        "Authorization": authorization,
        "X-Inner-Api-Key": inner_api_key
    }
    
    # 发送请求
    response = requests.post(target_url, headers=headers)
    
    return response.status_code, response.text
```

### 攻击场景

1. **内部API访问**:
   - 攻击者获取到inner_api_key
   - 利用HMAC-SHA1漏洞伪造有效签名
   - 访问内部API获取敏感数据

2. **权限提升**:
   - 攻击者构造特殊user_id
   - 利用签名验证逻辑缺陷绕过验证
   - 以管理员身份访问系统

3. **数据篡改**:
   - 攻击者利用长度扩展攻击
   - 修改请求数据而不改变签名
   - 提交篡改后的数据到系统

## 修复建议

### 1. 替换SHA1算法

**建议**: 使用更安全的哈希算法，如SHA256或SHA3

**修复代码**:
```python
from hashlib import sha256

# 替换前
signature = hmac_new(inner_api_key.encode("utf-8"), data_to_sign.encode("utf-8"), sha1)

# 替换后
signature = hmac_new(inner_api_key.encode("utf-8"), data_to_sign.encode("utf-8"), sha256)
```

### 2. 修复签名验证逻辑

**建议**: 当签名验证失败时，应拒绝请求而不是继续处理

**修复代码**:
```python
# 替换前
if signature_base64 != token:
    return view(*args, **kwargs)

# 替换后
if signature_base64 != token:
    abort(401)  # 返回401未授权状态码
```

### 3. 加强用户ID验证

**建议**: 对user_id进行格式和有效性验证

**修复代码**:
```python
import re

# 添加用户ID验证函数
def validate_user_id(user_id):
    """
    验证用户ID格式和有效性
    """
    # 检查user_id是否为UUID格式
    uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    if not re.match(uuid_pattern, user_id):
        return False
    
    # 检查user_id是否存在于数据库
    user = db.session.query(EndUser).where(EndUser.id == user_id).first()
    if not user:
        return False
    
    return True

# 在装饰器中使用验证函数
user_id, token = parts
if " " in user_id:
    user_id = user_id.split(" ")[1]

# 验证user_id
if not validate_user_id(user_id):
    abort(400)  # 返回400错误请求状态码
```

### 4. 加强密钥管理

**建议**: 实施更严格的密钥管理策略

**修复代码**:
```python
# 添加密钥验证函数
def validate_api_key(api_key):
    """
    验证API密钥的有效性和强度
    """
    # 检查密钥是否为空
    if not api_key:
        return False
    
    # 检查密钥长度（至少32字符）
    if len(api_key) < 32:
        return False
    
    # 检查密钥复杂度（包含大小写字母、数字和特殊字符）
    if not re.match(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{32,}$', api_key):
        return False
    
    # 检查密钥是否与配置中的密钥匹配
    if api_key != dify_config.INNER_API_KEY:
        return False
    
    return True

# 在装饰器中使用验证函数
inner_api_key = request.headers.get("X-Inner-Api-Key")
if not inner_api_key or not validate_api_key(inner_api_key):
    abort(401)  # 返回401未授权状态码
```

### 5. 实施签名时间戳机制

**建议**: 在签名中加入时间戳，防止重放攻击

**修复代码**:
```python
import time

# 修改待签名字符串，加入时间戳
timestamp = str(int(time.time()))
data_to_sign = f"DIFY {user_id} {timestamp}"

# 计算签名
signature = hmac_new(inner_api_key.encode("utf-8"), data_to_sign.encode("utf-8"), sha256)
signature_base64 = b64encode(signature.digest()).decode("utf-8")

# 在Authorization头中加入时间戳
authorization = f"{user_id}:{timestamp}:{signature_base64}"

# 验证签名时检查时间戳
def validate_timestamp(timestamp_str):
    """
    验证时间戳是否在有效期内（例如5分钟）
    """
    try:
        timestamp = int(timestamp_str)
        current_time = int(time.time())
        # 检查时间戳是否在有效期内（5分钟）
        if abs(current_time - timestamp) > 300:  # 300秒 = 5分钟
            return False
        return True
    except ValueError:
        return False

# 在装饰器中验证时间戳
parts = authorization.split(":")
if len(parts) != 3:
    abort(400)  # 返回400错误请求状态码

user_id, timestamp, token = parts

# 验证时间戳
if not validate_timestamp(timestamp):
    abort(401)  # 返回401未授权状态码
```

### 6. 修复其他HMAC-SHA1使用

**建议**: 将项目中所有使用HMAC-SHA1的地方替换为HMAC-SHA256

**修复代码**:
```python
# 修复apollo/utils.py
# 替换前
hmac_code = hmac.new(secret.encode(), string_to_sign.encode(), hashlib.sha1).digest()

# 替换后
hmac_code = hmac.new(secret.encode(), string_to_sign.encode(), hashlib.sha256).digest()

# 修复nacos/http_request.py
# 替换前
base64.encodebytes(hmac.new(sk.encode(), sign_str.encode(), digestmod=hashlib.sha1).digest())

# 替换后
base64.encodebytes(hmac.new(sk.encode(), sign_str.encode(), digestmod=hashlib.sha256).digest())
```

## 风险评估

### 严重性评估

- **CVSS评分**: 7.5 (High)
- **攻击向量**: 网络 (AN)
- **攻击复杂度**: 低 (AC:L)
- **权限要求**: 无 (PR:N)
- **用户交互**: 无 (UI:N)
- **影响范围**: 机密性 (C:H)、完整性 (I:H)、可用性 (A:H)

### 影响范围

- **直接影响**: 使用`enterprise_inner_api_user_auth`装饰器的API端点（目前未使用）
- **间接影响**: 使用HMAC-SHA1的其他组件（配置管理系统）
- **潜在影响**: 如果将来使用该装饰器，所有内部API都可能受到影响

### 利用可能性

- **利用难度**: 中等
- **所需技能**: 密码学知识、网络编程技能
- **所需条件**: 获取inner_api_key（如果使用该装饰器）
- **检测难度**: 中等

## 结论

`enterprise_inner_api_user_auth`装饰器存在多个严重的安全风险，包括使用不安全的SHA1算法、签名验证逻辑缺陷、用户ID解析不安全和密钥管理不当等问题。虽然该装饰器目前未被使用，但如果不修复这些问题，将来使用时可能导致严重的安全事件。

此外，项目中其他地方使用HMAC-SHA1的代码也存在类似的安全风险，需要一并修复。

建议尽快实施上述修复建议，特别是替换SHA1算法和修复签名验证逻辑，以提高系统的整体安全性。同时，建议建立安全开发流程，在代码审查阶段识别和修复类似的安全问题。

---
*报告生成时间: 2025-08-21 23:12:45*