# knowledge_manager.py

import logging
from typing import List, Dict, Any
import uuid
import json
import os
from datetime import datetime
import httpx

# --- LangChain & VectorStore 核心库 ---
from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter
from chromadb.config import Settings
from langchain_chroma import Chroma
from langchain_openai import OpenAIEmbeddings

class KnowledgeManager:
    """
    知识库管理器，用于管理和检索知识内容。

    该模块将知识内容进行向量化，并存储在 ChromaDB 中。
    支持知识内容的增删改查操作，提供智能检索和重排序功能。
    """
    def __init__(self, api_key: str, base_url: str, model_name: str, reranker_model: str,
                 reranker_base_url: str, reranker_api_key: str, persist_directory: str = None,
                 embedding_model=None, chunk_size=1000, chunk_overlap=200):
        """
        初始化知识库管理器。

        Args:
            api_key (str): 用于嵌入模型的 API key。
            base_url (str): 用于嵌入模型的 base URL。
            model_name (str): 要使用的嵌入模型的具体名称。
            persist_directory (str, optional): 用于持久化存储向量数据库的目录。如果为 None，则使用内存模式。
            embedding_model: 可选，用于文本嵌入的预构建模型实例。如果为 None，则使用提供的凭证创建。
            chunk_size (int): 文本分割的块大小。
            chunk_overlap (int): 文本分割的重叠大小。
            reranker_model (str): 重排序模型名称。
            reranker_base_url (str): 重排序模型的API地址。
            reranker_api_key (str): 重排序模型的API密钥。
        """
        if embedding_model:
            self.embedding_model = embedding_model
        else:
            self.embedding_model = OpenAIEmbeddings(
                openai_api_key=api_key,
                openai_api_base=base_url,
                model=model_name
            )

        self.text_splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)

        # 重排序模型配置
        self.reranker_model = reranker_model
        self.reranker_base_url = reranker_base_url
        self.reranker_api_key = reranker_api_key

        collection_name = "knowledge_base"

        # --- 禁用 ChromaDB 的匿名遥测 ---
        client_settings = Settings(anonymized_telemetry=False)

        if persist_directory:
            # 确保目录存在
            os.makedirs(persist_directory, exist_ok=True)
            # 使用持久化目录和自定义设置初始化 Chroma
            self.vectorstore = Chroma(
                collection_name=collection_name,
                embedding_function=self.embedding_model,
                persist_directory=persist_directory,
                client_settings=client_settings
            )
            logging.info(f"✅ 知识库管理器初始化成功，数据将持久化到 '{persist_directory}'")
        else:
            # 在内存中初始化 Chroma，并禁用遥测
            self.vectorstore = Chroma(
                collection_name=collection_name,
                embedding_function=self.embedding_model,
                client_settings=client_settings
            )
            logging.info("✅ 知识库管理器初始化成功，使用内存 ChromaDB (遥测已禁用)。")

    def add_knowledge(self, title: str, content: str, category: str = "general",
                     tags: List[str] = None, metadata: Dict[str, Any] = None) -> str:
        """
        添加知识内容到知识库。

        Args:
            title (str): 知识标题
            content (str): 知识内容
            category (str): 知识分类
            tags (List[str]): 标签列表
            metadata (Dict[str, Any]): 额外的元数据

        Returns:
            str: 知识内容的唯一ID
        """
        try:
            knowledge_id = str(uuid.uuid4())

            # 构建文档元数据
            doc_metadata = {
                "knowledge_id": knowledge_id,
                "title": title,
                "category": category,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "tags": json.dumps(tags or []),
                "content_length": len(content)
            }

            if metadata:
                doc_metadata.update(metadata)

            # 构建完整文档内容
            full_content = f"标题: {title}\n分类: {category}\n内容:\n{content}"

            if tags:
                full_content = f"标签: {', '.join(tags)}\n{full_content}"

            # 分割文本
            documents = self.text_splitter.create_documents([full_content])

            # 为每个文档片段添加元数据
            for doc in documents:
                doc.metadata = doc_metadata.copy()
                doc.metadata["chunk_index"] = documents.index(doc)
                doc.metadata["total_chunks"] = len(documents)
                doc.metadata["knowledge_id"] = knowledge_id

            # 添加到向量存储
            if documents:
                self.vectorstore.add_documents(documents)
                logging.info(f"✅ 知识内容已添加: '{title}' (ID: {knowledge_id})")

            return knowledge_id

        except Exception as e:
            logging.error(f"❌ 添加知识内容时发生错误: {e}")
            raise

    async def update_knowledge(self, knowledge_id: str, title: str = None, content: str = None,
                              category: str = None, tags: List[str] = None, metadata: Dict[str, Any] = None) -> bool:
        """
        更新知识内容。

        Args:
            knowledge_id (str): 知识ID
            title (str): 新的标题
            content (str): 新的内容
            category (str): 新的分类
            tags (List[str]): 新的标签列表
            metadata (Dict[str, Any]): 新的元数据

        Returns:
            bool: 更新是否成功
        """
        try:
            # 先删除旧的知识内容
            await self.delete_knowledge(knowledge_id)

            # 重新添加更新后的知识内容
            # 这里需要从数据库中获取原有的信息，但由于ChromaDB的限制，我们需要用户提供完整信息
            if title and content:
                new_id = self.add_knowledge(
                    title=title,
                    content=content,
                    category=category or "general",
                    tags=tags,
                    metadata=metadata
                )
                logging.info(f"✅ 知识内容已更新: ID {knowledge_id} -> {new_id}")
                return True
            else:
                logging.error("❌ 更新知识内容失败: 标题和内容不能为空")
                return False

        except Exception as e:
            logging.error(f"❌ 更新知识内容时发生错误: {e}")
            return False

    async def delete_knowledge(self, knowledge_id: str) -> bool:
        """
        删除知识内容。

        Args:
            knowledge_id (str): 知识ID

        Returns:
            bool: 删除是否成功
        """
        try:
            # 通过元数据过滤删除
            self.vectorstore.delete(where={"knowledge_id": knowledge_id})
            logging.info(f"✅ 知识内容已删除: ID {knowledge_id}")
            return True

        except Exception as e:
            logging.error(f"❌ 删除知识内容时发生错误: {e}")
            return False

    def list_knowledge(self, category: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """
        列出知识内容。

        Args:
            category (str): 分类筛选
            limit (int): 返回数量限制

        Returns:
            List[Dict[str, Any]]: 知识内容列表
        """
        try:
            where_clause = {"category": category} if category else None
            results = self.vectorstore.get(where=where_clause, limit=limit)

            knowledge_list = []
            seen_ids = set()

            for i, metadata in enumerate(results['metadatas']):
                knowledge_id = metadata.get('knowledge_id')
                if knowledge_id not in seen_ids:
                    knowledge_info = {
                        "id": knowledge_id,
                        "title": metadata.get('title', '无标题'),
                        "category": metadata.get('category', 'general'),
                        "created_at": metadata.get('created_at'),
                        "updated_at": metadata.get('updated_at'),
                        "tags": json.loads(metadata.get('tags', '[]')),
                        "content_length": metadata.get('content_length', 0)
                    }
                    knowledge_list.append(knowledge_info)
                    seen_ids.add(knowledge_id)

            return knowledge_list

        except Exception as e:
            logging.error(f"❌ 列出知识内容时发生错误: {e}")
            return []

    async def search_knowledge(self, query: str, k: int = 5, fetch_k: int = 20,
                              use_reranker: bool = True, similarity_threshold: float = 0.85,
                              relevance_threshold: float = 0.3, category: str = None) -> List[Dict[str, Any]]:
        """
        搜索相关知识内容。

        Args:
            query (str): 查询语句
            k (int): 最终要返回的知识数量
            fetch_k (int): 初始获取的文档数量
            use_reranker (bool): 是否使用重排序模型
            similarity_threshold (float): 去重时的相似度阈值
            relevance_threshold (float): 相关性阈值
            category (str): 分类筛选

        Returns:
            List[Dict[str, Any]]: 搜索结果列表
        """
        if not query:
            return []

        try:
            # 构建搜索过滤条件
            search_kwargs = {'k': fetch_k, 'fetch_k': fetch_k}
            if category:
                search_kwargs['filter'] = {"category": category}

            # 使用 MMR 进行初步检索
            retriever = self.vectorstore.as_retriever(
                search_type="mmr",
                search_kwargs=search_kwargs
            )
            initial_results = await retriever.ainvoke(query)

            if not initial_results:
                return []

            # 应用相关性阈值过滤
            filtered_results = await self.filter_by_relevance(query, initial_results, relevance_threshold)

            if not filtered_results:
                logging.info(f"⚠️ 所有候选文档的相关性得分均低于阈值 {relevance_threshold}，无相关知识")
                return []

            # 使用重排序模型进行精确排序
            final_results = filtered_results
            if use_reranker and len(filtered_results) > k:
                logging.info(f"🔄 正在对 {len(filtered_results)} 个相关知识进行重排序...")
                final_results = await self.rerank_documents(query, filtered_results, k)
            else:
                final_results = filtered_results[:k]

            # 强制去重处理
            if len(final_results) > 1:
                logging.info(f"🔍 正在对 {len(final_results)} 个知识进行去重处理...")
                final_results = self.deduplicate_documents(final_results, similarity_threshold)

            # 格式化结果
            results = []
            seen_ids = set()

            for doc in final_results:
                knowledge_id = doc.metadata.get('knowledge_id')
                if knowledge_id not in seen_ids:
                    knowledge_info = {
                        "id": knowledge_id,
                        "title": doc.metadata.get('title', '无标题'),
                        "category": doc.metadata.get('category', 'general'),
                        "content": doc.page_content,
                        "tags": json.loads(doc.metadata.get('tags', '[]')),
                        "relevance_score": doc.metadata.get('relevance_score', 0),
                        "created_at": doc.metadata.get('created_at'),
                        "updated_at": doc.metadata.get('updated_at')
                    }
                    results.append(knowledge_info)
                    seen_ids.add(knowledge_id)

            logging.info(f"🔍 根据查询 '{query[:50]}...' 找到了 {len(results)} 条相关知识")
            return results

        except Exception as e:
            logging.error(f"❌ 搜索知识内容时发生错误: {e}")
            return []
        finally:
            pass

    async def rerank_documents(self, query: str, documents: List[Any], top_k: int = 5) -> List[Any]:
        """
        使用重排序模型对文档进行重排序。
        """
        if not documents or len(documents) <= top_k:
            return documents[:top_k] if documents else []

        try:
            # 准备请求数据
            request_data = {
                "model": self.reranker_model,
                "query": query,
                "documents": [doc.page_content for doc in documents]
            }

            headers = {
                "Authorization": f"Bearer {self.reranker_api_key}",
                "Content-Type": "application/json"
            }

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    self.reranker_base_url,
                    headers=headers,
                    json=request_data
                )
                response.raise_for_status()
                result = response.json()

                # 根据重排序结果重新排列文档
                if "results" in result:
                    # 获取排序后的索引
                    sorted_indices = [item.get("index", i) for i, item in enumerate(result["results"])]
                    # 根据得分排序（得分高的在前）
                    sorted_indices.sort(key=lambda i: result["results"][i].get("score", 0), reverse=True)

                    # 取前top_k个
                    top_indices = sorted_indices[:top_k]
                    reranked_docs = [documents[i] for i in top_indices]

                    logging.info(f"✅ 重排序完成，原始文档数: {len(documents)}, 重排序后取前{top_k}个")
                    return reranked_docs
                else:
                    logging.warning("⚠️ 重排序API响应格式异常，使用原始排序")
                    return documents[:top_k]

        except Exception as e:
            logging.error(f"❌ 重排序过程中发生错误: {e}，使用原始排序")
            return documents[:top_k]

    def deduplicate_documents(self, documents: List[Any], similarity_threshold: float = 0.85) -> List[Any]:
        """
        对文档列表进行去重，移除相似度过高的重复内容。
        """
        if not documents or len(documents) <= 1:
            return documents

        try:
            from sklearn.feature_extraction.text import TfidfVectorizer
            from sklearn.metrics.pairwise import cosine_similarity
            import numpy as np

            # 提取文档内容
            contents = [doc.page_content for doc in documents]

            # 使用TF-IDF向量化
            vectorizer = TfidfVectorizer(
                max_features=1000,
                stop_words='english',
                ngram_range=(1, 2)
            )
            tfidf_matrix = vectorizer.fit_transform(contents)

            # 计算相似度矩阵
            similarity_matrix = cosine_similarity(tfidf_matrix)

            # 去重逻辑：保留相似度低的文档
            to_keep = []
            seen_indices = set()

            for i in range(len(documents)):
                if i in seen_indices:
                    continue

                # 找到当前文档的所有相似文档
                similar_indices = []
                for j in range(i + 1, len(documents)):
                    if similarity_matrix[i, j] >= similarity_threshold:
                        similar_indices.append(j)

                # 在当前文档和相似文档中选择得分最高的保留
                candidates = [i] + similar_indices
                def get_relevance_score(doc):
                    return doc.metadata.get('relevance_score', 0) if hasattr(doc, 'metadata') else 0

                best_idx = max(candidates, key=lambda x: get_relevance_score(documents[x]))

                to_keep.append(documents[best_idx])
                seen_indices.update(candidates)

            logging.info(f"✅ 去重完成：原始文档 {len(documents)} 个，去重后 {len(to_keep)} 个")
            return to_keep

        except ImportError:
            logging.warning("⚠️ sklearn未安装，使用简单去重方法")
            return self._simple_deduplicate(documents, similarity_threshold)
        except Exception as e:
            logging.error(f"❌ 去重过程中发生错误: {e}，返回原始文档")
            return documents

    def _simple_deduplicate(self, documents: List[Any], similarity_threshold: float = 0.85) -> List[Any]:
        """
        简单的去重方法，当sklearn不可用时使用。
        """
        if not documents or len(documents) <= 1:
            return documents

        to_keep = []

        for doc in documents:
            is_duplicate = False
            content = doc.page_content.lower()

            for kept_doc in to_keep:
                kept_content = kept_doc.page_content.lower()

                # 计算共同词比例
                words1 = set(content.split())
                words2 = set(kept_content.split())

                if words1 and words2:
                    common_ratio = len(words1.intersection(words2)) / max(len(words1), len(words2))
                    length_ratio = min(len(content), len(kept_content)) / max(len(content), len(kept_content))

                    # 如果共同词比例高且长度相近，认为是重复
                    if common_ratio >= similarity_threshold * 0.8 and length_ratio >= 0.7:
                        is_duplicate = True
                        break

            if not is_duplicate:
                to_keep.append(doc)

        logging.info(f"✅ 简单去重完成：原始文档 {len(documents)} 个，去重后 {len(to_keep)} 个")
        return to_keep

    async def filter_by_relevance(self, query: str, documents: List[Any], relevance_threshold: float = 0.3) -> List[Any]:
        """
        根据相关性阈值过滤文档。
        """
        if not documents:
            return []

        try:
            # 使用嵌入模型计算语义相似度
            filtered_docs = []
            query_embedding = await self.embedding_model.aembed_query(query)

            for doc in documents:
                doc_embedding = await self.embedding_model.aembed_query(doc.page_content)
                # 计算余弦相似度
                similarity = self._cosine_similarity(query_embedding, doc_embedding)

                if similarity >= relevance_threshold:
                    # 将相似度存储在文档的metadata中
                    if not hasattr(doc, 'metadata'):
                        doc.metadata = {}
                    doc.metadata['relevance_score'] = similarity
                    filtered_docs.append(doc)
                    logging.debug(f"📄 保留文档 (相似度: {similarity:.3f})")
                else:
                    logging.debug(f"🗑️ 丢弃文档 (相似度: {similarity:.3f}) - 低于阈值 {relevance_threshold}")

            logging.info(f"✅ 相关性过滤完成：原始文档 {len(documents)} 个，过滤后 {len(filtered_docs)} 个")
            return filtered_docs

        except Exception as e:
            logging.error(f"❌ 相关性过滤过程中发生错误: {e}，使用简单关键词匹配方法")
            return self._simple_relevance_filter(query, documents, relevance_threshold)

    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """
        计算两个向量的余弦相似度。
        """
        import numpy as np
        vec1 = np.array(vec1)
        vec2 = np.array(vec2)

        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)

    def _simple_relevance_filter(self, query: str, documents: List[Any], relevance_threshold: float = 0.3) -> List[Any]:
        """
        简单的关键词匹配相关性过滤方法。
        """
        filtered_docs = []

        # 将查询分解为关键词
        query_words = set(query.lower().split())
        query_len = len(query_words)

        if query_len == 0:
            return documents  # 如果查询为空，返回所有文档

        for doc in documents:
            doc_content = doc.page_content.lower()
            doc_words = set(doc_content.split())

            # 计算关键词匹配度
            common_words = query_words.intersection(doc_words)
            relevance_score = len(common_words) / query_len if query_len > 0 else 0

            if relevance_score >= relevance_threshold:
                # 将相关性得分存储在文档的metadata中
                if not hasattr(doc, 'metadata'):
                    doc.metadata = {}
                doc.metadata['relevance_score'] = relevance_score
                filtered_docs.append(doc)
                logging.debug(f"📄 保留文档 (关键词匹配度: {relevance_score:.3f})")
            else:
                logging.debug(f"🗑️ 丢弃文档 (关键词匹配度: {relevance_score:.3f}) - 低于阈值 {relevance_threshold}")

        logging.info(f"✅ 简单相关性过滤完成：原始文档 {len(documents)} 个，过滤后 {len(filtered_docs)} 个")
        return filtered_docs