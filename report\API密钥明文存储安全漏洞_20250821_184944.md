# API密钥明文存储安全漏洞报告

## 漏洞描述

在Dify项目中，API密钥以明文形式存储在数据库中，没有进行哈希或加密处理，这构成了严重的安全风险。任何能够访问数据库的人都可以直接获取所有用户的API密钥，从而完全控制相应的资源。

## 漏洞详情

### 1. API密钥存储方式

在`api/models/model.py`文件中，ApiToken模型定义如下：

```python
class ApiToken(Base):
    __tablename__ = "api_tokens"
    # ... 其他字段和索引定义 ...
    token: Mapped[str] = mapped_column(String(255), nullable=False)
    # ... 其他字段 ...
```

从代码中可以看到，token字段使用String(255)类型存储，没有加密或哈希处理，直接以明文形式存储在数据库中。

### 2. API密钥生成机制

在`api/models/model.py`文件中，API密钥生成函数如下：

```python
@staticmethod
def generate_api_key(prefix, n):
    while True:
        result = prefix + generate_string(n)
        if db.session.query(ApiToken).where(ApiToken.token == result).count() > 0:
            continue
        return result
```

其中，`generate_string`函数在`api/libs/helper.py`中定义：

```python
def generate_string(n):
    letters_digits = string.ascii_letters + string.digits
    result = ""
    for i in range(n):
        result += secrets.choice(letters_digits)
    return result
```

API密钥由前缀和24位随机字符串组成，例如`app-xxxxxxxxxxxxxxxxxxxxxxxx`。随机字符串使用`secrets.choice`生成，这是一个加密安全的随机数生成器，从随机性角度来看是安全的。

### 3. API密钥验证机制

在`api/controllers/service_api/wraps.py`文件中，API密钥验证函数如下：

```python
def validate_and_get_api_token(scope: str | None = None):
    """
    Validate and get API token.
    """
    auth_header = request.headers.get("Authorization")
    if auth_header is None or " " not in auth_header:
        raise Unauthorized("Authorization header must be provided and start with 'Bearer'")

    auth_scheme, auth_token = auth_header.split(None, 1)
    auth_scheme = auth_scheme.lower()

    if auth_scheme != "bearer":
        raise Unauthorized("Authorization scheme must be 'Bearer'")

    current_time = naive_utc_now()
    cutoff_time = current_time - timedelta(minutes=1)
    with Session(db.engine, expire_on_commit=False) as session:
        update_stmt = (
            update(ApiToken)
            .where(
                ApiToken.token == auth_token,
                (ApiToken.last_used_at.is_(None) | (ApiToken.last_used_at < cutoff_time)),
                ApiToken.type == scope,
            )
            .values(last_used_at=current_time)
            .returning(ApiToken)
        )
        result = session.execute(update_stmt)
        api_token = result.scalar_one_or_none()

        if not api_token:
            stmt = select(ApiToken).where(ApiToken.token == auth_token, ApiToken.type == scope)
            api_token = session.scalar(stmt)
            if not api_token:
                raise Unauthorized("Access token is invalid")
        else:
            session.commit()

    return api_token
```

验证过程直接将用户提供的token与数据库中存储的明文token进行比较，没有使用哈希比较或其他安全措施。

### 4. API密钥管理接口

在`api/controllers/console/apikey.py`文件中，API密钥创建和删除接口如下：

```python
@marshal_with(api_key_fields)
def post(self, resource_id):
    # ... 权限检查 ...
    
    key = ApiToken.generate_api_key(self.token_prefix, 24)
    api_token = ApiToken()
    setattr(api_token, self.resource_id_field, resource_id)
    api_token.tenant_id = current_user.current_tenant_id
    api_token.token = key  # 直接存储明文token
    api_token.type = self.resource_type
    db.session.add(api_token)
    db.session.commit()
    return api_token, 201

def delete(self, resource_id, api_key_id):
    # ... 权限检查 ...
    
    key = (
        db.session.query(ApiToken)
        .where(
            getattr(ApiToken, self.resource_id_field) == resource_id,
            ApiToken.type == self.resource_type,
            ApiToken.id == api_key_id,
        )
        .first()
    )
    
    if key is None:
        flask_restful.abort(404, message="API key not found")

    db.session.query(ApiToken).where(ApiToken.id == api_key_id).delete()
    db.session.commit()

    return {"result": "success"}, 204
```

创建API密钥时，直接将生成的明文token存储到数据库中，没有进行任何加密或哈希处理。

## 漏洞影响

### 1. 数据库泄露风险

如果数据库被未授权访问，所有API密钥将直接暴露。攻击者可以使用这些API密钥访问相应的资源，可能导致数据泄露、数据篡改或服务滥用。

### 2. 内部威胁

具有数据库访问权限的内部人员可以轻易获取所有API密钥，这可能被滥用于未经授权访问客户数据或进行恶意操作。

### 3. 备份安全

数据库备份文件中包含明文API密钥，如果备份文件存储不当或被未授权访问，将导致API密钥泄露。

### 4. 日志泄露

虽然我们没有发现直接记录API密钥到日志的代码，但如果数据库查询日志被记录并泄露，可能包含API密钥信息。

## 漏洞利用方式

### 场景1：数据库直接访问

攻击者通过SQL注入、未授权访问或其他方式获取数据库访问权限后，可以执行以下SQL查询获取所有API密钥：

```sql
SELECT id, app_id, tenant_id, type, token, last_used_at, created_at 
FROM api_tokens;
```

获取API密钥后，攻击者可以使用这些密钥访问相应的API端点，例如：

```bash
curl -X POST "https://api.example.com/v1/chat-messages" \
  -H "Authorization: Bearer app-xxxxxxxxxxxxxxxxxxxxxxxx" \
  -H "Content-Type: application/json" \
  -d '{"inputs": {"query": "Hello"}, "response_mode": "blocking", "conversation_id": ""}'
```

### 场景2：备份文件泄露

攻击者获取数据库备份文件后，可以恢复数据库并查询API密钥，或者直接从备份文件中提取API密钥。

### 场景3：内部人员滥用

具有数据库访问权限的内部人员可以直接查询API密钥，并使用这些密钥访问客户数据或进行恶意操作。

## 修复建议

### 1. 实现API密钥哈希存储

建议将API密钥以哈希形式存储，而不是明文存储。具体实现步骤如下：

1. 修改ApiToken模型，将token字段改为存储哈希值：

```python
class ApiToken(Base):
    __tablename__ = "api_tokens"
    # ... 其他字段和索引定义 ...
    token_hash: Mapped[str] = mapped_column(String(255), nullable=False)
    token_prefix: Mapped[str] = mapped_column(String(16), nullable=False)
    # ... 其他字段 ...
```

2. 修改API密钥生成和存储逻辑：

```python
@staticmethod
def generate_api_key(prefix, n):
    while True:
        token = prefix + generate_string(n)
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        if db.session.query(ApiToken).where(ApiToken.token_hash == token_hash).count() > 0:
            continue
        return token, token_hash

@marshal_with(api_key_fields)
def post(self, resource_id):
    # ... 权限检查 ...
    
    token, token_hash = ApiToken.generate_api_key(self.token_prefix, 24)
    api_token = ApiToken()
    setattr(api_token, self.resource_id_field, resource_id)
    api_token.tenant_id = current_user.current_tenant_id
    api_token.token_hash = token_hash  # 存储哈希值
    api_token.token_prefix = self.token_prefix  # 存储前缀，用于验证
    api_token.type = self.resource_type
    db.session.add(api_token)
    db.session.commit()
    # 返回明文token给用户，但只存储哈希值
    api_token.token = token  # 临时设置，用于返回
    return api_token, 201
```

3. 修改API密钥验证逻辑：

```python
def validate_and_get_api_token(scope: str | None = None):
    # ... 获取Authorization头 ...
    
    # 提取前缀
    prefix = auth_token.split('-')[0] + '-' if '-' in auth_token else ''
    token_hash = hashlib.sha256(auth_token.encode()).hexdigest()
    
    with Session(db.engine, expire_on_commit=False) as session:
        # 使用前缀和哈希值查询
        stmt = select(ApiToken).where(
            ApiToken.token_hash == token_hash,
            ApiToken.token_prefix == prefix,
            ApiToken.type == scope,
        )
        api_token = session.scalar(stmt)
        
        if not api_token:
            raise Unauthorized("Access token is invalid")
            
        # 更新last_used_at
        current_time = naive_utc_now()
        cutoff_time = current_time - timedelta(minutes=1)
        if api_token.last_used_at is None or api_token.last_used_at < cutoff_time:
            api_token.last_used_at = current_time
            session.commit()

    return api_token
```

### 2. 实施最小权限原则

确保数据库访问权限遵循最小权限原则，只有必要的应用程序和服务才能访问数据库，并且只能执行必要的操作。

### 3. 加密数据库备份

对数据库备份进行加密，确保即使备份文件被泄露，攻击者也无法直接获取其中的敏感信息。

### 4. 实施审计日志

实施详细的审计日志，记录所有API密钥的创建、使用和删除操作，以便在发生安全事件时进行追踪。

### 5. 定期轮换API密钥

建议实施API密钥定期轮换机制，要求用户定期更新API密钥，减少API密钥泄露后的影响。

## 风险评估

### 严重性：高

### 理由：

1. **影响范围广**：影响所有使用API密钥的用户和应用
2. **利用难度低**：一旦获取数据库访问权限，获取API密钥非常简单
3. **影响程度深**：API密钥泄露可能导致数据泄露、数据篡改或服务滥用
4. **检测难度高**：API密钥泄露可能长时间不被发现

### CVSS评分：8.2 (High)

- **攻击向量(AV)**: 网络 (N)
- **攻击复杂度(AC)**: 低 (L)
- **权限要求(PR)**: 高 (H) - 需要数据库访问权限
- **用户交互(UI)**: 无 (N)
- **影响范围(S)**: 不变 (C)
- **机密性影响(C)**: 高 (H)
- **完整性影响(I)**: 高 (H)
- **可用性影响(A)**: 高 (H)

## 结论

API密钥明文存储是一个严重的安全漏洞，可能导致数据泄露、数据篡改或服务滥用。建议立即实施修复措施，将API密钥以哈希形式存储，并加强数据库访问控制和备份安全。同时，建议实施审计日志和API密钥定期轮换机制，进一步提高系统安全性。

---
*报告生成时间: 2025-08-21 18:49:44*