#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库管理程序

这个程序提供了一个交互式的知识库管理界面，
允许用户对知识内容进行增删改查操作。
知识库使用持久化的向量数据库，不会因为程序重启而丢失数据。
"""

import os
import json
import argparse
import asyncio
from typing import Dict, Any, List
from datetime import datetime
import logging

# 导入知识库管理器
from knowledge_manager import KnowledgeManager

class KnowledgeAdmin:
    """知识库管理程序"""

    def __init__(self, config_file: str = "knowledge_config.json"):
        self.config_file = config_file
        self.config = self.load_config()
        self.knowledge_manager = None
        self.setup_logging()

    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('knowledge_admin.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )

    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            "embedding": {
                "api_key": "your_api_key_here",
                "base_url": "https://api.openai.com/v1",
                "model_name": "text-embedding-3-small"
            },
            "reranker": {
                "model": "bge-reranker-base",
                "base_url": "http://localhost:11434/api/rerank",
                "api_key": "your_reranker_api_key"
            },
            "database": {
                "persist_directory": "./knowledge_db"
            },
            "text_splitter": {
                "chunk_size": 1000,
                "chunk_overlap": 200
            }
        }

        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                default_config.update(user_config)
                logging.info(f"✅ 配置文件加载成功: {self.config_file}")
            except Exception as e:
                logging.error(f"❌ 加载配置文件失败: {e}，使用默认配置")
        else:
            logging.warning(f"⚠️ 配置文件不存在: {self.config_file}，使用默认配置")

        return default_config

    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            logging.info(f"✅ 配置文件已保存: {self.config_file}")
        except Exception as e:
            logging.error(f"❌ 保存配置文件失败: {e}")

    def initialize_knowledge_manager(self):
        """初始化知识库管理器"""
        try:
            self.knowledge_manager = KnowledgeManager(
                api_key=self.config["embedding"]["api_key"],
                base_url=self.config["embedding"]["base_url"],
                model_name=self.config["embedding"]["model_name"],
                reranker_model=self.config["reranker"]["model"],
                reranker_base_url=self.config["reranker"]["base_url"],
                reranker_api_key=self.config["reranker"]["api_key"],
                persist_directory=self.config["database"]["persist_directory"],
                chunk_size=self.config["text_splitter"]["chunk_size"],
                chunk_overlap=self.config["text_splitter"]["chunk_overlap"]
            )
            logging.info("✅ 知识库管理器初始化成功")
            return True
        except Exception as e:
            logging.error(f"❌ 知识库管理器初始化失败: {e}")
            return False

    def print_menu(self):
        """打印主菜单"""
        print("\n" + "="*60)
        print("🧠 知识库管理程序")
        print("="*60)
        print("1. 📝 添加知识")
        print("2. 🔍 搜索知识")
        print("3. 📋 列出知识")
        print("4. ✏️  更新知识")
        print("5. 🗑️  删除知识")
        print("6. ⚙️  配置设置")
        print("7. 📊 统计信息")
        print("0. 👋 退出程序")
        print("="*60)

    def get_user_input(self, prompt: str, required: bool = True) -> str:
        """获取用户输入"""
        while True:
            user_input = input(prompt).strip()
            if required and not user_input:
                print("❌ 输入不能为空，请重新输入")
                continue
            return user_input

    def get_multiline_input(self, prompt: str) -> str:
        """获取多行文本输入"""
        print(prompt)
        print("输入完成后，在新行输入 'END' 结束输入：")
        lines = []
        while True:
            line = input()
            if line.strip().upper() == 'END':
                break
            lines.append(line)
        return '\n'.join(lines).strip()

    def add_knowledge(self):
        """添加知识"""
        print("\n📝 添加知识")
        print("-" * 30)

        title = self.get_user_input("知识标题: ")
        content = self.get_multiline_input("知识内容:")
        category = self.get_user_input("知识分类 (默认: general): ") or "general"

        tags_input = self.get_user_input("标签 (用逗号分隔，可选): ")
        tags = [tag.strip() for tag in tags_input.split(',') if tag.strip()] if tags_input else []

        if not content:
            print("❌ 知识内容不能为空")
            return

        try:
            knowledge_id = self.knowledge_manager.add_knowledge(
                title=title,
                content=content,
                category=category,
                tags=tags
            )
            print(f"✅ 知识添加成功，ID: {knowledge_id}")
        except Exception as e:
            print(f"❌ 添加知识失败: {e}")

    def search_knowledge(self):
        """搜索知识"""
        print("\n🔍 搜索知识")
        print("-" * 30)

        query = self.get_user_input("搜索关键词: ")
        if not query:
            print("❌ 搜索关键词不能为空")
            return

        category = self.get_user_input("分类筛选 (可选): ") or None
        max_results = self.get_user_input("最大结果数 (默认: 5): ") or "5"

        try:
            max_results = int(max_results)
        except ValueError:
            max_results = 5

        try:
            results = asyncio.run(self.knowledge_manager.search_knowledge(
                query=query,
                k=max_results,
                category=category
            ))

            if results:
                print(f"\n📚 找到 {len(results)} 条相关知识:")
                for i, knowledge in enumerate(results, 1):
                    print(f"\n{i}. {knowledge['title']}")
                    print(f"   分类: {knowledge['category']}")
                    print(f"   相关度: {knowledge.get('relevance_score', 0):.3f}")
                    print(f"   内容: {knowledge['content'][:200]}{'...' if len(knowledge['content']) > 200 else ''}")
                    if knowledge.get('tags'):
                        print(f"   标签: {', '.join(knowledge['tags'])}")
            else:
                print("📭 未找到相关知识")

        except Exception as e:
            print(f"❌ 搜索知识失败: {e}")

    def list_knowledge(self):
        """列出知识"""
        print("\n📋 列出知识")
        print("-" * 30)

        category = self.get_user_input("分类筛选 (可选): ") or None
        limit_input = self.get_user_input("数量限制 (默认: 50): ") or "50"

        try:
            limit = int(limit_input)
        except ValueError:
            limit = 50

        try:
            knowledge_list = self.knowledge_manager.list_knowledge(
                category=category,
                limit=limit
            )

            if knowledge_list:
                print(f"\n📚 知识列表 (共 {len(knowledge_list)} 条):")
                for i, knowledge in enumerate(knowledge_list, 1):
                    print(f"\n{i}. {knowledge['title']}")
                    print(f"   ID: {knowledge['id']}")
                    print(f"   分类: {knowledge['category']}")
                    print(f"   内容长度: {knowledge['content_length']} 字符")
                    if knowledge.get('tags'):
                        print(f"   标签: {', '.join(knowledge['tags'])}")
                    if knowledge.get('created_at'):
                        print(f"   创建时间: {knowledge['created_at']}")
            else:
                print("📭 知识库为空")

        except Exception as e:
            print(f"❌ 列出知识失败: {e}")

    def update_knowledge(self):
        """更新知识"""
        print("\n✏️ 更新知识")
        print("-" * 30)

        knowledge_id = self.get_user_input("知识ID: ")
        if not knowledge_id:
            print("❌ 知识ID不能为空")
            return

        title = self.get_user_input("新标题 (可选): ") or None
        content_input = self.get_user_input("是否更新内容? (y/N): ").lower()
        content = None
        if content_input == 'y':
            content = self.get_multiline_input("新内容:")

        category = self.get_user_input("新分类 (可选): ") or None
        tags_input = self.get_user_input("新标签 (用逗号分隔，可选): ") or None
        tags = None
        if tags_input:
            tags = [tag.strip() for tag in tags_input.split(',') if tag.strip()]

        try:
            success = asyncio.run(self.knowledge_manager.update_knowledge(
                knowledge_id=knowledge_id,
                title=title,
                content=content,
                category=category,
                tags=tags
            ))

            if success:
                print("✅ 知识更新成功")
            else:
                print("❌ 知识更新失败")

        except Exception as e:
            print(f"❌ 更新知识失败: {e}")

    def delete_knowledge(self):
        """删除知识"""
        print("\n🗑️ 删除知识")
        print("-" * 30)

        knowledge_id = self.get_user_input("知识ID: ")
        if not knowledge_id:
            print("❌ 知识ID不能为空")
            return

        confirm = self.get_user_input(f"确认删除知识 {knowledge_id}? (y/N): ").lower()
        if confirm != 'y':
            print("已取消删除")
            return

        try:
            success = asyncio.run(self.knowledge_manager.delete_knowledge(knowledge_id))
            if success:
                print("✅ 知识删除成功")
            else:
                print("❌ 知识删除失败")

        except Exception as e:
            print(f"❌ 删除知识失败: {e}")

    def configure_settings(self):
        """配置设置"""
        print("\n⚙️ 配置设置")
        print("-" * 30)

        print("当前配置:")
        print(json.dumps(self.config, indent=2, ensure_ascii=False))

        print("\n配置项目:")
        print("1. Embedding API Key")
        print("2. Embedding Base URL")
        print("3. Embedding Model")
        print("4. Reranker Model")
        print("5. Reranker Base URL")
        print("6. Reranker API Key")
        print("7. 数据库路径")
        print("8. 文本分块大小")
        print("0. 返回")

        choice = self.get_user_input("选择要配置的项目: ")

        config_map = {
            "1": ("embedding", "api_key"),
            "2": ("embedding", "base_url"),
            "3": ("embedding", "model_name"),
            "4": ("reranker", "model"),
            "5": ("reranker", "base_url"),
            "6": ("reranker", "api_key"),
            "7": ("database", "persist_directory"),
            "8": ("text_splitter", "chunk_size")
        }

        if choice in config_map:
            section, key = config_map[choice]
            current_value = self.config[section][key]
            new_value = self.get_user_input(f"当前值: {current_value}\n新值: ")

            if new_value:
                self.config[section][key] = new_value
                self.save_config()
                print("✅ 配置已更新，需要重启程序生效")

    def show_statistics(self):
        """显示统计信息"""
        print("\n📊 统计信息")
        print("-" * 30)

        try:
            if not self.knowledge_manager:
                print("❌ 知识库管理器未初始化")
                return

            # 统计各类知识的数量
            categories = {}
            total_count = 0

            knowledge_list = self.knowledge_manager.list_knowledge(limit=1000)
            for knowledge in knowledge_list:
                category = knowledge['category']
                categories[category] = categories.get(category, 0) + 1
                total_count += 1

            print(f"总知识条数: {total_count}")
            print("\n分类统计:")
            for category, count in categories.items():
                print(f"  {category}: {count} 条")

            # 显示数据库路径
            print(f"\n数据库路径: {self.config['database']['persist_directory']}")

        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")

    def run(self):
        """运行程序"""
        print("🚀 启动知识库管理程序...")

        if not self.initialize_knowledge_manager():
            print("❌ 知识库管理器初始化失败，请检查配置")
            return

        while True:
            self.print_menu()
            choice = self.get_user_input("请选择操作 (0-7): ")

            if choice == '0':
                print("👋 感谢使用，再见！")
                break
            elif choice == '1':
                self.add_knowledge()
            elif choice == '2':
                self.search_knowledge()
            elif choice == '3':
                self.list_knowledge()
            elif choice == '4':
                self.update_knowledge()
            elif choice == '5':
                self.delete_knowledge()
            elif choice == '6':
                self.configure_settings()
            elif choice == '7':
                self.show_statistics()
            else:
                print("❌ 无效选择，请重新输入")

            input("\n按回车键继续...")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='知识库管理程序')
    parser.add_argument('-c', '--config', default='knowledge_config.json',
                       help='配置文件路径 (默认: knowledge_config.json)')
    args = parser.parse_args()

    admin = KnowledgeAdmin(config_file=args.config)
    admin.run()

if __name__ == "__main__":
    main()