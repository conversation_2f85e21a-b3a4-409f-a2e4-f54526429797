# 时间戳验证不严格导致签名验证绕过漏洞

## 漏洞描述

在 `core/file/helpers.py` 文件中的三个签名验证函数（`verify_plugin_file_signature`、`verify_image_signature` 和 `verify_file_signature`）中，时间戳验证逻辑不严格，可能导致签名验证绕过漏洞。

## 漏洞位置

**文件**: `api/core/file/helpers.py`

**受影响函数**:
1. `verify_plugin_file_signature` (第41-57行)
2. `verify_image_signature` (第60-71行)
3. `verify_file_signature` (第74-85行)

## 漏洞代码

```python
# 在所有三个函数中都有类似的时间戳验证逻辑
current_time = int(time.time())
return current_time - int(timestamp) <= dify_config.FILES_ACCESS_TIMEOUT
```

其中 `FILES_ACCESS_TIMEOUT` 的默认值为 300 秒（5分钟），定义在 `api/configs/feature/__init__.py` 中：

```python
FILES_ACCESS_TIMEOUT: int = Field(
    description="Expiration time in seconds for file access URLs",
    default=300,
)
```

## 漏洞分析

### 问题所在

当前的时间戳验证逻辑只检查时间戳是否在当前时间之前的 300 秒内，但没有以下安全检查：
1. 不检查时间戳是否在当前时间之后（未来时间戳）
2. 不检查时间戳是否过于陈旧（只检查上限，不检查下限）
3. 没有考虑服务器时间不同步的情况

### 潜在攻击场景

1. **未来时间戳攻击**：
   - 攻击者可以获取一个有效的签名 URL
   - 修改时间戳为一个未来的值，只要满足 `current_time - int(timestamp) <= 300`
   - 例如，如果当前时间是 1000，攻击者可以使用时间戳 1300（未来 300 秒），验证仍然会通过

2. **服务器时间不同步攻击**：
   - 如果服务器时间不正确，比实际时间慢，攻击者可以使用一个相对于实际时间已经过期的签名
   - 如果服务器时间比实际时间快，攻击者可以使用一个相对于实际时间还是未来的签名

3. **时间戳重放攻击**：
   - 攻击者可以捕获一个有效的签名和时间戳
   - 在稍后的时间重放这个请求，只要时间差不超过 300 秒，验证仍然会通过

### 影响范围

这些签名验证函数用于以下关键功能：
1. **插件文件上传**：`verify_plugin_file_signature` 在 `api/controllers/files/upload.py` 中用于验证插件文件上传请求
2. **图像预览**：`verify_image_signature` 在 `api/services/file_service.py` 中用于验证图像预览请求
3. **文件预览**：`verify_file_signature` 在 `api/services/file_service.py` 中用于验证文件预览请求

如果时间戳验证被绕过，攻击者可能能够：
1. 访问未经授权的文件
2. 上传恶意文件到系统
3. 导致信息泄露，特别是如果文件包含敏感信息

## 利用方法

攻击者可以通过以下步骤利用此漏洞：

1. 获取一个有效的签名 URL（通过正常使用系统或通过其他方式获取）
2. 修改时间戳参数为一个未来值，确保满足 `current_time - int(timestamp) <= 300`
3. 重放请求，访问未经授权的资源

例如，如果原始 URL 是：
```
https://example.com/files/1234/file-preview?timestamp=1000&nonce=abc&sign=xyz
```

攻击者可以将其修改为：
```
https://example.com/files/1234/file-preview?timestamp=1300&nonce=abc&sign=xyz
```

只要当前时间不超过 1300 + 300 = 1600，这个请求就会被认为是有效的。

## 修复建议

建议修改时间戳验证逻辑，增加以下安全检查：

```python
def verify_timestamp(timestamp: str) -> bool:
    current_time = int(time.time())
    timestamp_int = int(timestamp)
    
    # 检查时间戳是否在合理范围内（既不能是未来时间，也不能过于陈旧）
    time_diff = current_time - timestamp_int
    
    # 允许的时间差范围：-60 秒到 300 秒
    # -60 秒是为了考虑时钟轻微不同步的情况
    return -60 <= time_diff <= dify_config.FILES_ACCESS_TIMEOUT
```

然后在所有三个签名验证函数中使用这个函数：

```python
def verify_file_signature(*, upload_file_id: str, timestamp: str, nonce: str, sign: str) -> bool:
    data_to_sign = f"file-preview|{upload_file_id}|{timestamp}|{nonce}"
    secret_key = dify_config.SECRET_KEY.encode()
    recalculated_sign = hmac.new(secret_key, data_to_sign.encode(), hashlib.sha256).digest()
    recalculated_encoded_sign = base64.urlsafe_b64encode(recalculated_sign).decode()

    # verify signature
    if sign != recalculated_encoded_sign:
        return False

    # verify timestamp with stricter validation
    return verify_timestamp(timestamp)
```

此外，建议：
1. 实现服务器时间同步机制，确保服务器时间准确
2. 考虑使用一次性令牌或随机数来防止重放攻击
3. 在日志中记录时间戳验证失败的情况，以便检测潜在的攻击
4. 考虑增加速率限制，防止暴力破解签名

## 严重性评估

**严重性**: 中等

**理由**:
1. 攻击者需要先获取一个有效的签名，这限制了攻击范围
2. 时间窗口有限（最多 300 秒），限制了攻击的可持续性
3. 但是，如果攻击成功，可能导致未授权访问敏感文件，影响数据保密性

## 结论

时间戳验证不严格是一个确实存在的安全漏洞，可能导致签名验证被绕过。建议按照上述修复建议尽快修复此漏洞，以提高系统的安全性。

---
*报告生成时间: 2025-08-21 12:13:27*