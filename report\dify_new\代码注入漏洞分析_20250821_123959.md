# 代码注入漏洞分析报告

## 漏洞概述

在Dify工作流和模型运行时模块中，发现了一个潜在的代码注入漏洞，该漏洞位于工作流的代码执行节点（Code Node）中。攻击者可能通过构造恶意的代码输入，在服务器端执行任意代码，导致远程代码执行（RCE）。

## 漏洞位置

漏洞位于以下文件中：
- `core/workflow/nodes/code/code_node.py`
- `core/helper/code_executor/code_executor.py`
- `core/helper/code_executor/template_transformer.py`

## 漏洞详情

### 1. 代码执行节点分析

在`core/workflow/nodes/code/code_node.py`文件中，`CodeNode`类允许用户在工作流中执行自定义代码。该节点支持Python3、JavaScript和Jinja2三种语言的代码执行。

```python
def _run(self) -> NodeRunResult:
    # Get code language
    code_language = self._node_data.code_language
    code = self._node_data.code
    
    # Get variables
    variables = {}
    for variable_selector in self._node_data.variables:
        variable_name = variable_selector.variable
        variable = self.graph_runtime_state.variable_pool.get(variable_selector.value_selector)
        if isinstance(variable, ArrayFileSegment):
            variables[variable_name] = [v.to_dict() for v in variable.value] if variable.value else None
        else:
            variables[variable_name] = variable.to_object() if variable else None
    # Run code
    try:
        result = CodeExecutor.execute_workflow_code_template(
            language=code_language,
            code=code,
            inputs=variables,
        )
        
        # Transform result
        result = self._transform_result(result=result, output_schema=self._node_data.outputs)
    except (CodeExecutionError, CodeNodeError) as e:
        return NodeRunResult(
            status=WorkflowNodeExecutionStatus.FAILED, inputs=variables, error=str(e), error_type=type(e).__name__
        )
    
    return NodeRunResult(status=WorkflowNodeExecutionStatus.SUCCEEDED, inputs=variables, outputs=result)
```

### 2. 代码执行器分析

在`core/helper/code_executor/code_executor.py`文件中，`CodeExecutor`类负责执行代码。代码通过HTTP请求发送到外部沙箱服务执行。

```python
@classmethod
def execute_workflow_code_template(cls, language: CodeLanguage, code: str, inputs: Mapping[str, Any]):
    """
    Execute code
    :param language: code language
    :param code: code
    :param inputs: inputs
    :return:
    """
    template_transformer = cls.code_template_transformers.get(language)
    if not template_transformer:
        raise CodeExecutionError(f"Unsupported language {language}")
    
    runner, preload = template_transformer.transform_caller(code, inputs)
    
    try:
        response = cls.execute_code(language, preload, runner)
    except CodeExecutionError as e:
        raise e
    
    return template_transformer.transform_response(response)
```

### 3. 代码模板转换器分析

在`core/helper/code_executor/python3/python3_transformer.py`文件中，Python3代码被转换为可执行的脚本。

```python
class Python3TemplateTransformer(TemplateTransformer):
    @classmethod
    def get_runner_script(cls) -> str:
        runner_script = dedent(f"""
            # declare main function
            {cls._code_placeholder}
            
            import json
            from base64 import b64decode
            
            # decode and prepare input dict
            inputs_obj = json.loads(b64decode('{cls._inputs_placeholder}').decode('utf-8'))
            
            # execute main function
            output_obj = main(**inputs_obj)
            
            # convert output to json and print
            output_json = json.dumps(output_obj, indent=4)
            result = f'''<<RESULT>>{{output_json}}<<RESULT>>'''
            print(result)
            """)
        return runner_script
```

### 4. 漏洞点分析

漏洞的关键点在于代码执行节点没有对用户输入的代码进行充分的安全检查和过滤。虽然代码是在外部沙箱服务中执行，但如果攻击者能够绕过沙箱的限制，仍然可能导致服务器端代码执行。

特别是在`core/helper/code_executor/template_transformer.py`文件中的`assemble_runner_script`方法，直接将用户提供的代码插入到执行脚本中：

```python
@classmethod
def assemble_runner_script(cls, code: str, inputs: Mapping[str, Any]) -> str:
    # assemble runner script
    script = cls.get_runner_script()
    script = script.replace(cls._code_placeholder, code)
    inputs_str = cls.serialize_inputs(inputs)
    script = script.replace(cls._inputs_placeholder, inputs_str)
    return script
```

## 漏洞利用条件

1. 攻击者需要能够创建或修改工作流，并添加代码执行节点。
2. 攻击者需要在代码执行节点中输入恶意的Python3、JavaScript或Jinja2代码。
3. 沙箱服务需要存在可以被绕过的限制或漏洞。

## 漏洞影响

如果攻击者成功利用此漏洞，可能会：

1. 在服务器上执行任意代码，导致远程代码执行（RCE）。
2. 读取、修改或删除服务器上的敏感文件。
3. 执行系统命令，可能导致服务器完全被控制。
4. 窃取敏感数据，如数据库凭证、API密钥等。
5. 作为跳板，进一步攻击内网其他系统。

## 修复建议

1. **代码审查和过滤**：对用户输入的代码进行严格的安全审查和过滤，禁止使用危险的函数和模块。
2. **沙箱强化**：加强代码执行沙箱的安全限制，防止沙箱逃逸。
3. **权限隔离**：代码执行应在最低权限的环境中运行，避免使用root或管理员权限。
4. **资源限制**：对代码执行使用的CPU、内存、网络等资源进行限制，防止资源耗尽攻击。
5. **白名单机制**：只允许使用安全的函数和模块，其他一律禁止。
6. **超时设置**：为代码执行设置合理的超时时间，防止长时间运行的代码导致服务不可用。
7. **输入验证**：对所有输入变量进行严格的验证和过滤，防止注入攻击。
8. **日志记录**：记录所有代码执行的详细信息，便于安全审计和事件响应。

## 结论

代码注入漏洞是Dify工作流和模型运行时模块中的一个严重安全漏洞，可能导致服务器被完全控制。建议立即采取措施修复此漏洞，并对所有使用代码执行节点的工作流进行安全审查。

---
*报告生成时间: 2025-08-21 12:39:59*