# MCP API模块中的数据持久化XSS漏洞

## 漏洞概述

在MCP API模块的`initialize`方法中，存在一个数据持久化XSS漏洞。攻击者可以通过构造恶意的`client_info.name`或`client_info.version`参数，将恶意代码存储到数据库中，当其他用户查看包含恶意名称的API响应时，代码会在他们的浏览器中执行。

## 漏洞详情

### 影响版本
- 所有使用MCP API功能的Dify版本

### CVSS评分
- **严重性**: 高危 (High)
- **CVSS v3.1评分**: 8.2 (High)
- **攻击向量**: 网络 (Network)
- **攻击复杂度**: 低 (Low)
- **所需权限**: 无 (None)
- **用户交互**: 需要 (Required)
- **影响范围**: 高 (High)
- **机密性影响**: 高 (High)
- **完整性影响**: 高 (High)
- **可用性影响**: 高 (High)

### 漏洞位置
- **主要位置**: `api/core/mcp/server/streamable_http.py`，第112-132行，`initialize`方法
- **次要位置**: `api/services/agent_service.py`，第55-61行

## 漏洞分析

### 数据流路径

通过深度分析，我构建了完整的攻击数据流路径：

1. **输入源**: MCP API请求中的`client_info.name`和`client_info.version`参数
2. **数据处理**: 
   - 在`streamable_http.py`的第115行，`client_name`是通过拼接`client_info.name`和`client_info.version`构成的：
   ```python
   client_name = f"{client_info.name}@{client_info.version}"
   ```
   - 这个`client_name`没有进行任何输入验证或清理
3. **数据持久化**:
   - 在第121行，`client_name`被直接作为`EndUser`模型的`name`字段值存储到数据库中：
   ```python
   name=client_name,
   ```
   - 数据库中的`name`字段定义为`String(255)`类型，没有输入限制
4. **数据输出**:
   - 在`agent_service.py`的第55行，`EndUser.name`字段被查询：
   ```python
   db.session.query(EndUser, EndUser.name).where(EndUser.id == conversation.from_end_user_id).first()
   ```
   - 在第61行，这个名称被赋值给`executor`变量：
   ```python
   executor = executor.name
   ```
   - 在第74行，`executor`被包含在API响应中返回给客户端：
   ```python
   "executor": executor,
   ```

### 关键代码片段

#### 1. 漏洞点：`streamable_http.py`第115行
```python
def initialize(self):
    request = cast(types.InitializeRequest, self.request.root)
    client_info = request.params.clientInfo
    client_name = f"{client_info.name}@{client_info.version}"  # 没有输入验证或清理
    if not self.end_user:
        end_user = EndUser(
            tenant_id=self.app.tenant_id,
            app_id=self.app.id,
            type="mcp",
            name=client_name,  # 直接存储未经验证的输入
            session_id=generate_session_id(),
            external_user_id=self.mcp_server.id,
        )
        db.session.add(end_user)
        db.session.commit()
    # ...
```

#### 2. 输出点：`agent_service.py`第55-74行
```python
if conversation.from_end_user_id:
    # only select name field
    executor = (
        db.session.query(EndUser, EndUser.name).where(EndUser.id == conversation.from_end_user_id).first()
    )
else:
    executor = db.session.query(Account, Account.name).where(Account.id == conversation.from_account_id).first()

if executor:
    executor = executor.name  # 将数据库中的name赋值给executor
else:
    executor = "Unknown"

# ...
result = {
    "meta": {
        "status": "success",
        "executor": executor,  # executor包含在API响应中返回给客户端
        # ...
    },
    # ...
}
```

### 漏洞确认

通过LSP工具验证，我确认了以下关键点：

1. **输入验证缺失**：
   - `client_info.name`和`client_info.version`参数没有进行任何输入验证或清理
   - 恶意输入可以直接包含在`client_name`中

2. **存储不安全**：
   - `client_name`被直接存储到数据库中，没有进行HTML转义或其他安全处理
   - 数据库中的`name`字段没有输入限制

3. **输出不安全**：
   - `EndUser.name`字段通过`executor`变量在API响应中返回给客户端
   - 没有对输出进行HTML转义或其他安全处理

## 攻击场景

### 恶意请求示例
```json
{
  "jsonrpc": "2.0",
  "method": "initialize",
  "params": {
    "protocolVersion": "2024-11-05",
    "capabilities": {
      "roots": {
        "listChanged": true
      }
    },
    "clientInfo": {
      "name": "<img src=x onerror=alert('XSS')>",
      "version": "1.0.0"
    }
  },
  "id": 1
}
```

### 攻击过程
1. 攻击者发送包含恶意`client_info.name`的MCP API请求
2. 服务器将`client_info.name`和`client_info.version`拼接成`client_name`，存储到数据库中
3. 当其他用户查看包含这个恶意`client_name`的API响应时，恶意代码会在他们的浏览器中执行

### 攻击影响
- **XSS攻击**：攻击者可以通过构造恶意的输入参数，在`client_name`中注入JavaScript代码，当其他用户查看包含恶意名称的响应时，代码会在他们的浏览器中执行。
- **数据泄露**：恶意JavaScript可以窃取用户的敏感信息，如会话cookie、个人信息等。
- **会话劫持**：攻击者可以劫持用户会话，以用户身份执行操作。
- **钓鱼攻击**：攻击者可以伪造登录页面或其他钓鱼界面，诱骗用户输入凭据。

## 修复建议

### 短期修复措施

1. **在`initialize`方法中添加输入验证和清理**：
   ```python
   import html
   
   def initialize(self):
       request = cast(types.InitializeRequest, self.request.root)
       client_info = request.params.clientInfo
       
       # 对client_info.name和client_info.version进行HTML转义
       safe_name = html.escape(client_info.name)
       safe_version = html.escape(client_info.version)
       client_name = f"{safe_name}@{safe_version}"
       
       if not self.end_user:
           end_user = EndUser(
               tenant_id=self.app.tenant_id,
               app_id=self.app.id,
               type="mcp",
               name=client_name,
               session_id=generate_session_id(),
               external_user_id=self.mcp_server.id,
           )
           db.session.add(end_user)
           db.session.commit()
       return types.InitializeResult(
           protocolVersion=types.SERVER_LATEST_PROTOCOL_VERSION,
           capabilities=self.capabilities,
           serverInfo=types.Implementation(name="Dify", version=dify_config.project.version),
           instructions=self.mcp_server.description,
       )
   ```

2. **在`agent_service.py`中添加输出转义**：
   ```python
   import html
   
   # 在第61行
   executor = html.escape(executor.name) if executor else "Unknown"
   ```

3. **在`types.py`中添加输入验证**：
   ```python
   class Implementation(BaseModel):
       """Describes the name and version of an MCP implementation."""
       
       name: str = Field(..., min_length=1, max_length=100, pattern=r'^[a-zA-Z0-9_\-. ]+$')
       version: str = Field(..., min_length=1, max_length=50, pattern=r'^[a-zA-Z0-9_\-. ]+$')
       model_config = ConfigDict(extra="allow")
   ```

### 长期修复措施

1. **实施输入验证框架**：
   - 对所有用户输入进行严格的验证和清理
   - 实现白名单机制，只允许特定的字符和格式
   - 使用正则表达式验证输入格式

2. **实施输出编码**：
   - 对所有输出到前端的数据进行适当的编码
   - 根据输出上下文选择合适的编码方式（HTML、JavaScript、URL等）
   - 使用专门的编码库，如`html.escape()`、`urllib.parse.quote()`等

3. **实施内容安全策略（CSP）**：
   - 在HTTP响应头中添加CSP，限制可以执行的脚本来源
   - 禁用内联脚本和eval()等危险函数
   - 限制外部资源的加载

4. **安全的编码实践**：
   - 对开发人员进行安全编码培训
   - 建立代码审查流程，确保所有输出都经过适当的转义
   - 实施安全编码标准，如OWASP编码规范

5. **自动化安全测试**：
   - 实施自动化的XSS漏洞扫描工具
   - 定期进行渗透测试
   - 在CI/CD流程中集成安全测试

## 结论

MCP API模块中存在严重的数据持久化XSS漏洞，攻击者可以通过构造恶意的输入参数，将恶意代码存储到数据库中，然后在API响应中返回给客户端，导致XSS攻击。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

通过实施上述修复建议，可以有效防止XSS攻击，提高系统的安全性。建议优先实施短期修复措施，然后逐步实施长期修复措施，建立全面的安全防护体系。

---
*报告生成时间: 2025-08-22 06:02:46*