# 用户认证绕过漏洞报告

## 漏洞概述

在`get_user_tenant`装饰器中，当`user_id`为空时，系统会自动将其设置为"DEFAULT-USER"，并创建一个匿名的`EndUser`对象。这种设计结合固定的默认API密钥，导致了一个严重的安全漏洞，允许攻击者绕过正常的用户认证机制，访问所有使用该装饰器的内部API端点。

## 漏洞位置

- **主要文件**: `api/controllers/inner_api/plugin/wraps.py`
- **关键函数**: `get_user_tenant`装饰器和`get_user`函数
- **配置文件**: `api/configs/feature/__init__.py`

## 漏洞详情

### 1. 自动用户设置漏洞

在`get_user_tenant`装饰器中（第65-66行），当`user_id`为空时，系统会自动将其设置为"DEFAULT-USER"：

```python
if not user_id:
    user_id = "DEFAULT-USER"
```

这种设计允许攻击者通过不提供`user_id`或提供空值来绕过用户认证。

### 2. 匿名用户创建漏洞

在`get_user`函数中（第24-35行），当`user_id`为"DEFAULT-USER"时，系统会创建一个匿名用户：

```python
if user_id == "DEFAULT-USER":
    user_model = session.query(EndUser).where(EndUser.session_id == "DEFAULT-USER").first()
    if not user_model:
        user_model = EndUser(
            tenant_id=tenant_id,
            type="service_api",
            is_anonymous=True if user_id == "DEFAULT-USER" else False,
            session_id=user_id,
        )
        session.add(user_model)
        session.commit()
        session.refresh(user_model)
```

这个匿名用户（`is_anonymous=True`）仍然可以访问所有使用`get_user_tenant`装饰器的API端点，没有任何额外的权限检查。

### 3. 固定API密钥漏洞

在`plugin_inner_api_only`装饰器中（`api/controllers/inner_api/wraps.py`第72-74行），系统通过检查请求头中的"X-Inner-Api-Key"是否与配置中的`INNER_API_KEY_FOR_PLUGIN`值匹配来验证访问权限：

```python
inner_api_key = request.headers.get("X-Inner-Api-Key")
if not inner_api_key or inner_api_key != dify_config.INNER_API_KEY_FOR_PLUGIN:
    abort(404)
```

然而，`INNER_API_KEY_FOR_PLUGIN`的默认值为"inner-api-key"（`api/configs/feature/__init__.py`第165行），这是一个固定的、公开的默认值：

```python
INNER_API_KEY_FOR_PLUGIN: str = Field(description="Inner api key for plugin", default="inner-api-key")
```

攻击者可以轻易获取这个默认值，从而通过API密钥验证。

## 数据流分析

1. **攻击者发送请求**：攻击者向使用`get_user_tenant`装饰器的API端点发送请求，不提供`user_id`或提供空值，并在请求头中包含"X-Inner-Api-Key: inner-api-key"。

2. **API密钥验证**：`plugin_inner_api_only`装饰器验证请求头中的"X-Inner-Api-Key"是否与配置中的`INNER_API_KEY_FOR_PLUGIN`值匹配。由于使用了默认值"inner-api-key"，验证通过。

3. **用户ID处理**：`get_user_tenant`装饰器检测到`user_id`为空，自动将其设置为"DEFAULT-USER"。

4. **用户对象创建**：`get_user`函数检测到`user_id`为"DEFAULT-USER"，创建一个匿名的`EndUser`对象（`is_anonymous=True`）。

5. **API执行**：API端点使用这个匿名用户对象执行操作，没有额外的权限检查。

## 影响范围

所有使用`get_user_tenant`装饰器的API端点都受此漏洞影响，包括：

- LLM调用（`/invoke/llm`）
- 文本嵌入（`/invoke/text-embedding`）
- 重排序（`/invoke/rerank`）
- 文本转语音（`/invoke/tts`）
- 语音转文本（`/invoke/speech2text`）
- 内容审查（`/invoke/moderation`）
- 工具调用（`/invoke/tool`）
- 应用调用（`/invoke/app`）
- 加密解密（`/invoke/encrypt`）
- 摘要生成（`/invoke/summary`）
- 文件上传（`/upload/file/request`）
- 应用信息获取（`/fetch/app/info`）

## 漏洞利用概念

攻击者可以通过以下步骤利用此漏洞：

1. 获取目标系统的API端点URL。
2. 构造HTTP请求，不包含`user_id`或将其设置为空值。
3. 在请求头中添加"X-Inner-Api-Key: inner-api-key"。
4. 发送请求到目标API端点。

示例请求：

```bash
curl -X POST "https://target-domain/v1/plugin/invoke/llm" \
  -H "Content-Type: application/json" \
  -H "X-Inner-Api-Key: inner-api-key" \
  -d '{
    "tenant_id": "target-tenant-id",
    "user_id": "",
    "provider": "openai",
    "model": "gpt-3.5-turbo",
    "model_type": "llm",
    "prompt_messages": [{"role": "user", "content": "Hello"}],
    "completion_params": {}
  }'
```

## 风险评估

- **严重性**: 高
- **CVSS评分**: 9.1 (Critical)
- **影响范围**: 所有使用内部插件API的系统
- **利用难度**: 低
- **检测难度**: 中等

## 修复建议

### 1. 修改INNER_API_KEY_FOR_PLUGIN的默认值

将`INNER_API_KEY_FOR_PLUGIN`的默认值从"inner-api-key"修改为空字符串，强制用户在部署时设置自定义值：

```python
INNER_API_KEY_FOR_PLUGIN: str = Field(description="Inner api key for plugin", default="")
```

### 2. 强制验证user_id

修改`get_user_tenant`装饰器，不允许`user_id`为空或"DEFAULT-USER"：

```python
if not user_id:
    raise ValueError("user_id is required")

if user_id == "DEFAULT-USER":
    raise ValueError("invalid user_id")
```

### 3. 添加额外的认证机制

实现多层认证机制，如：

- IP白名单限制
- TLS客户端证书认证
- 请求签名验证
- 时间戳验证

### 4. 实现细粒度的权限控制

为不同的API端点定义不同的权限要求，例如：

- 对敏感操作（如文件上传、应用调用）要求额外的权限验证
- 对资源密集型操作（如LLM调用）实施速率限制
- 对匿名用户实施严格的权限限制

### 5. 添加审计日志

记录所有内部API的访问日志，包括：

- 请求时间戳
- 请求来源IP
- 请求用户ID
- 请求API端点
- 请求参数
- 响应状态码

实现实时监控和告警机制，检测异常访问模式。

### 6. 实施安全部署指南

提供详细的安全部署指南，包括：

- 如何生成和配置强密码作为API密钥
- 如何定期轮换API密钥
- 如何配置网络访问控制
- 如何启用审计日志

## 结论

`get_user_tenant`装饰器中的"DEFAULT-USER"默认用户设计存在严重的安全漏洞，允许攻击者绕过用户认证机制，访问所有使用该装饰器的内部API端点。此漏洞结合固定的默认API密钥，形成了一个严重的安全风险。建议立即实施上述修复建议，以保护系统安全。

---
*报告生成时间: 2025-08-21 23:48:51*