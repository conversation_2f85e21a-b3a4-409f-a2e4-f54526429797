# 路径遍历漏洞分析报告

## 漏洞概述

在 `services/file_service.py` 的 `upload_file` 方法中发现了潜在的路径遍历漏洞。该漏洞可能允许攻击者通过构造特制的文件名，将文件写入到预期的存储目录之外的位置，可能导致敏感信息泄露或系统文件被覆盖。

## 漏洞详情

### 1. 受影响代码位置

文件：`api/services/file_service.py`
方法：`FileService.upload_file`
关键行：第43-67行

```python
def upload_file(
    *,
    filename: str,
    content: bytes,
    mimetype: str,
    user: Union[Account, EndUser, Any],
    source: Literal["datasets"] | None = None,
    source_url: str = "",
) -> UploadFile:
    # get file extension
    extension = os.path.splitext(filename)[1].lstrip(".").lower()

    # check if filename contains invalid characters
    if any(c in filename for c in ["/", "\\", ":", "*", "?", '"', "<", ">", "|"]):
        raise ValueError("Filename contains invalid characters")

    if len(filename) > 200:
        filename = filename.split(".")[0][:200] + "." + extension

    if source == "datasets" and extension not in DOCUMENT_EXTENSIONS:
        raise UnsupportedFileTypeError()

    # get file size
    file_size = len(content)

    # check if the file size is exceeded
    if not FileService.is_file_size_within_limit(extension=extension, file_size=file_size):
        raise FileTooLargeError

    # generate file key
    file_uuid = str(uuid.uuid4())

    current_tenant_id = extract_tenant_id(user)

    file_key = "upload_files/" + (current_tenant_id or "") + "/" + file_uuid + "." + extension

    # save file to storage
    storage.save(file_key, content)
```

### 2. 漏洞分析

#### 2.1 文件名检查逻辑不充分

在第46行，代码检查文件名是否包含一些无效字符：
```python
if any(c in filename for c in ["/", "\\", ":", "*", "?", '"', "<", ">", "|"]):
    raise ValueError("Filename contains invalid characters")
```

然而，这个检查存在以下问题：

1. **没有检查路径遍历序列**：检查列表中不包含 `..` 或 `../` 这样的路径遍历序列，攻击者可以利用这些序列进行目录遍历攻击。

2. **检查顺序问题**：在获取文件扩展名之后才进行文件名检查，这意味着如果文件名包含路径遍历字符，扩展名可能已经被错误地提取。

#### 2.2 文件存储路径构建不安全

在第67行，代码构建文件存储路径：
```python
file_key = "upload_files/" + (current_tenant_id or "") + "/" + file_uuid + "." + extension
```

这个构建过程存在以下问题：

1. **使用字符串拼接而非路径构建函数**：使用字符串拼接而非 `os.path.join` 来构建路径，这可能导致路径分隔符不一致和路径遍历问题。

2. **没有对 extension 进行充分验证**：虽然对文件名进行了部分检查，但对 extension 变量没有进行充分验证。如果 extension 包含路径遍历字符（如 `../../`），最终的文件路径可能会指向预期之外的位置。

#### 2.3 存储实现缺乏路径验证

在 `api/extensions/storage/opendal_storage.py` 中，`OpenDALStorage.save` 方法直接将文件名传递给底层存储实现，没有进行额外的路径验证：

```python
def save(self, filename: str, data: bytes) -> None:
    self.op.write(path=filename, bs=data)
    logger.debug("file %s saved", filename)
```

对于本地存储（fs方案），这可能导致文件被写入到预期的存储目录之外。

### 3. 漏洞利用场景

攻击者可以通过以下步骤利用此漏洞：

1. 构造一个特制的文件名，例如 `normal.txt../../malicious.txt`，其中包含路径遍历序列。
2. 虽然文件名检查会阻止包含 `/` 或 `\` 的文件名，但攻击者可以使用 URL 编码或其他编码方式绕过检查。
3. 如果文件名检查被绕过，`extension` 变量可能会被设置为包含路径遍历序列的值。
4. 最终的文件路径可能会指向预期存储目录之外的位置，导致文件被写入到不安全的位置。

### 4. 潜在影响

1. **信息泄露**：攻击者可能能够读取系统上的敏感文件。
2. **文件覆盖**：攻击者可能能够覆盖系统上的重要文件，导致系统功能受损。
3. **拒绝服务**：通过写入大量文件或覆盖关键文件，攻击者可能导致系统不可用。
4. **权限提升**：在某些情况下，攻击者可能能够利用此漏洞获得更高的系统权限。

### 5. 修复建议

#### 5.1 加强文件名验证

```python
# 检查文件名是否包含路径遍历序列
if ".." in filename or filename.startswith("/") or filename.startswith("\\"):
    raise ValueError("Filename contains path traversal sequences")

# 检查文件名是否包含无效字符
if any(c in filename for c in ["/", "\\", ":", "*", "?", '"', "<", ">", "|"]):
    raise ValueError("Filename contains invalid characters")
```

#### 5.2 安全构建文件路径

使用 `os.path.join` 和 `os.path.normpath` 来安全地构建文件路径：

```python
# 安全地构建文件路径
file_key = os.path.normpath(os.path.join("upload_files", current_tenant_id or "", f"{file_uuid}.{extension}"))

# 确保文件路径不包含遍历序列
if ".." in file_key or file_key.startswith("/") or file_key.startswith("\\"):
    raise ValueError("Invalid file path")
```

#### 5.3 对 extension 进行额外验证

```python
# 验证 extension 不包含路径遍历字符
if ".." in extension or "/" in extension or "\\" in extension:
    raise ValueError("Invalid file extension")
```

#### 5.4 在存储层添加路径验证

在 `OpenDALStorage.save` 方法中添加路径验证：

```python
def save(self, filename: str, data: bytes) -> None:
    # 规范化路径并检查是否包含遍历序列
    normalized_path = os.path.normpath(filename)
    if ".." in normalized_path or normalized_path.startswith("/") or normalized_path.startswith("\\"):
        raise ValueError("Invalid file path")
    
    self.op.write(path=filename, bs=data)
    logger.debug("file %s saved", filename)
```

### 6. 结论

`services/file_service.py` 中的 `upload_file` 方法存在路径遍历漏洞，可能导致文件被写入到预期的存储目录之外的位置。建议按照上述修复建议加强文件名验证、安全构建文件路径，并在存储层添加额外的路径验证，以防止路径遍历攻击。

---
*报告生成时间: 2025-08-21 12:00:24*