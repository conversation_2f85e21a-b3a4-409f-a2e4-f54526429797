# 角色更新操作中的权限控制漏洞

## 漏洞描述

角色更新操作中存在权限控制漏洞，API入口处没有验证当前用户是否有权限更新成员角色，而是在业务逻辑内部进行权限检查。这种设计可能导致权限提升和未授权访问风险。

## 漏洞位置

1. **主要漏洞点**：`api/controllers/console/workspace/members.py`文件中的`MemberUpdateRoleApi.put`方法（第135-156行）。
2. **权限检查点**：`api/services/account_service.py`文件中的`TenantService.check_member_permission`方法（第1055-1072行）。
3. **业务逻辑点**：`api/services/account_service.py`文件中的`TenantService.update_member_role`方法（第1090-1114行）。

## 漏洞分析

### 代码分析

#### 1. API入口处缺少权限检查

`MemberUpdateRoleApi.put`方法（第135-156行）的代码如下：

```python
class MemberUpdateRoleApi(Resource):
    """Update member role."""

    @setup_required
    @login_required
    @account_initialization_required
    def put(self, member_id):
        parser = reqparse.RequestParser()
        parser.add_argument("role", type=str, required=True, location="json")
        args = parser.parse_args()
        new_role = args["role"]

        if not TenantAccountRole.is_valid_role(new_role):
            return {"code": "invalid-role", "message": "Invalid role"}, 400

        member = db.session.get(Account, str(member_id))
        if not member:
            abort(404)

        try:
            assert member is not None, "Member not found"
            TenantService.update_member_role(current_user.current_tenant, member, new_role, current_user)
        except Exception as e:
            raise ValueError(str(e))

        # todo: 403

        return {"result": "success"}
```

从代码中可以看出：

- 该方法使用了装饰器`@setup_required`、`@login_required`和`@account_initialization_required`，但这些装饰器都没有检查当前用户是否有权限更新成员角色。
- 在方法内部，只验证了角色是否有效（第141行），但没有验证当前用户是否有权限更新成员角色。
- 方法中有一个注释"// todo: 403"（第154行），表明开发者意识到可能需要处理403 Forbidden错误，但尚未实现。
- 异常处理使用了通用的`Exception`捕获（第151行），而没有专门处理`NoPermissionError`。

#### 2. 权限检查延迟到业务逻辑内部

`TenantService.update_member_role`方法（第1090-1114行）的代码如下：

```python
@staticmethod
def update_member_role(tenant: Tenant, member: Account, new_role: str, operator: Account) -> None:
    """Update member role"""
    TenantService.check_member_permission(tenant, operator, member, "update")

    target_member_join = (
        db.session.query(TenantAccountJoin).filter_by(tenant_id=tenant.id, account_id=member.id).first()
    )

    if not target_member_join:
        raise MemberNotInTenantError("Member not in tenant.")

    if target_member_join.role == new_role:
        raise RoleAlreadyAssignedError("The provided role is already assigned to the member.")

    if new_role == "owner":
        # Find the current owner and change their role to 'admin'
        current_owner_join = (
            db.session.query(TenantAccountJoin).filter_by(tenant_id=tenant.id, role="owner").first()
        )
        if current_owner_join:
            current_owner_join.role = "admin"

    # Update the role of the target member
    target_member_join.role = new_role
    db.session.commit()
```

从代码中可以看出：

- 权限检查实际上是在`TenantService.update_member_role`方法内部进行的（第1092行），调用了`TenantService.check_member_permission`。
- 这种设计将权限检查延迟到业务逻辑内部，而不是在API入口处进行，违反了安全设计的最佳实践。

#### 3. 权限验证逻辑本身正确

`TenantService.check_member_permission`方法（第1055-1072行）的代码如下：

```python
def check_member_permission(tenant: Tenant, operator: Account, member: Account | None, action: str) -> None:
    """Check member permission"""
    perms = {
        "add": [TenantAccountRole.OWNER, TenantAccountRole.ADMIN],
        "remove": [TenantAccountRole.OWNER],
        "update": [TenantAccountRole.OWNER],
    }
    if action not in {"add", "remove", "update"}:
        raise InvalidActionError("Invalid action.")

    if member:
        if operator.id == member.id:
            raise CannotOperateSelfError("Cannot operate self.")

    ta_operator = db.session.query(TenantAccountJoin).filter_by(tenant_id=tenant.id, account_id=operator.id).first()

    if not ta_operator or ta_operator.role not in perms[action]:
        raise NoPermissionError(f"No permission to {action} member.")
```

从代码中可以看出：

- `TenantService.check_member_permission`方法定义了只有OWNER角色才能执行"update"操作。
- 权限验证逻辑本身是正确的，但是权限检查的位置不对，应该在API入口处进行检查。

#### 4. 错误处理不完整

与`MemberCancelInviteApi.delete`方法（第110-126行）相比，后者有完整的错误处理：

```python
def delete(self, member_id):
    member = db.session.query(Account).where(Account.id == str(member_id)).first()
    if member is None:
        abort(404)
    else:
        try:
            TenantService.remove_member_from_tenant(current_user.current_tenant, member, current_user)
        except services.errors.account.CannotOperateSelfError as e:
            return {"code": "cannot-operate-self", "message": str(e)}, 400
        except services.errors.account.NoPermissionError as e:
            return {"code": "forbidden", "message": str(e)}, 403
        except services.errors.account.MemberNotInTenantError as e:
            return {"code": "member-not-found", "message": str(e)}, 404
        except Exception as e:
            raise ValueError(str(e))

    return {"result": "success", "tenant_id": str(current_user.current_tenant.id)}, 200
```

从代码中可以看出：

- `MemberCancelInviteApi.delete`方法有完整的错误处理，包括对`NoPermissionError`的处理（第119-120行），返回403 Forbidden错误。
- 而`MemberUpdateRoleApi.put`方法没有专门处理`NoPermissionError`异常，可能导致不适当的错误响应。

### 数据流分析

1. **输入数据流**：
   - 用户通过PUT请求访问`/workspaces/current/members/<uuid:member_id>/update-role`端点。
   - 请求体中包含`role`参数，指定要更新的角色。

2. **处理流程**：
   - `MemberUpdateRoleApi.put`方法接收请求，解析参数。
   - 验证角色是否有效（第141行）。
   - 获取要更新的成员对象（第144行）。
   - 调用`TenantService.update_member_role`方法更新成员角色（第150行）。
   - `TenantService.update_member_role`方法调用`TenantService.check_member_permission`进行权限检查（第1092行）。
   - 如果权限检查通过，更新成员角色；否则，抛出`NoPermissionError`异常。

3. **问题点**：
   - 权限检查发生在业务逻辑内部，而不是在API入口处。
   - `MemberUpdateRoleApi.put`方法没有专门处理`NoPermissionError`异常，可能导致不适当的错误响应。

## 漏洞影响

### 1. 权限提升风险

- 如果`TenantService.update_member_role`方法被其他地方调用，可能绕过权限检查。
- 攻击者可能通过某种方式直接调用`TenantService.update_member_role`方法，而不经过`MemberUpdateRoleApi.put`方法，从而绕过权限检查。

### 2. 不一致的权限检查

- 其他类似的API可能在入口处就有权限检查，而这种不一致性可能导致安全漏洞。
- 开发人员可能会误以为所有API都在入口处有权限检查，从而在其他地方忽略权限检查。

### 3. 潜在的未授权访问风险

- 如果系统中有其他路径可以调用`TenantService.update_member_role`方法，可能存在未授权访问的风险。

### 4. 错误处理不当

- 权限检查失败时，可能返回不适当的错误响应，而不是403 Forbidden错误。

## 利用概念

### 1. 直接调用业务逻辑

如果系统中存在其他路径可以调用`TenantService.update_member_role`方法，攻击者可能通过这些路径绕过API入口处的检查。例如，如果系统中有管理后台或其他内部接口，可能直接调用业务逻辑方法，从而绕过权限检查。

### 2. 错误响应分析

攻击者可以通过分析错误响应来获取系统内部信息。如果权限检查失败时返回500内部服务器错误，而不是403 Forbidden错误，攻击者可能通过错误信息获取系统内部结构。

### 3. 权限提升

如果系统中存在漏洞允许低权限用户调用`TenantService.update_member_role`方法，攻击者可能将自己或其他用户的角色提升为更高权限的角色。

### 4. POC示例

```python
# 假设攻击者是一个已登录的普通用户（非OWNER角色）
# 攻击者尝试直接调用TenantService.update_member_role方法

import requests
import json

# 攻击者的会话信息
attacker_session = {
    "session_id": "attacker_session_id",
    "user_id": "attacker_user_id",
    "tenant_id": "target_tenant_id"
}

# 目标成员信息
target_member_id = "target_member_id"
new_role = "admin"  # 尝试将目标成员角色提升为admin

# 构造恶意请求
url = "http://example.com/api/v1/workspaces/current/members/{}/update-role".format(target_member_id)
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer attacker_token"
}
data = {
    "role": new_role
}

# 发送请求
response = requests.put(url, headers=headers, data=json.dumps(data))

# 分析响应
if response.status_code == 200:
    print("权限提升成功！")
    print("响应内容:", response.json())
elif response.status_code == 500:
    print("服务器内部错误，可能存在权限检查漏洞")
    print("错误信息:", response.text)
else:
    print("请求失败，状态码:", response.status_code)
    print("响应内容:", response.json())
```

## 修复建议

### 1. 在API入口处添加权限检查

在`MemberUpdateRoleApi.put`方法中添加权限检查，确保只有有权限的用户才能调用此API。可以创建一个新的装饰器，如`@permission_required`，用于检查用户是否有权限执行特定操作。

```python
class MemberUpdateRoleApi(Resource):
    """Update member role."""

    @setup_required
    @login_required
    @account_initialization_required
    @workspace_permission_required(WorkspacePermission.MANAGE_MEMBERS)  # 添加权限检查装饰器
    def put(self, member_id):
        parser = reqparse.RequestParser()
        parser.add_argument("role", type=str, required=True, location="json")
        args = parser.parse_args()
        new_role = args["role"]

        if not TenantAccountRole.is_valid_role(new_role):
            return {"code": "invalid-role", "message": "Invalid role"}, 400

        member = db.session.get(Account, str(member_id))
        if not member:
            abort(404)

        try:
            assert member is not None, "Member not found"
            TenantService.update_member_role(current_user.current_tenant, member, new_role, current_user)
        except services.errors.account.CannotOperateSelfError as e:
            return {"code": "cannot-operate-self", "message": str(e)}, 400
        except services.errors.account.NoPermissionError as e:
            return {"code": "forbidden", "message": str(e)}, 403  # 处理权限错误
        except services.errors.account.MemberNotInTenantError as e:
            return {"code": "member-not-found", "message": str(e)}, 404
        except services.errors.account.RoleAlreadyAssignedError as e:
            return {"code": "role-already-assigned", "message": str(e)}, 400
        except Exception as e:
            raise ValueError(str(e))

        return {"result": "success"}
```

### 2. 统一权限检查位置

确保所有API都在入口处进行权限检查，而不是在业务逻辑内部。这样可以确保权限检查的一致性，减少因权限检查位置不一致导致的安全漏洞。

### 3. 改进错误处理

在`MemberUpdateRoleApi.put`方法中添加对`NoPermissionError`的专门处理，返回403 Forbidden错误。删除"// todo: 403"注释，实现适当的错误处理。

### 4. 添加权限检查日志

在权限检查失败时，记录详细的日志，包括用户ID、操作类型、时间戳等信息。这样可以帮助安全团队监控和分析潜在的权限提升攻击。

```python
def check_member_permission(tenant: Tenant, operator: Account, member: Account | None, action: str) -> None:
    """Check member permission"""
    perms = {
        "add": [TenantAccountRole.OWNER, TenantAccountRole.ADMIN],
        "remove": [TenantAccountRole.OWNER],
        "update": [TenantAccountRole.OWNER],
    }
    if action not in {"add", "remove", "update"}:
        raise InvalidActionError("Invalid action.")

    if member:
        if operator.id == member.id:
            raise CannotOperateSelfError("Cannot operate self.")

    ta_operator = db.session.query(TenantAccountJoin).filter_by(tenant_id=tenant.id, account_id=operator.id).first()

    if not ta_operator or ta_operator.role not in perms[action]:
        # 记录权限检查失败的日志
        logging.warning(f"Permission check failed. Operator: {operator.id}, Action: {action}, Tenant: {tenant.id}")
        raise NoPermissionError(f"No permission to {action} member.")
```

### 5. 代码审查和安全测试

在代码审查过程中，特别关注权限检查的位置和逻辑。在安全测试中，特别测试权限提升和未授权访问的场景。

## 风险评估

- **严重性**：中
- **CVSS评分**：6.5 (Medium)
- **影响范围**：所有使用角色更新功能的工作区
- **利用难度**：中等
- **检测难度**：中等

## 结论

角色更新操作中存在权限控制漏洞，虽然当前实现中权限检查最终会在业务逻辑内部进行，但这种设计不够安全，可能导致权限提升和未授权访问风险。建议将权限检查移至API入口处，并实现更细粒度的权限控制，以提高系统的整体安全性。

---
*报告生成时间: 2025-08-21 20:01:04*