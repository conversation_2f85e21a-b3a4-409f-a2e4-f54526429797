## API密钥明文存储安全漏洞

### 漏洞描述
在Dify项目中，API密钥以明文形式存储在数据库中，没有进行哈希或加密处理。这构成了严重的安全风险，因为任何能够访问数据库的人都可以直接获取所有用户的API密钥，从而完全控制相应的资源。

### 漏洞详情

#### 1. API密钥生成机制

API密钥的生成过程如下：

1. **密钥格式**：API密钥由前缀和24位随机字符串组成，例如`app-xxxxxxxxxxxxxxxxxxxxxxxx`。
2. **前缀类型**：
   - App API密钥：`app-`（在`api/controllers/console/apikey.py`中定义）
   - Dataset API密钥：`ds-`（在`api/controllers/console/apikey.py`中定义）或`dataset-`（在`api/controllers/console/datasets/datasets.py`中定义）
3. **随机字符串生成**：使用`generate_string`函数（在`api/libs/helper.py`中定义）生成24位随机字符串：
   ```python
   def generate_string(n):
       letters_digits = string.ascii_letters + string.digits
       result = ""
       for i in range(n):
           result += secrets.choice(letters_digits)
       return result
   ```
   该函数使用`secrets.choice`从字母和数字的组合中随机选择字符，这是一个加密安全的随机数生成器，从随机性角度来看是安全的。

4. **唯一性检查**：生成后会检查数据库中是否已存在相同的API密钥，如果存在则重新生成：
   ```python
   @staticmethod
   def generate_api_key(prefix, n):
       while True:
           result = prefix + generate_string(n)
           if db.session.query(ApiToken).where(ApiToken.token == result).count() > 0:
               continue
           return result
   ```

#### 2. API密钥存储机制

API密钥的存储在`api/models/model.py`中的`ApiToken`模型中定义：
```python
class ApiToken(Base):
    __tablename__ = "api_tokens"
    # ...
    token: Mapped[str] = mapped_column(String(255), nullable=False)
    # ...
```

API密钥以明文形式存储在数据库的`token`字段中，没有进行哈希或加密处理。这意味着任何能够访问数据库的人都可以直接读取所有API密钥。

#### 3. API密钥验证机制

API密钥的验证在`api/controllers/service_api/wraps.py`中的`validate_and_get_api_token`函数中实现：
```python
def validate_and_get_api_token(scope: str | None = None):
    """
    Validate and get API token.
    """
    auth_header = request.headers.get("Authorization")
    if auth_header is None or " " not in auth_header:
        raise Unauthorized("Authorization header must be provided and start with 'Bearer'")

    auth_scheme, auth_token = auth_header.split(None, 1)
    auth_scheme = auth_scheme.lower()

    if auth_scheme != "bearer":
        raise Unauthorized("Authorization scheme must be 'Bearer'")

    current_time = naive_utc_now()
    cutoff_time = current_time - timedelta(minutes=1)
    with Session(db.engine, expire_on_commit=False) as session:
        update_stmt = (
            update(ApiToken)
            .where(
                ApiToken.token == auth_token,
                (ApiToken.last_used_at.is_(None) | (ApiToken.last_used_at < cutoff_time)),
                ApiToken.type == scope,
            )
            .values(last_used_at=current_time)
            .returning(ApiToken)
        )
        result = session.execute(update_stmt)
        api_token = result.scalar_one_or_none()

        if not api_token:
            stmt = select(ApiToken).where(ApiToken.token == auth_token, ApiToken.type == scope)
            api_token = session.scalar(stmt)
            if not api_token:
                raise Unauthorized("Access token is invalid")
        else:
            session.commit()

    return api_token
```

验证过程包括：
1. 从Authorization头中获取Bearer token
2. 在数据库中查询匹配的token
3. 更新last_used_at时间戳，但有一个1分钟的时间窗口限制，避免频繁更新

### 漏洞影响

1. **数据库泄露风险**：如果数据库被未授权访问，所有API密钥将直接暴露，攻击者可以获取所有用户的API密钥，从而完全控制相应的资源。

2. **内部威胁**：具有数据库访问权限的内部人员（如DBA、系统管理员）可以轻易获取所有API密钥，滥用权限。

3. **备份安全**：数据库备份文件中包含明文API密钥，如果备份文件处理不当，可能导致API密钥泄露。

4. **日志泄露**：如果数据库查询日志被记录并泄露，可能包含API密钥信息。

### 漏洞利用方式

1. **直接数据库访问**：攻击者通过SQL注入、未授权访问或其他方式获取数据库访问权限后，可以直接查询`api_tokens`表获取所有API密钥：
   ```sql
   SELECT id, token, type, app_id, tenant_id FROM api_tokens;
   ```

2. **备份文件泄露**：获取数据库备份文件后，可以恢复数据库并提取API密钥。

3. **日志分析**：通过分析数据库查询日志，可能获取API密钥信息。

### 修复建议

1. **API密钥哈希存储**：
   - 在存储API密钥时，应使用强哈希算法（如bcrypt、scrypt或Argon2）对API密钥进行哈希处理
   - 验证时，将用户提供的API密钥进行哈希后与数据库中存储的哈希值进行比较

2. **密钥轮换机制**：
   - 实现API密钥的定期轮换机制
   - 提供密钥轮换的API接口，允许用户主动轮换密钥

3. **访问控制**：
   - 限制对`api_tokens`表的直接访问
   - 实现细粒度的访问控制，确保只有授权人员才能访问API密钥

4. **审计日志**：
   - 记录API密钥的创建、使用和删除操作
   - 监控异常的API密钥使用模式

5. **加密传输**：
   - 确保API密钥在传输过程中使用HTTPS加密
   - 考虑使用客户端证书进行额外的身份验证

### 风险评估

**严重性：高**

这个漏洞的严重性很高，因为：
1. API密钥是访问系统资源的直接凭证
2. 明文存储使得任何数据库访问都可能导致密钥泄露
3. 没有额外的保护措施来减轻泄露风险

### 结论

Dify项目中的API密钥以明文形式存储在数据库中，没有进行哈希或加密处理，这构成了严重的安全风险。建议立即采取措施修复此漏洞，包括实现API密钥的哈希存储、加强访问控制和实施密钥轮换机制。

---
*报告生成时间: 2025-08-21 18:48:42*