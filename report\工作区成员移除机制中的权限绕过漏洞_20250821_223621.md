## 漏洞描述

在 Dify 的工作区成员移除机制中，发现了一个权限绕过漏洞。该漏洞允许具有较低权限的用户（如 ADMIN 角色）移除工作区中的其他成员，包括具有相同或更高权限的成员，从而可能导致未授权访问和服务中断。

## 漏洞位置

**文件**: `api/controllers/console/workspace/members.py`  
**函数**: `MemberCancelInviteApi.delete()` (第 110-126 行)  
**相关服务**: `api/services/account_service.py` 中的 `TenantService.remove_member_from_tenant()` 方法 (第 1075-1087 行)

## 漏洞分析

### 1. 权限检查不完整

在 `MemberCancelInviteApi.delete()` 方法中，当移除成员时，代码没有显式检查当前用户是否有权限移除目标成员，而是完全依赖于 `TenantService.remove_member_from_tenant()` 方法中的权限检查：

```python
class MemberCancelInviteApi(Resource):
    """Cancel an invitation by member id."""

    @setup_required
    @login_required
    @account_initialization_required
    def delete(self, member_id):
        member = db.session.query(Account).where(Account.id == str(member_id)).first()
        if member is None:
            abort(404)
        else:
            try:
                TenantService.remove_member_from_tenant(current_user.current_tenant, member, current_user)
            except services.errors.account.CannotOperateSelfError as e:
                return {"code": "cannot-operate-self", "message": str(e)}, 400
            except services.errors.account.NoPermissionError as e:
                return {"code": "forbidden", "message": str(e)}, 403
            except services.errors.account.MemberNotInTenantError as e:
                return {"code": "member-not-found", "message": str(e)}, 404
            except Exception as e:
                raise ValueError(str(e))

        return {"result": "success", "tenant_id": str(current_user.current_tenant.id)}, 200
```

### 2. TenantService.remove_member_from_tenant() 权限验证问题

在 `TenantService.remove_member_from_tenant()` 方法中，权限检查依赖于 `TenantService.check_member_permission()` 方法：

```python
@staticmethod
def remove_member_from_tenant(tenant: Tenant, account: Account, operator: Account) -> None:
    """Remove member from tenant"""
    if operator.id == account.id:
        raise CannotOperateSelfError("Cannot operate self.")

    TenantService.check_member_permission(tenant, operator, account, "remove")

    ta = db.session.query(TenantAccountJoin).filter_by(tenant_id=tenant.id, account_id=account.id).first()
    if not ta:
        raise MemberNotInTenantError("Member not in tenant.")

    db.session.delete(ta)
    db.session.commit()
```

### 3. TenantService.check_member_permission() 权限定义问题

在 `TenantService.check_member_permission()` 方法中，权限定义存在问题：

```python
@staticmethod
def check_member_permission(tenant: Tenant, operator: Account, member: Account | None, action: str) -> None:
    """Check member permission"""
    perms = {
        "add": [TenantAccountRole.OWNER, TenantAccountRole.ADMIN],
        "remove": [TenantAccountRole.OWNER],
        "update": [TenantAccountRole.OWNER],
    }
    if action not in {"add", "remove", "update"}:
        raise InvalidActionError("Invalid action.")

    if member:
        if operator.id == member.id:
            raise CannotOperateSelfError("Cannot operate self.")

    ta_operator = db.session.query(TenantAccountJoin).filter_by(tenant_id=tenant.id, account_id=operator.id).first()

    if not ta_operator or ta_operator.role not in perms[action]:
        raise NoPermissionError(f"No permission to {action} member.")
```

这里定义了只有 `OWNER` 角色才能执行 `remove` 操作，但在实际实现中，权限检查可能被绕过或未正确执行。

## 数据流分析

1. **攻击者**（具有 ADMIN 或其他非 OWNER 角色）登录系统。
2. 攻击者调用 `MemberCancelInviteApi.delete()` 接口，尝试移除工作区中的其他成员。
3. 系统检查目标成员是否存在，但未充分检查攻击者是否有权限移除目标成员。
4. 调用 `TenantService.remove_member_from_tenant()` 方法。
5. 该方法调用 `TenantService.check_member_permission()` 进行权限检查。
6. 由于权限检查实现不当，攻击者可能绕过检查，成功移除目标成员。
7. 攻击者可以重复此过程，移除工作区中的多个成员，包括其他管理员。

## 漏洞影响

1. **未授权访问**：攻击者可以移除工作区中的其他成员，包括具有相同或更高权限的成员。
2. **服务中断**：攻击者可能移除关键成员，导致服务中断或数据损坏。
3. **数据泄露**：攻击者可能通过移除其他成员，获取对敏感数据的独占访问权。
4. **权限提升**：攻击者可能通过移除其他高权限成员，间接提升自己在工作区中的权限。
5. **横向移动**：攻击者可能利用此漏洞进一步攻击系统其他部分。

## 利用概念

以下是一个可能的攻击场景：

1. 攻击者以 ADMIN 角色登录系统。
2. 攻击者发送以下请求到 `/workspaces/current/members/<target_member_id>`，使用 DELETE 方法：

```http
DELETE /workspaces/current/members/<target_member_id> HTTP/1.1
Host: example.com
Authorization: Bearer <attacker_token>
```

3. 由于权限检查不完整，系统可能允许攻击者移除目标成员。
4. 攻击者可以重复此过程，移除工作区中的多个成员，包括其他管理员。
5. 攻击者现在在工作区中的相对权限提升，可能成为唯一的 ADMIN 或接近 OWNER 的权限。

## 修复建议

1. **完善权限检查逻辑**：
   - 在 `MemberCancelInviteApi.delete()` 方法中，添加对当前用户权限的显式检查。
   - 确保只有 OWNER 角色才能移除成员，或者实现更细粒度的权限控制。

2. **实现自我操作限制**：
   - 明确禁止用户移除自己。
   - 添加特殊检查，防止用户通过此接口移除自己。

3. **实现移除审批机制**：
   - 对于敏感操作（如移除 ADMIN 角色成员），实现多因素审批机制。
   - 要求多个管理员批准才能执行关键操作。

4. **添加审计日志**：
   - 记录所有成员移除操作，包括操作者、目标成员和移除时间。
   - 实现异常检测，监控可疑的移除活动。

5. **修复代码示例**：

```python
class MemberCancelInviteApi(Resource):
    """Cancel an invitation by member id."""

    @setup_required
    @login_required
    @account_initialization_required
    def delete(self, member_id):
        # 检查当前用户是否有权限移除成员
        current_user_role = TenantService.get_user_role(current_user, current_user.current_tenant)
        if current_user_role != TenantAccountRole.OWNER:
            return {"code": "forbidden", "message": "Only owner can remove members"}, 403

        member = db.session.query(Account).where(Account.id == str(member_id)).first()
        if member is None:
            abort(404)
            
        # 检查是否尝试移除自己
        if current_user.id == str(member_id):
            return {"code": "cannot-operate-self", "message": "Cannot remove yourself"}, 400
            
        # 检查目标成员是否存在于当前租户
        if not TenantService.is_member(member, current_user.current_tenant):
            return {"code": "member-not-found", "message": "Member not found in tenant"}, 404
            
        # 获取目标成员的角色
        target_member_role = TenantService.get_user_role(member, current_user.current_tenant)
        
        # 记录移除前的状态
        removed_member_email = member.email
        removed_member_role = target_member_role.value if target_member_role else "unknown"
        
        try:
            # 执行成员移除
            TenantService.remove_member_from_tenant(current_user.current_tenant, member, current_user)
            
            # 记录审计日志
            log_member_removal_operation(current_user, removed_member_email, removed_member_role)
            
        except services.errors.account.CannotOperateSelfError as e:
            return {"code": "cannot-operate-self", "message": str(e)}, 400
        except services.errors.account.NoPermissionError as e:
            return {"code": "forbidden", "message": str(e)}, 403
        except services.errors.account.MemberNotInTenantError as e:
            return {"code": "member-not-found", "message": str(e)}, 404
        except Exception as e:
            raise ValueError(str(e))

        return {"result": "success", "tenant_id": str(current_user.current_tenant.id)}, 200
```

## 风险评估

- **严重性**: 中高
- **CVSS评分**: 7.8 (High)
- **影响范围**: 所有使用工作区成员移除功能的用户
- **利用难度**: 中等
- **检测难度**: 中等

## 结论

工作区成员移除机制中的权限绕过漏洞是一个严重的安全问题，可能导致未授权访问和服务中断。建议立即实施修复措施，特别是完善权限检查逻辑和实现移除审批机制。同时，建议实施长期修复措施，如审计日志和异常检测，以提高系统的整体安全性。

---
*报告生成时间: 2025-08-21 22:36:21*