import logging

class TokenTracker:
    def __init__(self):
        self.total_prompt_tokens = 0
        self.total_completion_tokens = 0
        self.total_tokens = 0
        self.model_name = ""

    def set_model_name(self, model_name):
        self.model_name = model_name

    def track_tokens(self, response):
        """Tracks tokens from AIMessage or a similar object with response_metadata or usage_metadata."""
        prompt_tokens = 0
        completion_tokens = 0

        if hasattr(response, 'response_metadata') and 'token_usage' in response.response_metadata:
            token_usage = response.response_metadata['token_usage']
            prompt_tokens = token_usage.get('prompt_tokens', 0)
            completion_tokens = token_usage.get('completion_tokens', 0)
        elif hasattr(response, 'usage_metadata') and response.usage_metadata is not None:
            usage_metadata = response.usage_metadata
            if usage_metadata is not None:  # Double check to prevent NoneType error
                prompt_tokens = usage_metadata.get('input_tokens', 0)
                completion_tokens = usage_metadata.get('output_tokens', 0)

        if prompt_tokens > 0 or completion_tokens > 0:
            self.total_prompt_tokens += prompt_tokens
            self.total_completion_tokens += completion_tokens
            self.total_tokens += prompt_tokens + completion_tokens

            logging.info(
                f"TOKEN_USAGE - Model: {self.model_name}, "
                f"Prompt: {prompt_tokens}, Completion: {completion_tokens}, "
                f"Total: {prompt_tokens + completion_tokens}"
            )

    def get_summary(self):
        return (
            f"\n--- Final Token Usage Summary ---\n"
            f"Model: {self.model_name}\n"
            f"Total Prompt Tokens: {self.total_prompt_tokens}\n"
            f"Total Completion Tokens: {self.total_completion_tokens}\n"
            f"Total Tokens Used: {self.total_tokens}\n"
            f"---------------------------------"
        )