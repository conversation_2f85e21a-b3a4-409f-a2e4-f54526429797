# 用户认证绕过漏洞报告

## 漏洞概述

在Dify的get_user_tenant装饰器实现中，存在一个严重的用户认证绕过漏洞。当user_id为空时，系统会自动将其设置为"DEFAULT-USER"，并创建一个匿名用户，允许访问所有使用该装饰器的内部API端点。结合固定的默认API密钥，攻击者可以轻易绕过正常的用户认证机制，访问敏感的内部API功能。

## 漏洞位置

- **主要文件**: `api/controllers/inner_api/plugin/wraps.py`
- **关键函数**: `get_user_tenant`装饰器和`get_user`函数
- **相关文件**: `api/controllers/inner_api/wraps.py`
- **配置文件**: `api/configs/feature/__init__.py`

## 漏洞分析

### 1. 自动用户设置漏洞

在`get_user_tenant`装饰器中（第65-66行），当`user_id`为空时，系统会自动将其设置为"DEFAULT-USER"，而不是拒绝请求：

```python
if not user_id:
    user_id = "DEFAULT-USER"
```

这种设计允许攻击者通过不提供`user_id`或提供空值来绕过用户认证。

### 2. 匿名用户创建漏洞

在`get_user`函数中（第24-35行），当`user_id`为"DEFAULT-USER"时，系统会创建一个匿名用户（`is_anonymous=True`）：

```python
if user_id == "DEFAULT-USER":
    user_model = session.query(EndUser).where(EndUser.session_id == "DEFAULT-USER").first()
    if not user_model:
        user_model = EndUser(
            tenant_id=tenant_id,
            type="service_api",
            is_anonymous=True if user_id == "DEFAULT-USER" else False,
            session_id=user_id,
        )
        session.add(user_model)
        session.commit()
        session.refresh(user_model)
```

这个匿名用户仍然可以访问所有使用`get_user_tenant`装饰器的API端点，没有额外的权限检查。

### 3. 固定API密钥漏洞

在`plugin_inner_api_only`装饰器中（第72-74行），系统通过检查请求头中的"X-Inner-Api-Key"是否与配置中的`INNER_API_KEY_FOR_PLUGIN`值匹配来验证访问权限：

```python
inner_api_key = request.headers.get("X-Inner-Api-Key")
if not inner_api_key or inner_api_key != dify_config.INNER_API_KEY_FOR_PLUGIN:
    abort(404)
```

然而，`INNER_API_KEY_FOR_PLUGIN`的默认值为"inner-api-key"（在`api/configs/feature/__init__.py`第165行），这是一个固定的、公开的默认值：

```python
INNER_API_KEY_FOR_PLUGIN: str = Field(description="Inner api key for plugin", default="inner-api-key")
```

攻击者可以轻易获取这个默认值，从而通过API密钥验证。

## 数据流分析

1. **攻击者发送请求**：攻击者向使用`get_user_tenant`装饰器的API端点发送请求，不提供`user_id`或提供空值。
2. **API密钥验证**：请求通过`plugin_inner_api_only`装饰器，因为攻击者在请求头中包含了"X-Inner-Api-Key: inner-api-key"。
3. **用户ID处理**：`get_user_tenant`装饰器检测到`user_id`为空，自动将其设置为"DEFAULT-USER"。
4. **匿名用户创建**：`get_user`函数检测到`user_id`为"DEFAULT-USER"，创建或获取一个匿名用户（`is_anonymous=True`）。
5. **API调用**：系统使用这个匿名用户调用API端点，没有额外的权限检查。
6. **响应返回**：API端点处理请求并返回响应，攻击者成功绕过了用户认证。

## 影响范围

所有使用`get_user_tenant`装饰器的API端点都受此漏洞影响，包括：

- LLM调用 (`/invoke/llm`)
- 文本嵌入 (`/invoke/text-embedding`)
- 重排序 (`/invoke/rerank`)
- 文本转语音 (`/invoke/tts`)
- 语音转文本 (`/invoke/speech2text`)
- 内容审查 (`/invoke/moderation`)
- 工具调用 (`/invoke/tool`)
- 应用调用 (`/invoke/app`)
- 加密解密 (`/invoke/encrypt`)
- 摘要生成 (`/invoke/summary`)
- 文件上传 (`/upload/file/request`)
- 应用信息获取 (`/fetch/app/info`)

这些API端点提供了系统的核心功能，包括AI模型调用、数据处理、文件操作等，攻击者可以利用这些功能进行未授权操作，可能导致数据泄露、资源滥用、服务中断等严重后果。

## 漏洞利用概念

### 漏洞利用条件

1. 攻击者知道`INNER_API_KEY_FOR_PLUGIN`的默认值为"inner-api-key"。
2. 攻击者可以访问内部API端点。
3. 目标系统使用默认的`INNER_API_KEY_FOR_PLUGIN`值。

### 漏洞利用步骤

1. **准备请求**：攻击者准备一个HTTP请求，目标为使用`get_user_tenant`装饰器的API端点，例如`/invoke/llm`。
2. **设置请求头**：在请求头中包含"X-Inner-Api-Key: inner-api-key"。
3. **设置请求体**：在请求体中不提供`user_id`或提供空值，但提供必要的`tenant_id`和其他参数。
4. **发送请求**：发送HTTP请求到目标API端点。
5. **接收响应**：系统会自动创建一个匿名用户并处理请求，攻击者接收响应。

### 漏洞利用示例

```bash
curl -X POST http://example.com/api/inner-api/plugin/invoke/llm \
  -H "Content-Type: application/json" \
  -H "X-Inner-Api-Key: inner-api-key" \
  -d '{
    "tenant_id": "tenant-123",
    "user_id": "",
    "provider": "openai",
    "model_type": "llm",
    "model": "gpt-3.5-turbo",
    "prompt_messages": [
      {
        "role": "user",
        "content": "Hello, world!"
      }
    ],
    "completion_params": {
      "temperature": 0.7,
      "max_tokens": 100
    }
  }'
```

在这个示例中，攻击者通过不提供`user_id`（或提供空值）并使用默认的API密钥，成功调用了LLM API，绕过了用户认证。

## 修复建议

### 1. 修改INNER_API_KEY_FOR_PLUGIN的默认值

将`INNER_API_KEY_FOR_PLUGIN`的默认值从"inner-api-key"修改为空字符串，强制用户在部署时设置自定义值：

```python
INNER_API_KEY_FOR_PLUGIN: str = Field(description="Inner api key for plugin", default="")
```

### 2. 强制验证user_id

修改`get_user_tenant`装饰器，不允许`user_id`为空或"DEFAULT-USER"：

```python
if not user_id:
    raise ValueError("user_id is required")

if user_id == "DEFAULT-USER":
    raise ValueError("user_id cannot be DEFAULT-USER")
```

### 3. 添加额外的认证机制

除了API密钥验证外，添加其他认证机制，如：

- **IP白名单**：只允许来自特定IP地址的请求访问内部API端点。
- **TLS客户端证书**：要求客户端提供有效的TLS证书。
- **请求签名**：要求请求包含基于共享密钥的签名，验证请求的完整性和来源。

### 4. 实现细粒度的权限控制

为不同的API端点定义不同的权限要求，例如：

- 为敏感API端点（如LLM调用、应用调用）添加额外的权限检查。
- 基于用户角色和权限限制API访问。
- 实现资源级别的权限控制，确保用户只能访问其有权限的资源。

### 5. 添加审计日志

记录所有内部API的访问日志，包括：

- 请求时间戳
- 请求来源IP
- 请求的用户ID（如果提供）
- 请求的API端点
- 请求参数
- 响应状态
- 错误信息（如果有）

实现实时监控和告警机制，当检测到异常访问模式时（如大量匿名用户请求、频繁的API调用等），立即发送告警通知。

## 风险评估

- **严重性**: 高
- **CVSS评分**: 9.1 (Critical)
- **影响范围**: 所有使用get_user_tenant装饰器的内部API端点
- **利用难度**: 低
- **检测难度**: 中等

## 结论

get_user_tenant装饰器中"DEFAULT-USER"默认用户的设计存在严重的安全风险，导致用户认证绕过漏洞。攻击者可以通过不提供`user_id`或提供空值，并使用默认的API密钥，绕过用户认证，访问所有使用该装饰器的内部API端点。建议立即采取修复措施，包括修改默认API密钥、强制验证user_id、添加额外的认证机制、实现细粒度的权限控制和添加审计日志，以提高系统的整体安全性。

---
*报告生成时间: 2025-08-21 23:49:57*