# 插件API用户认证绕过漏洞报告

## 漏洞概述

在Dify的插件API中，`get_user_tenant`装饰器存在严重的用户认证绕过漏洞。当`user_id`为空时，系统会自动将其设置为"DEFAULT-USER"，并创建一个匿名用户，而不进行任何额外的权限验证。结合固定的默认API密钥，攻击者可以轻易地绕过正常的用户认证机制，访问所有使用该装饰器的内部API端点。

## 漏洞位置

**文件**: `api/controllers/inner_api/plugin/wraps.py`

**函数**: `get_user_tenant` (第48-100行)

**关键代码**:
```python
def get_user_tenant(view: Optional[Callable] = None):
    def decorator(view_func):
        @wraps(view_func)
        def decorated_view(*args, **kwargs):
            # fetch json body
            parser = reqparse.RequestParser()
            parser.add_argument("tenant_id", type=str, required=True, location="json")
            parser.add_argument("user_id", type=str, required=True, location="json")

            kwargs = parser.parse_args()

            user_id = kwargs.get("user_id")
            tenant_id = kwargs.get("tenant_id")

            if not tenant_id:
                raise ValueError("tenant_id is required")

            if not user_id:  # 关键漏洞点：当user_id为空时，不拒绝请求而是设置默认值
                user_id = "DEFAULT-USER"

            del kwargs["tenant_id"]
            del kwargs["user_id"]

            try:
                tenant_model = (
                    db.session.query(Tenant)
                    .where(
                        Tenant.id == tenant_id,
                    )
                    .first()
                )
            except Exception:
                raise ValueError("tenant not found")

            if not tenant_model:
                raise ValueError("tenant not found")

            kwargs["tenant_model"] = tenant_model

            user = get_user(tenant_id, user_id)  # 传递可能为"DEFAULT-USER"的user_id
            kwargs["user_model"] = user

            current_app.login_manager._update_request_context_with_user(user)  # type: ignore
            user_logged_in.send(current_app._get_current_object(), user=_get_user())  # type: ignore

            return view_func(*args, **kwargs)

        return decorated_view

    if view is None:
        return decorator
    else:
        return decorator(view)
```

**辅助函数**: `get_user` (第18-45行)

**关键代码**:
```python
def get_user(tenant_id: str, user_id: str | None) -> Account | EndUser:
    try:
        with Session(db.engine) as session:
            if not user_id:
                user_id = "DEFAULT-USER"

            if user_id == "DEFAULT-USER":  # 关键漏洞点：使用"DEFAULT-USER"创建匿名用户
                user_model = session.query(EndUser).where(EndUser.session_id == "DEFAULT-USER").first()
                if not user_model:
                    user_model = EndUser(
                        tenant_id=tenant_id,
                        type="service_api",
                        is_anonymous=True if user_id == "DEFAULT-USER" else False,  # 设置为匿名用户
                        session_id=user_id,
                    )
                    session.add(user_model)
                    session.commit()
                    session.refresh(user_model)
            else:
                user_model = AccountService.load_user(user_id)
                if not user_model:
                    user_model = session.query(EndUser).where(EndUser.id == user_id).first()
                if not user_model:
                    raise ValueError("user not found")
    except Exception:
        raise ValueError("user not found")

    return user_model
```

**配置文件**: `api/configs/feature/__init__.py`

**关键配置**:
```python
INNER_API_KEY_FOR_PLUGIN: str = Field(description="Inner api key for plugin", default="inner-api-key")
```

## 漏洞分析

### 数据流分析

1. **攻击者发送请求**：攻击者向使用`get_user_tenant`装饰器的API端点发送请求，不提供`user_id`或提供空值。

2. **API密钥验证**：请求首先经过`plugin_inner_api_only`装饰器（在`api/controllers/inner_api/wraps.py`第65-78行），验证请求头中的"X-Inner-Api-Key"是否与配置中的`INNER_API_KEY_FOR_PLUGIN`值匹配。

3. **用户ID处理**：在`get_user_tenant`装饰器中，如果`user_id`为空，系统会自动将其设置为"DEFAULT-USER"，而不是拒绝请求。

4. **用户模型创建**：在`get_user`函数中，当`user_id`为"DEFAULT-USER"时，系统会创建一个`EndUser`类型的用户模型，设置`is_anonymous=True`，并将其作为当前用户。

5. **权限验证绕过**：系统没有对匿名用户进行额外的权限验证，允许其访问所有使用`get_user_tenant`装饰器的API端点。

### 漏洞根因

1. **自动用户设置漏洞**：当`user_id`为空时，系统会自动将其设置为"DEFAULT-USER"，而不是拒绝请求。这违反了安全设计的最小权限原则，应该拒绝无效的输入而不是自动提供默认值。

2. **匿名用户权限过大**：系统为"DEFAULT-USER"创建的匿名用户没有受到适当的权限限制，可以访问所有使用`get_user_tenant`装饰器的API端点。

3. **固定默认API密钥**：`INNER_API_KEY_FOR_PLUGIN`的默认值为"inner-api-key"，这是一个固定的、公开的默认值，攻击者可以轻易获取并用于访问受保护的API端点。

## 影响范围

所有使用`get_user_tenant`装饰器的API端点都受此漏洞影响，这些端点位于`api/controllers/inner_api/plugin/plugin.py`文件中，包括：

1. **PluginInvokeLLMApi** (`/invoke/llm`) - LLM调用
2. **PluginInvokeLLMWithStructuredOutputApi** (`/invoke/llm/structured-output`) - 结构化输出LLM调用
3. **PluginInvokeTextEmbeddingApi** (`/invoke/text-embedding`) - 文本嵌入
4. **PluginInvokeRerankApi** (`/invoke/rerank`) - 重排序
5. **PluginInvokeTTSApi** (`/invoke/tts`) - 文本转语音
6. **PluginInvokeSpeech2TextApi** (`/invoke/speech2text`) - 语音转文本
7. **PluginInvokeModerationApi** (`/invoke/moderation`) - 内容审查
8. **PluginInvokeToolApi** (`/invoke/tool`) - 工具调用
9. **PluginInvokeParameterExtractorNodeApi** (`/invoke/parameter-extractor`) - 参数提取节点
10. **PluginInvokeQuestionClassifierNodeApi** (`/invoke/question-classifier`) - 问题分类节点
11. **PluginInvokeAppApi** (`/invoke/app`) - 应用调用
12. **PluginInvokeEncryptApi** (`/invoke/encrypt`) - 加密解密
13. **PluginInvokeSummaryApi** (`/invoke/summary`) - 摘要生成
14. **PluginUploadFileRequestApi** (`/upload/file/request`) - 文件上传
15. **PluginFetchAppInfoApi** (`/fetch/app/info`) - 应用信息获取

## 漏洞利用

### 利用条件

1. 攻击者需要知道`INNER_API_KEY_FOR_PLUGIN`的默认值"inner-api-key"。
2. 攻击者需要能够访问Dify的API端点。
3. 目标系统使用的是默认的`INNER_API_KEY_FOR_PLUGIN`值。

### 利用步骤

1. **准备请求**：构造一个HTTP请求，目标为使用`get_user_tenant`装饰器的API端点，例如`/invoke/llm`。

2. **设置请求头**：在请求头中包含"X-Inner-Api-Key: inner-api-key"。

3. **设置请求体**：在请求体中提供`tenant_id`，但不提供`user_id`或提供空值。

4. **发送请求**：发送HTTP请求到目标端点。

### 利用示例

```bash
curl -X POST "https://dify.example.com/v1/plugins/invoke/llm" \
  -H "Content-Type: application/json" \
  -H "X-Inner-Api-Key: inner-api-key" \
  -d '{
    "tenant_id": "valid-tenant-id",
    "user_id": "",
    "inputs": {
      "query": "Hello, world!"
    },
    "response_mode": "blocking",
    "model": {
      "provider": "openai",
      "name": "gpt-3.5-turbo",
      "mode": "chat"
    }
  }'
```

### 预期结果

系统会接受请求，自动将`user_id`设置为"DEFAULT-USER"，创建一个匿名用户，并允许访问LLM调用功能。攻击者可以滥用此功能消耗计算资源、处理敏感内容或进行其他恶意操作。

## 修复建议

### 短期修复

1. **修改INNER_API_KEY_FOR_PLUGIN的默认值**：
   将`INNER_API_KEY_FOR_PLUGIN`的默认值从"inner-api-key"修改为空字符串，强制用户在部署时设置自定义值。
   
   ```python
   # 在 api/configs/feature/__init__.py 中
   INNER_API_KEY_FOR_PLUGIN: str = Field(description="Inner api key for plugin", default="")
   ```

2. **强制验证user_id**：
   修改`get_user_tenant`装饰器，当`user_id`为空时拒绝请求，而不是设置默认值。
   
   ```python
   # 在 api/controllers/inner_api/plugin/wraps.py 中
   if not user_id:
       raise ValueError("user_id is required")
   ```

3. **禁用匿名用户访问敏感API**：
   修改`get_user`函数，拒绝"DEFAULT-USER"访问敏感API端点。
   
   ```python
   # 在 api/controllers/inner_api/plugin/wraps.py 中
   if user_id == "DEFAULT-USER":
       raise ValueError("anonymous user is not allowed to access this API")
   ```

### 长期修复

1. **实现细粒度的权限控制**：
   为不同的API端点定义不同的权限要求，基于用户角色和资源所有权进行访问控制。

2. **添加额外的认证机制**：
   - IP白名单：限制只有特定IP地址可以访问内部API。
   - TLS客户端证书：要求客户端提供有效的TLS证书。
   - 请求签名：使用HMAC或其他签名算法验证请求的完整性。

3. **实现API密钥轮换机制**：
   定期更换`INNER_API_KEY_FOR_PLUGIN`的值，并提供安全的密钥管理界面。

4. **添加审计日志**：
   记录所有内部API的访问日志，包括请求时间、IP地址、用户ID和操作类型，实现实时监控和告警机制。

5. **实施速率限制**：
   对内部API的访问频率进行限制，防止滥用和拒绝服务攻击。

### 安全加固建议

1. **网络安全**：
   - 在网络层实施额外的安全措施，如防火墙规则、VPN访问等。
   - 将内部API部署在隔离的网络环境中，仅允许通过特定的网关访问。

2. **监控和告警**：
   - 实施实时监控，检测异常的API访问模式。
   - 设置告警机制，当检测到可疑活动时立即通知安全团队。

3. **安全测试**：
   - 定期进行安全测试，包括渗透测试和代码审计。
   - 实施自动化安全扫描，检测潜在的安全漏洞。

## 风险评估

- **严重性**: 高
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用默认INNER_API_KEY_FOR_PLUGIN值的插件内部API
- **利用难度**: 低
- **检测难度**: 中等

此漏洞可能导致未授权访问、资源滥用、数据泄露和权限提升等严重安全风险，建议立即采取修复措施。

---
*报告生成时间: 2025-08-21 23:51:21*