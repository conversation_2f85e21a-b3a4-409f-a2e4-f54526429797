# main.py

import asyncio
from pyexpat import model
import traceback
import json
import re
import logging
import os
import sys
import shutil
from typing import Optional
import httpx
from token_tracker import TokenTracker

token_tracker = TokenTracker()


# --- LangChain & LangGraph 核心库 ---
from langchain_core.messages import BaseMessage, AIMessage, ToolMessage
from langchain_core.tools import tool
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import create_react_agent
from pydantic import BaseModel, Field
from langchain_openai import ChatOpenAI
from langchain_deepseek import ChatDeepSeek
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain import hub
# --- 导入我们自己的模块化组件 ---
from task_manager import Task, TaskStackManager
from tools import all_base_tools # 从新的tools包中导入所有基础工具
from tools.history_tools import set_history_manager # 导入历史工具设置函数
from prompts import SUB_AGENT_PROMPT, MAIN_AGENT_PROMPT # 导入 prompts
# 导入MCP客户端管理器
from mcp_tools_loader import load_all_mcp_tools, cleanup_mcp_sessions
from history_manager import VectorizedHistoryManager

# --- 定义全局变量 ---
app = None
all_tools = []
history_manager = None # 在全局作用域中声明
llm = None # 在全局作用域中声明


# --- 初始化LLM ---
# glm

# base_url="https://open.bigmodel.cn/api/paas/v4/"
# api_key="11019a9be0bd453ca364829f852dd5f3.btCxpeyGRvBwHd5T"
# model_name = "GLM-4.5"

# base_url="https://api.ibsgss.website/v1"
# api_key = "sk-S65Ksdt55qdqvyChsrFJDMTRbQ5IaW3iUeyh9OCQNZvcWssT"
# model_name = "claude-opus-4-20250514-thinking"

#公益站
# base_url = "https://one-hub.passerbywtj.us.kg/v1"
# api_key="sk-8XER70xCYM7hPsV_i72ILZzKKbz3p7dSwxV1dEQKi9m90hEmrgjRCdNvOsk"
# model_name = "deepseek-ai/DeepSeek-R1-0528"

#代理站
# base_url = "http://124.220.37.159:8301/v1"
# api_key = "sk-0eOt5j4XhuBkSn9mBIZigspdqkSHhyvGENO7fEFkYqvcEAfC"
# model_name = "deepseek-ai/DeepSeek-R1-0528"

#4.1-mini
# base_url = "https://tbai.xin/v1"
# api_key = "sk-LOdjI1sxJ0r53TDnl7n8D4wWrGfyVhvkfKf0nzLXwnopCWlR"
# model_name = "gpt-4.1-mini"


base_url = "http://124.220.37.159:8301/v1"
api_key = "sk-0eOt5j4XhuBkSn9mBIZigspdqkSHhyvGENO7fEFkYqvcEAfC"
model_name = "glm-4.5"


# gemini
# base_url = "http://47.96.154.128:8420/proxy/gemini-paid/v1beta/openai"
# api_key = "sk-YM8tJ_0AMDOrL3Kvr6Js6SrTb1E0Xyuszf-E4b8IZ18Ox1y6"
# model_name = "gemini-2.5-pro"

# base_url = "http://47.96.154.128:8420/proxy/targon/v1"
# api_key = "sk-JsU3xCkS0LMNuHtzcoKhqW2t-ZY3L5PdQUNU_d_QSrEyBtZe"
# model_name = "Qwen/Qwen3-Coder-480B-A35B-Instruct-FP8"

# -- OPENAI --
# llm = ChatOpenAI(
#     model=model_name,
#     openai_api_key=api_key,
#     openai_api_base=base_url,
#     max_retries=20,
#     temperature=0.8,
#     streaming=True,
#     timeout=360
# )

# -- Gemini --

# gemini

# base_url = "http://47.96.154.128:8420/proxy/gemini-paid/v1beta"

# # api_key = "AIzaSyDVsrkuz3qWuv9I-bdeq9DpfRDkoklJf2w"
# api_key = "sk-YM8tJ_0AMDOrL3Kvr6Js6SrTb1E0Xyuszf-E4b8IZ18Ox1y6"
# model_name = "gemini-2.5-pro"

# llm = ChatGoogleGenerativeAI(  
#     model=model_name,  
#     temperature=0.8,  
#     google_api_key=api_key,  # 直接传递 API key
#     include_thoughts=True,
#     client_options={  
#         'api_endpoint': base_url,
#     }  
# )


# -- DeepSeek --

llm = ChatDeepSeek(
    model=model_name,
    api_key=api_key,
    api_base=base_url,
    max_retries=20,
    temperature=0.6,
    streaming=True,
    timeout=360
)


# --- 初始化历史管理器 ---
EMBEDDING_BASE_URL = "https://api.siliconflow.cn/v1"
EMBEDDING_API_KEY = "sk-zrpeouyilapveahhwtxoyuampujfplikgqjkauvjhztwasos"
EMBEDDING_MODEL_NAME = "BAAI/bge-m3"
CHROMA_PERSIST_DIR = "./chroma_db"  # 定义持久化目录

# --- 重排序模型配置 ---
RERANKER_MODEL = "BAAI/bge-reranker-v2-m3"
RERANKER_BASE_URL = "https://api.siliconflow.cn/v1/rerank"
RERANKER_API_KEY = "sk-zrpeouyilapveahhwtxoyuampujfplikgqjkauvjhztwasos"


project_path=r"C:\Users\<USER>\Desktop\test\dify-main"

# 基础工具现在从tools包中导入
base_tools = all_base_tools


from task_manager import TaskStackManager, Task # 导入 TaskStackManager 和 Task

# 初始化任务管理器
task_manager = TaskStackManager()


class NewTaskInput(BaseModel):
    task_description: str = Field(description="需要解决的任务，并且要给出明确的目标")
    Additional_information: Optional[str] = Field(default=None, description="需要额外补充的知识，因为子智能体和父智能体的记忆内容不互通")
@tool(args_schema=NewTaskInput)
async def NewTask(task_description: str, Additional_information: Optional[str] = None) -> str:
    """
    仅当一个任务极其复杂，需要多个步骤和多种工具协调才能完成时，才使用此工具创建子代理。
    **警告**: 对于可以通过单个工具（如 read_file, list_files, run_command）直接完成的简单操作，**严禁**使用此工具。
    错误用法：为了读取文件而创建一个新任务。正确用法：直接调用 read_file 工具。
    在你决定使用此工具前，请先自问：我是否已经尝试了所有其他工具来收集必要信息？
    """
    logging.info(f"NewTask called with: {task_description}")

    parent_task = task_manager.peek()
    new_task = Task(description=task_description, status="in_progress")

    if parent_task:
        task_manager.add_child_task(parent_task.task_id, new_task)
    else:
        task_manager.push(new_task)

    logging.info(f"🧠 正在为新任务 '{task_description[:50]}...' 检索历史经验...")
    # 直接使用history_manager而不是工具，因为这里是系统内部调用
    relevant_history = await history_manager.search_relevant_history(task_description)
    if not relevant_history:
        logging.info("📂 未找到相关历史记录。")

    instructions = f"""
    **--- 当前任务指令 ---**
    你要严格遵守系统提示词中的要求,后续如果指定子任务，内容不可以和历史任务相同或相似。
    1.  **目标项目根目录**: `{project_path}`.
    2.  **任务继承关系**:
        {task_manager.format_task_branch_info(task_manager.get_task_branch(new_task.task_id))}
    3.  **上级传递的关键信息**: {Additional_information if Additional_information else "无"}
    4.  **你需要完成的核心任务**: "{task_description}"
    请立即开始评估你的核心任务并行动。
    5.  **有关当前任务状态的历史信息**:
    {relevant_history if relevant_history else "无相关历史记录可供参考。"}
    """
    new_agent = create_react_agent(llm, tools=all_tools)
    prompt = [
        {"role": "system", "content": SUB_AGENT_PROMPT.format(
            model_name=model_name,
            task_description=task_description
        )},
        {"role": "user", "content": instructions}
    ]

    last_ai_response = ""
    max_retries = 3
    retry_delay = 5  # seconds

    for attempt in range(max_retries):
        try:
            interaction_history = []
            async for mode,chunk in new_agent.astream({"messages": prompt}, {"recursion_limit": 500000},stream_mode=["messages","updates"]):
                if mode == "messages":
                    token,_metedata = chunk
                    # print(token)
                    if isinstance(token, ToolMessage):
                        continue
                    if tool_calls := token.additional_kwargs.get("tool_calls"):
                        for tool_call in tool_calls:
                            # 'function' 键包含名称和参数
                            function_info = tool_call.get("function", {})
                            tool_name = function_info.get("name")
                            tool_args_str = function_info.get("arguments", "{}")
                            logging.info(f"🛠️ [子任务-工具调用]:\n调用 `{tool_name}`，参数: {tool_args_str}")
                        continue 
                    
                    # 处理 reasoning_content
                    if hasattr(token, 'additional_kwargs') and token.additional_kwargs and 'reasoning_content' in token.additional_kwargs:
                        reasoning_content = token.additional_kwargs['reasoning_content']
                        if reasoning_content:
                            print(reasoning_content, end="", flush=True)
                            with open("agent_log.log", "a", encoding="utf-8") as log_file:
                                log_file.write(reasoning_content)
                                log_file.flush()

                    if content := token.content:
                        if isinstance(content, list) and content:
                            content = content[0].get('thinking', '') if content else ''
                        print(content, end="", flush=True)
                        with open("agent_log.log", "a", encoding="utf-8") as log_file:
                            log_file.write(content)
                            log_file.flush()

                if mode == "updates":

                    interaction_history.append(chunk)
                    if "agent" in chunk and "messages" in chunk["agent"]:
                        for msg in chunk["agent"]["messages"]:
                            if isinstance(msg, AIMessage):
                                token_tracker.track_tokens(msg)
                                if msg.content:
                                    last_ai_response = msg.content
                                # if hasattr(msg, 'tool_calls') and msg.tool_calls:
                                #     for tool_call in msg.tool_calls:
                                #         logging.info(f"🛠️ [子任务-工具调用]:\n调用 `{tool_call['name']}`，参数: {tool_call['args']}")
                    if "tools" in chunk and "messages" in chunk["tools"]:
                        for output in chunk["tools"]["messages"]:
                            if isinstance(output, ToolMessage):
                                if len(output.content) > 2000: # 截断输出
                                    logging.info(f"✅ [子任务-工具输出]:\n{output.content[:2000]}\n...(省略后续字符)")
                                else:
                                    logging.info(f"✅ [子任务-工具输出]:\n{output.content}")

            await history_manager.add_history(last_ai_response)
            print()
            with open("agent_log.log", "a", encoding="utf-8") as log_file:
                log_file.write("\n")
                log_file.flush()
            task_manager.pop(status="completed")
            last_ai_response = "子任务执行结果：" + last_ai_response
            return last_ai_response  # Success, exit the retry loop

        except (httpx.ReadError, httpx.TimeoutException) as e:
            error_type = "网络读取错误" if isinstance(e, httpx.ReadError) else "网络请求超时"
            logging.warning(f"⚠️ {error_type} (尝试 {attempt + 1}/{max_retries}): {e}。将在 {retry_delay} 秒后重试...")
            if attempt + 1 == max_retries:
                logging.error("❌ 已达到最大重试次数，任务失败。")
                raise  # Re-raise the exception to be caught by the outer handler
            await asyncio.sleep(retry_delay)
            continue # Go to the next retry attempt

        except Exception as e:
            error_message = f"执行子任务 '{new_task.description}' 时失败: {e}"
            logging.error(f"❌ {error_message}")
            logging.error(traceback.format_exc())
            task_manager.pop(status="failed")
            replan_prompt = f"""
            **Subtask Execution Failed**
            - **Failed Task**: "{new_task.description}"
            - **Reason for Failure**: {str(e)}
            - **Parent Task Goal** (for reference): {parent_task.description if parent_task else "None"}
            **Action Instructions**:
            Primary agent, you must reevaluate the situation based on the failure information above. Please formulate a new, adjusted plan to solve or bypass this problem, or re-execute the task.
            You may choose:
            1.  **Modify and Retry**: Design a different approach to complete the original task.
            2.  **Break Down Task**: If the original task is too complex, break it down into smaller, more specific steps.
            3.  **Seek Clarification**: If the failure is due to missing information, you can ask questions to the higher level.
            **Please immediately analyze the reason for failure and output your new plan.**
            """
            return replan_prompt

    # This part is reached only if all retries fail for httpx.ReadError
    task_manager.pop(status="failed")
    return f"任务 '{new_task.description}' 因持续的网络错误而失败。"



def format_tools_list(tools):
    """格式化工具列表为用户友好的格式"""
    if not tools:
        return "子智能体当前没有可用的工具。"

    formatted_list = ["子智能体拥有的工具列表为: "]
    for i, tool in enumerate(tools, 1):
        tool_name = tool.name
        tool_description = tool.description.split('\n')[0] if tool.description else "无描述"
        formatted_list.append(f"{i}. `{tool_name}` - {tool_description}")

    return '\n'.join(formatted_list)

async def main():
    """主函数"""
    # 配置日志
    if os.path.exists("agent_log.log"):
        os.remove("agent_log.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler("agent_log.log", mode='a', encoding="utf-8"),
            logging.StreamHandler() # 同时输出到控制台
        ]
    )

    global app, all_tools, history_manager,token_tracker


    
    logging.info(f"✅ LLM ({model_name}) 初始化成功。")
    token_tracker.set_model_name(model_name)


    # --- 在初始化前，清理旧的数据库目录 ---
    if os.path.exists(CHROMA_PERSIST_DIR):
        logging.info(f"🧹 检测到旧的历史记录目录，正在清理 '{CHROMA_PERSIST_DIR}'...")
        shutil.rmtree(CHROMA_PERSIST_DIR)
        logging.info("✅ 旧的历史记录已成功清理。")

    history_manager = VectorizedHistoryManager(
        api_key=EMBEDDING_API_KEY,
        base_url=EMBEDDING_BASE_URL,
        model_name=EMBEDDING_MODEL_NAME,
        reranker_model=RERANKER_MODEL,
        reranker_base_url=RERANKER_BASE_URL,
        reranker_api_key=RERANKER_API_KEY,
        persist_directory=CHROMA_PERSIST_DIR # 传入持久化目录
    )

    # 设置历史管理器到工具中
    set_history_manager(history_manager)
    
    all_tools = base_tools.copy()
    all_tools.append(NewTask)  # 添加递归规划器工具

    try:
        mcp_tools = await load_all_mcp_tools()
        if mcp_tools:
            all_tools.extend(mcp_tools)
            logging.info(f"✅ 成功加载 {len(mcp_tools)} 个MCP工具") 
        else:
            logging.info("⚠️ 没有加载到MCP工具") 
    except Exception as e:
        logging.error(f"⚠️ MCP工具加载时发生错误: {e}") # 使用logging.error
    
    # 创建执行器代理
    # 主智能体包含所有基础工具，包括新的历史检索工具
    main_agent_tools = [tool for tool in all_base_tools if tool.name != "vulnerability_report" and tool.name != "read_file"]
    main_agent_tools.append(NewTask)
    main_agent = create_react_agent(llm, main_agent_tools)


    # --- 主交互和执行循环 ---
    logging.info("\n--- 自主智能体已准备就绪 ---")
    main_request = input("请输入您的总体任务目标: ")
    child_tools = format_tools_list(all_tools)
    try:
        prompt = [
            {"role": "system", "content": MAIN_AGENT_PROMPT.format(
                project_path=project_path,
                main_request=main_request,
                child_tools_list=child_tools
            )},
            {"role": "user", "content": f"""审计步骤参考信息：
                         1.使用`init_workspace`工具初始化目录`{project_path}`项目
                         2.判断项目框架
                         3.寻找入口点
                         4.逐个模块、逐个功能逐步的递归审计（需要考虑所有漏洞类型）

<final_goal>
{main_request}
</final_goal>"""},
        ]

        async for mode,chunk in main_agent.astream({"messages": prompt}, {"recursion_limit": 500000}, stream_mode=["messages","updates"]):
            if mode == "messages":
                token,_metedata = chunk
                # print(token)
                if isinstance(token, ToolMessage):
                    continue
                if tool_calls := token.additional_kwargs.get("tool_calls"):
                    for tool_call in tool_calls:
                        # 'function' 键包含名称和参数
                        function_info = tool_call.get("function", {})
                        tool_name = function_info.get("name")
                        tool_args_str = function_info.get("arguments", "{}")
                        logging.info(f"🛠️ [主任务-工具调用]:\n调用 `{tool_name}`，参数: {tool_args_str}")
                    continue 

                # 处理 reasoning_content
                if hasattr(token, 'additional_kwargs') and token.additional_kwargs and 'reasoning_content' in token.additional_kwargs:
                    reasoning_content = token.additional_kwargs['reasoning_content']
                    if reasoning_content:
                        print(reasoning_content, end="", flush=True)
                        with open("agent_log.log", "a", encoding="utf-8") as log_file:
                            log_file.write(reasoning_content)
                            log_file.flush()

                if content := token.content:
                    if isinstance(content, list) and content:
                        content = content[0].get('thinking', '') if content else ''
                    print(content, end="", flush=True)
                    with open("agent_log.log", "a", encoding="utf-8") as log_file:
                        log_file.write(content)
                        log_file.flush()

            if mode == "updates":
                # print(chunk)
                if 'agent' in chunk and 'messages' in chunk['agent']:
                    for msg in chunk['agent']['messages']:
                        if isinstance(msg, AIMessage):
                            token_tracker.track_tokens(msg)
                            # if msg.content:
                            #     logging.info(f"\n🔍 [主任务-AI思考/回应]: {msg.content}")

                            # 检查并以简洁格式打印工具调用
                            # if hasattr(msg, 'tool_calls') and msg.tool_calls:
                            #     for tool_call in msg.tool_calls:
                            #         logging.info(f"🛠️ [主任务-工具调用]:\n调用 `{tool_call['name']}`，参数: {tool_call['args']}")

                # 检查 'tools' key 并打印 ToolMessage
                if 'tools' in chunk and "messages" in chunk["tools"]:
                    for output in chunk["tools"]["messages"]:
                        if isinstance(output, ToolMessage):
                            if len(output.content) > 2000: # 截断输出
                                logging.info(f"✅ [主任务-工具输出]:\n{output.content[:2000]}\n...(省略后续字符)")
                            else:
                                logging.info(f"✅ [主任务-工具输出]:\n{output.content}")
        print()
        with open("agent_log.log", "a", encoding="utf-8") as log_file:
            log_file.write("\n")
            log_file.flush()

    except Exception as e:
        logging.error(f"\n❌ 执行任务时发生严重错误: {e}") # 使用logging.error
        traceback.print_exc()
    finally:
        logging.info("\n--- 流程执行完毕，正在清理所有异步资源 ---")
        await cleanup_mcp_sessions()
        logging.info(token_tracker.get_summary())
        logging.info("\n--- 所有资源已成功清理 ---")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断。")