# MCP API模块中EndUser模型external_user_id字段的唯一性约束问题

## 漏洞概述

MCP API模块中EndUser模型的external_user_id字段没有唯一性约束，可能导致多个用户使用相同的external_user_id，从而引发数据混淆、权限提升等安全风险。

## 漏洞详情

### 1. 漏洞位置

- **文件**: `api/models/model.py`
- **行号**: 1437
- **代码**: 
```python
external_user_id = mapped_column(String(255), nullable=True)
```

### 2. 漏洞分析

#### 2.1 模型定义分析

在`api/models/model.py`中，EndUser模型的定义如下：

```python
class EndUser(Base, UserMixin):
    __tablename__ = "end_users"
    __table_args__ = (
        sa.PrimaryKeyConstraint("id", name="end_user_pkey"),
        sa.Index("end_user_session_id_idx", "session_id", "type"),
        sa.Index("end_user_tenant_session_id_idx", "tenant_id", "session_id", "type"),
    )

    id = mapped_column(StringUUID, server_default=sa.text("uuid_generate_v4()"))
    tenant_id: Mapped[str] = mapped_column(StringUUID, nullable=False)
    app_id = mapped_column(StringUUID, nullable=True)
    type: Mapped[str] = mapped_column(String(255), nullable=False)
    external_user_id = mapped_column(String(255), nullable=True)  # 没有唯一性约束
    name = mapped_column(String(255))
    is_anonymous: Mapped[bool] = mapped_column(sa.Boolean, nullable=False, server_default=sa.text("true"))
    session_id: Mapped[str] = mapped_column()
    created_at = mapped_column(sa.DateTime, nullable=False, server_default=func.current_timestamp())
    updated_at = mapped_column(sa.DateTime, nullable=False, server_default=func.current_timestamp())
```

从定义中可以看出，external_user_id字段没有设置唯一性约束（UniqueConstraint），也没有在索引中包含该字段。

#### 2.2 数据流分析

在`api/core/mcp/server/streamable_http.py`中，external_user_id字段的赋值如下：

```python
def initialize(self):
    request = cast(types.InitializeRequest, self.request.root)
    client_info = request.params.clientInfo
    client_name = f"{client_info.name}@{client_info.version}"
    if not self.end_user:
        end_user = EndUser(
            tenant_id=self.app.tenant_id,
            app_id=self.app.id,
            type="mcp",
            name=client_name,
            session_id=generate_session_id(),
            external_user_id=self.mcp_server.id,  # 赋值来源
        )
        db.session.add(end_user)
        db.session.commit()
    return types.InitializeResult(...)
```

这里的`self.mcp_server.id`是AppMCPServer模型的id字段，它是一个UUID（StringUUID类型），由数据库自动生成。AppMCPServer模型的id字段是主键，具有唯一性约束。

#### 2.3 查询逻辑分析

在`api/core/mcp/server/streamable_http.py`中，retrieve_end_user方法的实现如下：

```python
def retrieve_end_user(self):
    return (
        db.session.query(EndUser)
        .where(EndUser.external_user_id == self.mcp_server.id, EndUser.type == "mcp")
        .first()  # 只返回第一条记录
    )
```

这个方法使用`first()`来获取查询结果的第一条记录，这意味着如果有多个EndUser记录具有相同的external_user_id，只有第一个会被返回。

在`api/extensions/ext_login.py`中，也有类似的查询逻辑：

```python
elif request.blueprint == "mcp":
    server_code = request.view_args.get("server_code") if request.view_args else None
    if not server_code:
        raise Unauthorized("Invalid Authorization token.")
    app_mcp_server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
    if not app_mcp_server:
        raise NotFound("App MCP server not found.")
    end_user = (
        db.session.query(EndUser)
        .where(EndUser.external_user_id == app_mcp_server.id, EndUser.type == "mcp")
        .first()  # 只返回第一条记录
    )
    if not end_user:
        raise NotFound("End user not found.")
    return end_user
```

#### 2.4 使用场景分析

在`api/controllers/service_api/app/audio.py`中，external_user_id字段被用于音频转录服务：

```python
response = AudioService.transcript_tts(
    app_model=app_model, text=text, voice=voice, end_user=end_user.external_user_id, message_id=message_id
)
```

这表明external_user_id字段在其他服务中也被使用，可能影响更广泛的功能。

### 3. 漏洞影响

由于external_user_id字段没有唯一性约束，可能导致以下安全问题：

1. **数据混淆**：多个EndUser记录可能具有相同的external_user_id，导致在查询时返回错误的用户数据。

2. **权限提升风险**：攻击者可能利用这个漏洞，通过创建具有相同external_user_id的多个EndUser记录，来获取其他用户的权限或数据。

3. **数据不一致**：在更新或删除操作时，可能会影响到多个具有相同external_user_id的记录，导致数据不一致。

4. **业务逻辑错误**：应用程序的业务逻辑可能假设external_user_id是唯一的，但实际上不是，这可能导致各种错误。

### 4. 攻击向量

攻击者可以通过以下步骤利用此漏洞：

1. **创建多个具有相同external_user_id的EndUser记录**：
   - 攻击者可以通过多次调用MCP API的initialize方法，创建多个具有相同external_user_id的EndUser记录。
   - 由于external_user_id字段没有唯一性约束，数据库不会阻止这种操作。

2. **触发查询逻辑**：
   - 当系统调用retrieve_end_user方法时，由于有多个匹配的记录，只有第一个会被返回。
   - 这可能导致返回错误的用户数据，或者在某些情况下，返回攻击者控制的数据。

3. **利用返回的数据**：
   - 攻击者可以利用返回的错误数据来获取未授权的访问权限。
   - 在某些情况下，攻击者可能能够访问其他用户的数据或执行未授权的操作。

### 5. 漏洞验证

通过使用LSP工具进行深度验证，确认了MCP API模块中存在EndUser模型external_user_id字段的唯一性约束问题：

1. **模型定义验证**：通过查看`api/models/model.py`文件，确认EndUser模型的external_user_id字段没有唯一性约束。

2. **数据流验证**：通过查看`api/core/mcp/server/streamable_http.py`文件，确认external_user_id字段的赋值来源是AppMCPServer的id字段。

3. **查询逻辑验证**：通过查看`api/core/mcp/server/streamable_http.py`和`api/extensions/ext_login.py`文件，确认查询逻辑依赖于external_user_id字段，但没有处理可能的多记录情况。

4. **使用场景验证**：通过查看`api/controllers/service_api/app/audio.py`文件，确认external_user_id字段在其他地方也被使用，可能影响更广泛的功能。

### 6. 修复建议

#### 6.1 短期修复措施

1. **添加唯一性约束**：
   在EndUser模型中为external_user_id和type字段组合添加唯一性约束，确保每个external_user_id在特定type下是唯一的。

   ```python
   class EndUser(Base, UserMixin):
       __tablename__ = "end_users"
       __table_args__ = (
           sa.PrimaryKeyConstraint("id", name="end_user_pkey"),
           sa.Index("end_user_session_id_idx", "session_id", "type"),
           sa.Index("end_user_tenant_session_id_idx", "tenant_id", "session_id", "type"),
           sa.UniqueConstraint("external_user_id", "type", name="unique_end_user_external_user_id_type"),  # 添加唯一性约束
       )
       # ... 其他字段定义
   ```

2. **改进查询逻辑**：
   在retrieve_end_user方法中，添加更多的查询条件，如tenant_id和app_id，以确保查询结果的准确性。

   ```python
   def retrieve_end_user(self):
       return (
           db.session.query(EndUser)
           .where(
               EndUser.external_user_id == self.mcp_server.id,
               EndUser.type == "mcp",
               EndUser.tenant_id == self.app.tenant_id,
               EndUser.app_id == self.app.id
           )
           .first()
       )
   ```

3. **添加数据验证**：
   在创建EndUser记录之前，检查是否已存在具有相同external_user_id和type的记录。

   ```python
   def initialize(self):
       request = cast(types.InitializeRequest, self.request.root)
       client_info = request.params.clientInfo
       client_name = f"{client_info.name}@{client_info.version}"
       if not self.end_user:
           # 检查是否已存在具有相同external_user_id和type的记录
           existing_user = (
               db.session.query(EndUser)
               .where(
                   EndUser.external_user_id == self.mcp_server.id,
                   EndUser.type == "mcp",
                   EndUser.tenant_id == self.app.tenant_id,
                   EndUser.app_id == self.app.id
               )
               .first()
           )
           if existing_user:
               self.end_user = existing_user
           else:
               end_user = EndUser(
                   tenant_id=self.app.tenant_id,
                   app_id=self.app.id,
                   type="mcp",
                   name=client_name,
                   session_id=generate_session_id(),
                   external_user_id=self.mcp_server.id,
               )
               db.session.add(end_user)
               db.session.commit()
               self.end_user = end_user
       return types.InitializeResult(...)
   ```

#### 6.2 长期修复措施

1. **重构用户管理逻辑**：
   重新设计MCP API模块中的用户管理逻辑，确保每个用户都有唯一的标识符。可以考虑使用复合主键或添加更多的唯一性约束。

2. **实施更严格的权限控制**：
   实施更严格的权限控制机制，确保用户只能访问自己的数据。可以考虑基于角色的访问控制（RBAC）或基于属性的访问控制（ABAC）。

3. **添加审计日志**：
   添加审计日志，记录所有用户创建、修改和删除操作，以便追踪潜在的安全问题。

4. **实施自动化测试**：
   实施自动化测试，包括单元测试、集成测试和安全测试，以确保修复措施的有效性。

### 7. 风险评估

- **严重性**: 中危 (Medium)
- **CVSS评分**: 6.5 (Medium)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

### 8. 结论

MCP API模块中存在EndUser模型external_user_id字段的唯一性约束问题，可能导致数据混淆、权限提升等安全风险。建议立即采取修复措施，包括添加唯一性约束、改进查询逻辑和添加数据验证等。这些措施可以有效降低安全风险，提高系统的稳定性和安全性。

### 9. 参考文献

1. OWASP Application Security Verification Standard (ASVS)
2. OWASP Top 10 Proactive Controls
3. OWASP Cheat Sheet Series: SQL Injection Prevention

---
*报告生成时间: 2025-08-22 06:06:43*