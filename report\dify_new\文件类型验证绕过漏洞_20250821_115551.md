# 文件类型验证绕过漏洞

## 漏洞描述

在 `services/file_service.py` 的 `upload_file` 方法中，文件类型验证仅基于文件扩展名，而不是文件内容。这可能导致攻击者通过修改文件扩展名绕过验证，上传恶意文件。

## 漏洞位置

- **文件**: `api/services/file_service.py`
- **方法**: `upload_file`
- **关键代码行**: 42-43, 52-53

## 漏洞代码分析

```python
# 获取文件扩展名（第42-43行）
extension = os.path.splitext(filename)[1].lstrip(".").lower()

# 仅当source为"datasets"时进行文件类型验证（第52-53行）
if source == "datasets" and extension not in DOCUMENT_EXTENSIONS:
    raise UnsupportedFileTypeError()
```

## 漏洞详情

### 1. 仅基于扩展名的验证

文件类型验证完全依赖于从文件名中提取的扩展名，而没有检查文件的实际内容（magic number 或文件头）。这意味着攻击者可以通过重命名文件来绕过验证。

```python
extension = os.path.splitext(filename)[1].lstrip(".").lower()
```

### 2. 条件性验证

只有当 `source == "datasets"` 时才会进行文件类型验证。对于其他来源的文件，完全跳过了文件类型验证。

```python
if source == "datasets" and extension not in DOCUMENT_EXTENSIONS:
    raise UnsupportedFileTypeError()
```

### 3. MIME 类型未验证

虽然方法接收了 `mimetype` 参数，但在验证过程中完全没有使用这个参数来验证文件的实际类型。

```python
def upload_file(
    *,
    filename: str,
    content: bytes,
    mimetype: str,  # 接收了mimetype参数但未使用
    user: Union[Account, EndUser, Any],
    source: Literal["datasets"] | None = None,
    source_url: str = "",
) -> UploadFile:
```

## 支持的文件类型

从 `constants/__init__.py` 文件中可以看到，支持的文件扩展名包括：
- 图片：jpg, jpeg, png, webp, gif, svg（及其大写形式）
- 视频：mp4, mov, mpeg, webm（及其大写形式）
- 音频：mp3, m4a, wav, amr, mpga（及其大写形式）
- 文档：根据 ETL_TYPE 配置不同而有所区别，但主要包括 txt, markdown, md, mdx, pdf, html, htm, xlsx, xls, docx, csv 等（及其大写形式）

## 漏洞利用方式

### 利用方式1：修改文件扩展名

攻击者可以将一个可执行文件（如 `.exe`）重命名为允许的扩展名（如 `.jpg` 或 `.pdf`），然后上传到服务器。

```python
# 恶意文件原名：malware.exe
# 修改后：malware.jpg
# 这样可以通过文件类型验证
```

### 利用方式2：绕过数据集文件类型限制

通过将 `source` 参数设置为非 "datasets" 值，可以完全绕过文件类型验证。

```python
# 正常上传（会验证文件类型）
FileService.upload_file(filename="malware.exe", content=..., mimetype=..., user=..., source="datasets")

# 恶意上传（跳过文件类型验证）
FileService.upload_file(filename="malware.exe", content=..., mimetype=..., user=..., source="other")
```

### 利用方式3：上传恶意文件

攻击者可以上传包含恶意代码的文件，这些文件可能在服务器上被执行或导致其他安全问题。

## 漏洞影响

1. **恶意文件上传**：攻击者可以上传包含恶意代码的文件，如 webshell、病毒或其他恶意软件。

2. **服务器安全风险**：如果上传的文件在服务器上被执行，可能导致服务器被入侵。

3. **数据泄露**：上传的恶意文件可能被用来窃取敏感数据。

4. **拒绝服务攻击**：上传大文件或特殊构造的文件可能导致服务器资源耗尽。

## 修复建议

### 1. 基于文件内容的验证

实现基于文件内容（magic number 或文件头）的验证，而不仅仅依赖文件扩展名。

```python
def get_file_type_from_content(content: bytes) -> str:
    """根据文件内容获取文件类型"""
    # 实现基于magic number的文件类型检测
    pass

# 在upload_file方法中使用
actual_file_type = get_file_type_from_content(content)
if actual_file_type not in ALLOWED_FILE_TYPES:
    raise UnsupportedFileTypeError()
```

### 2. 验证MIME类型

使用接收到的 `mimetype` 参数进行验证，确保它与文件扩展名和实际内容匹配。

```python
# 验证mimetype与扩展名是否匹配
if not is_mimetype_valid_for_extension(mimetype, extension):
    raise UnsupportedFileTypeError()
```

### 3. 移除条件性验证

无论 `source` 参数是什么值，都应该进行文件类型验证。

```python
# 移除source == "datasets"的条件
if extension not in ALLOWED_EXTENSIONS:
    raise UnsupportedFileTypeError()
```

### 4. 使用文件内容扫描

对上传的文件进行恶意软件扫描，特别是对于可执行文件类型。

```python
# 使用恶意软件扫描工具
if is_malicious_content(content):
    raise SecurityError("File contains malicious content")
```

## 结论

`services/file_service.py` 中的文件类型验证逻辑存在严重的安全漏洞，仅基于文件扩展名进行验证，并且只在特定条件下执行验证。这允许攻击者通过简单的重命名文件或修改请求参数来绕过验证，上传任意类型的文件，包括可能包含恶意代码的文件。建议尽快实施上述修复建议，加强文件上传安全性。

---
*报告生成时间: 2025-08-21 11:55:51*